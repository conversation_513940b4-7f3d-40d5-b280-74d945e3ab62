import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/presentation/pages/education/quiz_categories_page.dart';
import 'package:agriculture/presentation/bloc/education/education_cubit/education_cubit.dart';
import 'package:agriculture/presentation/pages/education/courses_page.dart';
import 'package:agriculture/presentation/widgets/education/daily_tips_page_widget.dart';
import 'package:agriculture/presentation/widgets/education/welcome_card.dart';
import 'package:agriculture/routing/route_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// صفحة التعليم والتدريب
///
/// توفر هذه الصفحة واجهة للوصول إلى محتوى التعليم والتدريب
/// مثل الدورات التدريبية والمقالات والاختبارات
class Education extends StatefulWidget {
  /// إنشاء صفحة التعليم والتدريب
  const Education({super.key});

  @override
  State<Education> createState() => _EducationState();
}

class _EducationState extends State<Education> with TickerProviderStateMixin {
  /// التحكم في التمرير
  final ScrollController _scrollController = ScrollController();

  /// التحكم في التبويبات
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // إنشاء التحكم في التبويبات (4 تبويبات)
    _tabController = TabController(length: 4, vsync: this);
    // تحميل البيانات عند بدء الصفحة
    _loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  void _loadData() {
    // تحميل بيانات التعليم

    // تحميل فئات التعليم
    context.read<EducationCubit>().loadEducationCategories();

    // تحميل الدورات من Firebase
    context.read<EducationCubit>().loadCourses();

    // تحميل النصيحة اليومية
    context.read<EducationCubit>().loadDailyTip();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FFFE),
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: AssetsColors.primary,
        elevation: 0,
        title: Text(
          'مركز التعلم الزراعي',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _loadData();
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ترحيب مذهل
              const WelcomeCard(),

              const SizedBox(height: 40),

              // عنوان الأقسام
              Text(
                'ابدأ رحلة التعلم',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.primary,
                ),
              ),

              const SizedBox(height: 8),

              Text(
                'اختر ما يناسبك من الخدمات التعليمية',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),

              const SizedBox(height: 32),

              _buildLargeSectionCard(
                title: 'الدورات التدريبية',
                subtitle: 'فيديوهات تعليمية متخصصة للمزارعين',
                icon: Icons.play_circle_filled,
                gradient: [const Color(0xFF6C63FF), const Color(0xFF5A52FF)],
                onTap: () => _showCoursesDetails(),
              ),

              const SizedBox(height: 20),

              // الأقسام الأربعة في بطاقات كبيرة عمودية
              _buildLargeSectionCard(
                title: 'نصيحة اليوم',
                subtitle: 'نصائح يومية وأسبوعية مفيدة للمزارعين',
                icon: Icons.lightbulb,
                gradient: [const Color(0xFF66BB6A), const Color(0xFF4CAF50)],
                onTap: () => _showDailyTipDetails(),
              ),

              const SizedBox(height: 20),

              _buildLargeSectionCard(
                title: 'المقالات الزراعية',
                subtitle: 'دليل شامل ومقالات مفيدة للمزارعين',
                icon: Icons.article,
                gradient: [const Color(0xFF4ECDC4), const Color(0xFF44A08D)],
                onTap: () => _showArticlesDetails(),
              ),

              const SizedBox(height: 20),

              _buildLargeSectionCard(
                title: 'اختبر نفسك',
                subtitle: 'اختبارات تفاعلية لقياس معرفتك',
                icon: Icons.quiz,
                gradient: [const Color(0xFF9B59B6), const Color(0xFF8E44AD)],
                onTap: () => _showQuizzesDetails(),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة قسم كبيرة ومذهلة مثل Agrinex و Brainie
  Widget _buildLargeSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(28),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: gradient[0].withValues(alpha: 0.4),
              blurRadius: 25,
              offset: const Offset(0, 12),
            ),
          ],
        ),
        child: Row(
          children: [
            // أيقونة كبيرة ومعبرة - 48 بكسل
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                size: 48, // حجم 48 بكسل كما طلبت
                color: Colors.white,
              ),
            ),

            const SizedBox(width: 24),

            // النصوص والمحتوى
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان الرئيسي
                  Text(
                    title,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                  ),

                  const SizedBox(height: 8),

                  // الوصف التفصيلي
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // سهم الانتقال الأنيق
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل النصيحة اليومية - الانتقال إلى صفحة منفصلة
  ///
  /// يقوم بالانتقال إلى صفحة النصائح الكاملة التي تعرض جميع النصائح المحلية اليمنية
  void _showDailyTipDetails() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const DailyTipsPageWidget()),
    );
  }

  /// عرض تفاصيل الدورات - الانتقال إلى صفحة منفصلة
  void _showCoursesDetails() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CoursesPage()),
    );
  }

  /// عرض تفاصيل المقالات - الانتقال إلى صفحة منفصلة
  void _showArticlesDetails() {
    Navigator.pushNamed(context, RouteConstants.articles);
  }

  /// عرض تفاصيل الاختبارات - الانتقال إلى صفحة الأقسام
  void _showQuizzesDetails() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const QuizCategoriesPage(),
      ),
    );
  }
}

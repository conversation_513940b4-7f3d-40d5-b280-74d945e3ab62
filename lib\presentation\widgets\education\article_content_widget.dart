import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:agriculture/data/models/education/article_model.dart';
import 'package:flutter/material.dart';

/// ويدجت محتوى المقالة
///
/// يعرض هذا الويدجت محتوى المقالة مع الملخص والنص الكامل
class ArticleContentWidget extends StatelessWidget {
  /// نموذج المقالة
  final ArticleModel article;

  /// إنشاء ويدجت محتوى المقالة
  ///
  /// المعلمات:
  /// - [article]: نموذج المقالة المراد عرض محتواها
  const ArticleContentWidget({
    super.key,
    required this.article,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ملخص المقالة
        _buildArticleSummary(context),

        const SizedBox(height: 24),

        // محتوى المقالة الكامل
        _buildArticleContent(context),
      ],
    );
  }

  /// بناء ملخص المقالة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  ///
  /// يعرض الملخص المختصر للمقالة في بطاقة مميزة
  Widget _buildArticleSummary(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AssetsColors.primary.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.primary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الملخص
          Row(
            children: [
              Icon(
                Icons.summarize,
                size: 20,
                color: AssetsColors.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'ملخص المقالة',
                style: TextStyles.of(context).headlineMedium(
                  fontSize: 16,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.primary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // نص المحتوى مباشرة (تم حذف الملخص من النموذج المبسط)
          Text(
            'محتوى المقالة الزراعية',
            style: TextStyles.of(context).bodyMedium(
              fontSize: 14,
              fontFamily: AssetsFonts.cairo,
              color: Colors.grey[700],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المقالة الكامل
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  ///
  /// يعرض المحتوى الكامل للمقالة باستخدام Markdown
  Widget _buildArticleContent(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المحتوى
          Row(
            children: [
              Icon(
                Icons.article,
                size: 20,
                color: AssetsColors.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'محتوى المقالة',
                style: TextStyles.of(context).headlineMedium(
                  fontSize: 18,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.primary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // المحتوى كنص عادي (مؤقتاً)
          Text(
            article.content,
            style: TextStyles.of(context).bodyMedium(
              fontSize: 16,
              fontFamily: AssetsFonts.cairo,
              color: Colors.grey[800],
              height: 1.8,
            ),
          ),
        ],
      ),
    );
  }

}

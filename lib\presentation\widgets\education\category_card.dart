import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/data/models/education/education_category_model.dart';
import 'package:agriculture/presentation/widgets/shared/cachd_net_image.dart';
import 'package:flutter/material.dart';

import '../../../core/constants/text_styles.dart';

/// بطاقة فئة التعليم
///
/// تعرض هذه البطاقة معلومات فئة التعليم
class EducationCategoryCard extends StatelessWidget {
  /// نموذج فئة التعليم
  final EducationCategoryModel category;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback onTap;

  /// إنشاء بطاقة فئة التعليم
  ///
  /// المعلمات:
  /// - [category]: نموذج فئة التعليم
  /// - [onTap]: دالة يتم استدعاؤها عند النقر على البطاقة
  const EducationCategoryCard({
    super.key,
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: AssetsColors.kWhite,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AssetsColors.primary.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          children: [
            // صورة الفئة
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              child: SizedBox(
                width: 100,
                height: 100,
                child: CachedNetImage(
                  imageUrl: category.imageUrl,
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // معلومات الفئة
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الفئة
                    Text(
                      category.name,
                      style: TextStyles.of(context).headlineMedium(
                        fontSize: 16,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.primary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // وصف الفئة
                    Text(
                      category.description,
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        color: AssetsColors.kGrey100,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 8),

                    // عدد العناصر
                    Row(
                      children: [
                        Icon(
                          Icons.library_books,
                          size: 16,
                          color: AssetsColors.primary.withValues(alpha: 0.7),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${category.itemsCount} عنصر',
                          style: TextStyles.of(context).bodySmall(
                            fontSize: 12,
                            fontFamily: AssetsFonts.cairo,
                            color: AssetsColors.kGrey100,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // سهم للانتقال
            Padding(
              padding: const EdgeInsets.all(16),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AssetsColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}


import 'package:agriculture/data/models/education/course_model.dart';
import 'package:agriculture/data/models/education/lesson_model.dart';

import 'package:agriculture/presentation/widgets/shared/error_message.dart';


import '../../../imports.dart';

/// صفحة تفاصيل الدورة التدريبية
///
/// تعرض هذه الصفحة تفاصيل دورة تدريبية محددة
class CourseDetailsPage extends StatefulWidget {
  /// معرف الدورة التدريبية
  final String courseId;

  /// إنشاء صفحة تفاصيل الدورة التدريبية
  ///
  /// المعلمات:
  /// - [courseId]: معرف الدورة التدريبية
  const CourseDetailsPage({super.key, required this.courseId});

  @override
  State<CourseDetailsPage> createState() => _CourseDetailsPageState();
}

class _CourseDetailsPageState extends State<CourseDetailsPage> {
  CourseModel? _currentCourse;
  List<LessonModel>? _currentLessons;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // تحميل تفاصيل الدورة عند بدء الصفحة
    _loadCourseDetails();
  }

  /// تحميل تفاصيل الدورة
  void _loadCourseDetails() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // التحقق من نوع الدورة وتحميلها بالطريقة الصحيحة
    if (widget.courseId.startsWith('ym-')) {
      // دورة يمنية - تحميل من البيانات المحلية
      _loadYemeniCourseDetails();
    } else {
      // دورة أدمن - تحميل من Firebase
      context.read<EducationCubit>().loadCourseDetails(widget.courseId);
    }
  }

  /// تحميل تفاصيل الدورة اليمنية من البيانات المحلية
  void _loadYemeniCourseDetails() {
    try {
      final yemeniCourses = YemenCoursesData.getCourses();
      final course = yemeniCourses.firstWhere(
        (course) => course.id == widget.courseId,
        orElse: () => throw Exception('لم يتم العثور على الدورة'),
      );

      setState(() {
        _currentCourse = course;
        _isLoading = false;
        _errorMessage = null;
      });

      // تحميل دروس الدورة اليمنية
      _loadYemeniCourseLessons();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'لم يتم العثور على الدورة المطلوبة';
      });
    }
  }

  /// تحميل دروس الدورة اليمنية من البيانات المحلية
  void _loadYemeniCourseLessons() {
    try {
      final lessons = YemenCoursesData.getCourseLessons(widget.courseId);
      setState(() {
        _currentLessons = lessons;
      });
    } catch (e) {
      // في حالة عدم وجود دروس، نتركها فارغة
      setState(() {
        _currentLessons = [];
      });
    }
  }

  /// تحميل دروس الدورة
  void _loadCourseLessons() {
    context.read<EducationCubit>().loadCourseLessons(widget.courseId);
  }

  /// معالجة تحديث الحالة (لدورات الأدمن فقط)
  void _handleStateUpdate(EducationState state) {
    // تجاهل التحديثات إذا كانت دورة يمنية (تم تحميلها محلي<|im_start|>)
    if (widget.courseId.startsWith('ym-')) {
      return;
    }

    if (state is EducationCourseDetailsLoaded) {
      setState(() {
        _currentCourse = state.course;
        _isLoading = false;
        _errorMessage = null;
      });
      // تحميل الدروس بعد تحميل تفاصيل الدورة
      _loadCourseLessons();
    } else if (state is EducationCourseLessonsLoaded) {
      // التأكد من أن الدروس تخص نفس الدورة
      if (state.courseId == widget.courseId) {
        // التحقق من ربط الدروس بالدورة
        final validLessons = state.lessons.where((lesson) => lesson.courseId == widget.courseId).toList();

        setState(() {
          _currentLessons = validLessons;
        });

        // طباعة معلومات للتصحيح
        LoggerService.debug('تم تحميل ${validLessons.length} درس صحيح للدورة ${widget.courseId}');
        for (var lesson in validLessons) {
          LoggerService.debug('درس: ${lesson.title} - الترتيب: ${lesson.order} - معرف الدورة: ${lesson.courseId}');
        }

        // تحذير إذا كان هناك دروس غير مرتبطة بالدورة
        final invalidLessons = state.lessons.where((lesson) => lesson.courseId != widget.courseId).toList();
        if (invalidLessons.isNotEmpty) {
          LoggerService.debug('تحذير: وجدت ${invalidLessons.length} دروس غير مرتبطة بالدورة ${widget.courseId}');
          for (var lesson in invalidLessons) {
            LoggerService.debug('درس غير صحيح: ${lesson.title} - معرف الدورة: ${lesson.courseId}');
          }
        }
      }
    } else if (state is EducationError) {
      setState(() {
        _isLoading = false;
        _errorMessage = state.message;
      });
      LoggerService.debug('خطأ في تحميل بيانات الدورة: ${state.message}');
    } else if (state is EducationLoading) {
      // لا نغير حالة التحميل إذا كانت لدينا بيانات بالفعل
      if (_currentCourse == null) {
        setState(() {
          _isLoading = true;
          _errorMessage = null;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: defaultAppBar(
        color: AssetsColors.primary,
        context: context,
        titel: 'تفاصيل الدورة',
      ),
      body: BlocListener<EducationCubit, EducationState>(
        listener: (context, state) {
          _handleStateUpdate(state);
        },
        child: _buildBody(),
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    // حالة التحميل
    if (_isLoading) {
      return const Center(child: CustomLoadingAnimation());
    }

    // حالة الخطأ
    if (_errorMessage != null) {
      return ErrorMessage(
        message: _errorMessage!,
        onRetry: () {
          _loadCourseDetails();
        },
      );
    }

    // حالة عرض البيانات
    if (_currentCourse != null) {
      if (_currentLessons != null) {
        // عرض الدورة مع الدروس
        return _buildCourseDetailsWithLessons(_currentCourse!, _currentLessons!);
      } else {
        // عرض الدورة مع تحميل الدروس
        return _buildCourseDetails(_currentCourse!);
      }
    }

    // حالة افتراضية
    return const Center(child: Text('لا توجد بيانات متاحة'));
  }

  /// بناء تفاصيل الدورة
  Widget _buildCourseDetails(CourseModel course) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة الدورة
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              course.imageUrl,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: double.infinity,
                  height: 200,
                  color: AssetsColors.kGrey50,
                  child: const Icon(
                    Icons.image_not_supported,
                    size: 50,
                    color: AssetsColors.kGrey100,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // عنوان الدورة
          Text(
            course.title,
            style: TextStyles.of(context).headlineMedium(
              fontSize: 20,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),

          const SizedBox(height: 8),

          // وصف الدورة
          Text(
            course.description,
            style: TextStyles.of(context).bodyMedium(
              fontSize: 16,
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kGrey100,
            ),
          ),

          const SizedBox(height: 16),

          // معلومات الدورة
          _buildInfoCard(context, course),

          const SizedBox(height: 24),

          // عنوان قائمة الدروس
          Text(
            'محتوى الدورة',
            style: TextStyles.of(context).headlineMedium(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),

          const SizedBox(height: 8),

          // رسالة تحميل الدروس
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  CustomLoadingAnimation(),
                  SizedBox(height: 8),
                  Text(
                    'جاري تحميل دروس الدورة...',
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 14,
                      color: AssetsColors.kGrey70,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تفاصيل الدورة مع الدروس
  Widget _buildCourseDetailsWithLessons(
    CourseModel course,
    List<LessonModel> lessons,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة الدورة
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              course.imageUrl,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: double.infinity,
                  height: 200,
                  color: AssetsColors.kGrey50,
                  child: const Icon(
                    Icons.image_not_supported,
                    size: 50,
                    color: AssetsColors.kGrey100,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // عنوان الدورة
          Text(
            course.title,
            style: TextStyles.of(context).headlineMedium(
              fontSize: 20,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),

          const SizedBox(height: 8),

          // وصف الدورة
          Text(
            course.description,
            style: TextStyles.of(context).bodyMedium(
              fontSize: 16,
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kGrey100,
            ),
          ),

          const SizedBox(height: 16),

          // معلومات الدورة
          _buildInfoCard(context, course),

          const SizedBox(height: 24),

          // عنوان قائمة الدروس
          Text(
            'محتوى الدورة',
            style: TextStyles.of(context).headlineMedium(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),

          const SizedBox(height: 8),

          // قائمة الدروس
          _buildLessonsListWithData(lessons),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات الدورة
  Widget _buildInfoCard(BuildContext context, CourseModel course) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.kWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.primary.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // المدرب
          _buildInfoRow(context, Icons.person, 'المدرب', course.instructorName),

          const SizedBox(height: 12),

          // المستوى
          _buildInfoRow(
            context,
            Icons.signal_cellular_alt,
            'المستوى',
            course.level,
          ),

          const SizedBox(height: 12),

          // المدة
          _buildInfoRow(
            context,
            Icons.access_time,
            'المدة',
            _formatDuration(course.durationMinutes),
          ),

          const SizedBox(height: 12),

          // عدد الدروس
          _buildInfoRow(
            context,
            Icons.video_library,
            'عدد الدروس',
            '${course.lessonsCount} درس',
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(icon, size: 20, color: AssetsColors.primary),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyles.of(context).bodyMedium(
            fontSize: 14,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kGrey100,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyles.of(context).bodyMedium(
            fontSize: 14,
            fontFamily: AssetsFonts.cairo,
            color: AssetsColors.kGrey100,
          ),
        ),
      ],
    );
  }

  /// بناء قائمة الدروس مع البيانات
  Widget _buildLessonsListWithData(List<LessonModel> lessons) {
    if (lessons.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('لا توجد دروس متاحة لهذه الدورة'),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: lessons.length,
      itemBuilder: (context, index) {
        final lesson = lessons[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AssetsColors.primary,
              child: Text(
                '${lesson.order}',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kWhite,
                ),
              ),
            ),
            title: Text(
              lesson.title,
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
            ),
            subtitle: Text(
              '${lesson.duration} دقيقة',
              style: TextStyles.of(context).bodySmall(
                fontSize: 12,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey100,
              ),
            ),
            trailing: Icon(
              Icons.play_circle_outline,
              color: AssetsColors.primary,
            ),
            onTap: () {
              // الانتقال إلى صفحة عرض فيديو الدرس
              Navigator.pushNamed(
                context,
                RouteConstants.lessonVideo,
                arguments: {'lesson': lesson},
              );
            },
          ),
        );
      },
    );
  }

  /// تنسيق المدة
  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes دقيقة';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours ساعة';
      } else {
        return '$hours ساعة و $remainingMinutes دقيقة';
      }
    }
  }
}


import 'package:agriculture/presentation/bloc/admin/admin_users_cubit.dart';
import 'package:agriculture/presentation/bloc/admin/admin_users_state.dart';

import 'package:agriculture/presentation/widgets/shared/error_message.dart';


import '../../../imports.dart';

/// صفحة إدارة الدورات التدريبية
///
/// تعرض هذه الصفحة قائمة بجميع الدورات التدريبية
/// مع إمكانية إضافة وتعديل وحذف الدورات
class AdminCoursesPage extends StatefulWidget {
  /// إنشاء صفحة إدارة الدورات التدريبية
  const AdminCoursesPage({super.key});

  @override
  State<AdminCoursesPage> createState() => _AdminCoursesPageState();
}

class _AdminCoursesPageState extends State<AdminCoursesPage> {
  @override
  void initState() {
    super.initState();
    context.read<AdminUsersCubit>().loadCourses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: const AdminDrawer(),
      body: BlocBuilder<AdminUsersCubit, AdminUsersState>(
        builder: (context, state) {
          if (state is AdminCoursesLoading) {
            return const Center(child: CustomLoadingAnimation());
          } else if (state is AdminCoursesError) {
            return ErrorMessage(
              message: state.message,
              onRetry: () => context.read<AdminUsersCubit>().loadCourses(),
            );
          } else if (state is AdminCoursesLoaded) {
            return _buildCoursesList(context, state);
          } else {
            return const Center(child: CustomLoadingAnimation());
          }
        },
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة الدورات التدريبية',
        style: TextStyles.of(context).headlineMedium(
          fontSize: 20,
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
          color: AssetsColors.kWhite,
        ),
      ),
      backgroundColor: AssetsColors.primary,
      foregroundColor: AssetsColors.kWhite,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () => context.read<AdminUsersCubit>().loadCourses(),
          icon: const Icon(Icons.refresh),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  /// بناء قائمة الدورات
  Widget _buildCoursesList(BuildContext context, AdminCoursesLoaded state) {
    if (state.courses.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<AdminUsersCubit>().loadCourses();
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(state.courses.length),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: state.courses.length,
                itemBuilder: (context, index) {
                  final course = state.courses[index];
                  return AdminCourseCard(
                    course: course,
                    onEdit: () => _navigateToEditCourse(course.id),
                    onViewLessons: () => _navigateToCourseLessons(course.id, course.title),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس القائمة
  Widget _buildHeader(int coursesCount) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AssetsColors.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.school,
            color: AssetsColors.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي الدورات',
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 14,
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.kGrey70,
                  ),
                ),
                Text(
                  '$coursesCount دورة تدريبية',
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 18,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 80,
              color: AssetsColors.kGrey70,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد دورات تدريبية',
              style: TextStyles.of(context).headlineMedium(
                fontSize: 20,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة أول دورة تدريبية',
              style: TextStyles.of(context).bodyMedium(
                fontSize: 16,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _navigateToAddCourse,
              icon: const Icon(Icons.add),
              label: const Text(
                'إضافة دورة جديدة',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.primary,
                foregroundColor: AssetsColors.kWhite,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToAddCourse,
      backgroundColor: AssetsColors.primary,
      foregroundColor: AssetsColors.kWhite,
      icon: const Icon(Icons.add),
      label: const Text(
        'إضافة دورة',
        style: TextStyle(
          fontSize: 14,
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// الانتقال إلى صفحة إضافة دورة
  void _navigateToAddCourse() {
    Navigator.pushNamed(context, RouteConstants.adminCourseForm);
  }

  /// الانتقال إلى صفحة تعديل دورة
  void _navigateToEditCourse(String courseId) {
    Navigator.pushNamed(
      context,
      RouteConstants.adminCourseForm,
      arguments: {'courseId': courseId},
    );
  }

  /// الانتقال إلى صفحة دروس الدورة
  void _navigateToCourseLessons(String courseId, String courseTitle) {
    // طباعة معلومات مفصلة للتصحيح
    LoggerService.debug('🔥 AdminCoursesPage._navigateToCourseLessons() - بدء الانتقال:');
    LoggerService.debug('📚 معرف الدورة المرسل: "$courseId"');
    LoggerService.debug('📝 عنوان الدورة المرسل: "$courseTitle"');

    // التحقق من أن courseId ليس فارغ
    if (courseId.isEmpty) {
      LoggerService.debug('❌ خطأ حرج: معرف الدورة فارغ في صفحة الدورات!');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ: معرف الدورة مفقود'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final arguments = {
      'courseId': courseId,
      'courseTitle': courseTitle,
    };

    LoggerService.debug('📦 المعلمات المرسلة إلى صفحة الدروس: $arguments');

    Navigator.pushNamed(
      context,
      RouteConstants.adminCourseLessons,
      arguments: arguments,
    );
  }
}

import 'package:animate_do/animate_do.dart';


import '../../../imports.dart';

// ignore: must_be_immutable
class OnBoarding extends StatelessWidget {
  OnBoarding({
    required this.onBoardingModel,
    super.key,
  });
  OnBoardingModel onBoardingModel;
  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // صورة الشريحة مع تأثير حركي
        Expanded(
          flex: 3,
          child: FadeInDown(
            duration: Duration(milliseconds: 1200),
            child: Container(
              width: double.maxFinite,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.2),
                    blurRadius: 10,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Image(
                  width: double.maxFinite,
                  fit: BoxFit.cover,
                  image: AssetImage(
                    onBoardingModel.imageUrl,
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 20.0),

        // عنوان الشريحة مع تأثير حركي
        FadeInRight(
          duration: Duration(milliseconds: 800),
          delay: Duration(milliseconds: 300),
          child: Center(
            child: Text(
              onBoardingModel.title,
              style: TextStyles.of(context).headlineSmall(
                  fontWeight: FontWeight.w900,
                  fontFamily: 'ElMessiri',
                  fontSize: height * .03),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        SizedBox(height: 20.0),

        // نص الشريحة مع تأثير حركي
        FadeInUp(
          duration: Duration(milliseconds: 800),
          delay: Duration(milliseconds: 600),
          child: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                onBoardingModel.bodyTitle,
                style: TextStyles.of(context).headlineSmall(
                  fontFamily: 'ElMessiri',
                  fontSize: width * .04,
                  fontWeight: FontWeight.w900,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
        SizedBox(height: 30.0),
      ],
    );
  }
}

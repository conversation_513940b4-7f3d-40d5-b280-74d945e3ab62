import 'package:flutter/material.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/experts/agricultural_expert_model.dart';
import '../../../data/datasources/remote/firebase/expert_ratings_firebase_service.dart';

/// بطاقة خبير محسنة وفق توصيات المراجعة الشاملة
///
/// تصميم جديد يركز على تحسين تجربة المستخدم والتفاعل
/// مع إضافة ميزات بصرية وتفاعلية متقدمة
class EnhancedExpertCardWidget extends StatefulWidget {
  /// بيانات الخبير
  final AgriculturalExpertModel expert;

  /// هل الخبير في المفضلة
  final bool isFavorite;

  /// دالة استدعاء عند النقر على البطاقة
  final VoidCallback? onTap;

  /// دالة استدعاء عند النقر على الاتصال
  final VoidCallback? onCallPressed;

  /// دالة استدعاء عند النقر على الرسائل
  final VoidCallback? onMessagePressed;

  /// دالة استدعاء عند حجز موعد
  final VoidCallback? onBookAppointmentPressed;

  /// دالة استدعاء عند تقييم الخبير
  final VoidCallback? onRatingPressed;

  /// دالة استدعاء عند تبديل المفضلة
  final VoidCallback? onFavoriteToggled;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  const EnhancedExpertCardWidget({
    super.key,
    required this.expert,
    this.isFavorite = false,
    this.onTap,
    this.onCallPressed,
    this.onMessagePressed,
    this.onBookAppointmentPressed,
    this.onRatingPressed,
    this.onFavoriteToggled,
    this.showAnimations = true,
  });

  @override
  State<EnhancedExpertCardWidget> createState() =>
      _EnhancedExpertCardWidgetState();
}

class _EnhancedExpertCardWidgetState extends State<EnhancedExpertCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    if (widget.showAnimations) {
      _setupAnimations();
    }
  }

  void _setupAnimations() {
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _elevationAnimation = Tween<double>(begin: 4.0, end: 12.0).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    if (widget.showAnimations) {
      _hoverController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget card = GestureDetector(
      onTap: widget.onTap,
      onTapDown:
          widget.showAnimations ? (_) => _hoverController.forward() : null,
      onTapUp: widget.showAnimations ? (_) => _hoverController.reverse() : null,
      onTapCancel:
          widget.showAnimations ? () => _hoverController.reverse() : null,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: widget.showAnimations ? _elevationAnimation.value : 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildQuickStats(),
            _buildRealRatings(),
            _buildActionButtons(),
          ],
        ),
      ),
    );

    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _hoverController,
        builder: (context, child) {
          return Transform.scale(scale: _scaleAnimation.value, child: card);
        },
      );
    }

    return card;
  }

  /// بناء رأس البطاقة مع صورة الخبير ومعلوماته الأساسية
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildProfileImage(),
          const SizedBox(width: 12),
          Expanded(child: _buildExpertInfo()),
          _buildFavoriteButton(),
        ],
      ),
    );
  }

  /// بناء صورة الملف الشخصي مع مؤشر الحالة
  Widget _buildProfileImage() {
    return Stack(
      children: [
        CircleAvatar(
          radius: 30,
          backgroundColor: AppColors.surface,
          backgroundImage:
              widget.expert.hasProfileImage
                  ? NetworkImage(widget.expert.profileImage)
                  : null,
          child:
              !widget.expert.hasProfileImage
                  ? Icon(Icons.person, size: 30, color: AppColors.textSecondary)
                  : null,
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color:
                  widget.expert.isAvailable
                      ? AppColors.success
                      : AppColors.error,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء معلومات الخبير
  Widget _buildExpertInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.expert.name,
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Text(
          widget.expert.specialization,
          style: AppTextStyles.bodyMedium.copyWith(color: AppColors.primary),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.location_on, size: 14, color: AppColors.textSecondary),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                widget.expert.fullLocation,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء زر المفضلة
  Widget _buildFavoriteButton() {
    return IconButton(
      onPressed: widget.onFavoriteToggled,
      icon: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: Icon(
          widget.isFavorite ? Icons.favorite : Icons.favorite_border,
          key: ValueKey(widget.isFavorite),
          color: widget.isFavorite ? AppColors.error : AppColors.textSecondary,
        ),
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // _buildQuickStat(
          //   Icons.star,
          //   widget.expert.rating.toStringAsFixed(1),
          //   AppColors.warning,
          // ),
          const SizedBox(width: 16),
          _buildQuickStat(
            Icons.work_history,
            '${widget.expert.experienceYears} سنة',
            AppColors.info,
          ),
          const SizedBox(width: 100),
          _buildQuickStat(
            Icons.chat,
            '${widget.expert.consultationsCount}',
            AppColors.success,
          ),
          const Spacer(),
          _buildAvailabilityChip(),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية سريعة
  Widget _buildQuickStat(IconData icon, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          value,
          style: AppTextStyles.bodySmall.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// بناء شريحة حالة التوفر
  Widget _buildAvailabilityChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color:
            widget.expert.isAvailable
                ? AppColors.success.withValues(alpha: 0.1)
                : AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        widget.expert.isAvailable ? 'متاح' : 'مشغول',
        style: AppTextStyles.bodySmall.copyWith(
          color:
              widget.expert.isAvailable ? AppColors.success : AppColors.error,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// بناء التقييمات الحقيقية
  Widget _buildRealRatings() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: FutureBuilder<Map<String, dynamic>>(
        future: _loadExpertRatingData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const SizedBox(
              height: 20,
              child: Center(
                child: SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return _buildBasicRating();
          }

          final data = snapshot.data!;
          final averageRating = data['averageRating'] as double;
          final totalRatings = data['totalRatings'] as int;

          return _buildEnhancedRating(averageRating, totalRatings);
        },
      ),
    );
  }

  /// تحميل بيانات التقييم
  Future<Map<String, dynamic>> _loadExpertRatingData() async {
    try {
      final ratingsService = ExpertRatingsFirebaseService();
      final averageRating = await ratingsService.getExpertAverageRating(
        widget.expert.id,
      );
      final totalRatings = await ratingsService.getExpertRatingsCount(
        widget.expert.id,
      );

      return {'averageRating': averageRating, 'totalRatings': totalRatings};
    } catch (e) {
      throw Exception('فشل في تحميل التقييمات');
    }
  }

  /// بناء التقييم الأساسي (fallback)
  Widget _buildBasicRating() {
    return Row(
      children: [
        _buildStarRating(widget.expert.rating),
        const SizedBox(width: 8),
        Text(
          widget.expert.rating.toStringAsFixed(1),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.warning,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '(${widget.expert.consultationsCount})',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  /// بناء التقييم المحسن
  Widget _buildEnhancedRating(double averageRating, int totalRatings) {
    return Row(
      children: [
        _buildStarRating(averageRating),
        const SizedBox(width: 8),
        Text(
          averageRating.toStringAsFixed(1),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.warning,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          '($totalRatings تقييم)',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontSize: 10,
          ),
        ),
        if (averageRating > 4.0) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'ممتاز',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.success,
                fontSize: 9,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء نجوم التقييم
  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor()
              ? Icons.star
              : index < rating
              ? Icons.star_half
              : Icons.star_border,
          color: AppColors.warning,
          size: 14,
        );
      }),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // الصف الأول من الأزرار
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.phone,
                  label: 'اتصال',
                  color: AppColors.success,
                  onPressed: widget.onCallPressed,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.message,
                  label: 'رسالة',
                  color: AppColors.info,
                  onPressed: widget.onMessagePressed,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // الصف الثاني من الأزرار
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.calendar_today,
                  label: 'موعد',
                  color: AppColors.warning,
                  onPressed: widget.onBookAppointmentPressed,
                  enabled: widget.expert.isAvailable,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.star,
                  label: 'تقييم',
                  color: AppColors.primary,
                  onPressed: widget.onRatingPressed,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onPressed,
    bool enabled = true,
  }) {
    return ElevatedButton.icon(
      onPressed: enabled ? onPressed : null,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: enabled ? color : AppColors.surface,
        foregroundColor: enabled ? Colors.white : AppColors.textSecondary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(vertical: 8),
      ),
    );
  }
}

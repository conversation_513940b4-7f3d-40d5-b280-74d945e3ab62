import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/app_constants.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/presentation/bloc/community_forum/posts_cubit.dart';
import 'confirmation_dialog.dart';

/// حوار حذف منشور
///
/// يستخدم هذا الحوار لتأكيد حذف منشور من المنتدى المجتمعي.
class DeletePostDialog {
  /// عرض حوار حذف المنشور
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [postId]: معرف المنشور المراد حذفه
  /// - [userId]: معرف المستخدم الذي يحاول حذف المنشور
  static Future<void> show({
    required BuildContext context,
    required String postId,
    required String userId,
  }) async {
    return ConfirmationDialog.show(
      context: context,
      title: 'حذف المنشور',
      content: '${AppConstants.confirmDeletePost}\nسيتم حذف جميع الصور والفيديوهات والتعليقات المرتبطة به.',
      confirmButtonText: 'حذف',
      cancelButtonText: 'إلغاء',
      confirmButtonColor: Colors.red,
      confirmButtonIcon: Icons.delete,
      onConfirm: () => _deletePost(context, postId, userId),
    );
  }

  /// حذف المنشور
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [postId]: معرف المنشور المراد حذفه
  /// - [userId]: معرف المستخدم الذي يحاول حذف المنشور
  static Future<void> _deletePost(
    BuildContext context,
    String postId,
    String userId,
  ) async {
    try {
      // عرض مؤشر التحميل
      final scaffold = ScaffoldMessenger.of(context);
      final loadingSnackBar = SnackBar(
        content: Row(
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 2.0,
            ),
            const SizedBox(width: 16),
            Text('جاري حذف المنشور...'),
          ],
        ),
        duration: const Duration(seconds: 60), // مدة طويلة لأننا سنغلقها يدويًا
        backgroundColor: Colors.black87,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      );

      scaffold.showSnackBar(loadingSnackBar);

      // حذف المنشور
      final success = await context.read<PostsCubit>().deletePost(postId, userId);

      // إغلاق مؤشر التحميل
      scaffold.hideCurrentSnackBar();

      // عرض رسالة النجاح أو الفشل
      scaffold.showSnackBar(
        SnackBar(
          content: Text(
            success ? AppConstants.successPostDelete : 'فشل في حذف المنشور',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          action: success
              ? null
              : SnackBarAction(
                  label: 'إعادة المحاولة',
                  textColor: Colors.white,
                  onPressed: () => show(
                    context: context,
                    postId: postId,
                    userId: userId,
                  ),
                ),
        ),
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف المنشور',
        error: e,
        tag: 'DeletePostDialog',
      );

      // عرض رسالة الخطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppConstants.errorGeneral}\n${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        );
      }
    }
  }
}

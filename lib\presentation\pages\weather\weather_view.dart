
import 'package:jiffy/jiffy.dart';

import '../../../data/models/weather/weather_model.dart';
import '../../../imports.dart';
import '../../../core/constants/weather/index.dart';
import '../../widgets/weather/weather_loading_widget.dart';
import '../../widgets/weather/weather_error_widget.dart';
import '../../widgets/weather/weather_alerts_widget.dart';
import '../../bloc/weather/weather_alerts_cubit.dart';
import 'weather_settings_page.dart';

class WeatherView extends StatelessWidget {
  const WeatherView({super.key});

  @override
  Widget build(BuildContext context) {
    // تحميل بيانات الطقس عند بناء الويدجت
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LoggerService.info(
        'بدء تحميل بيانات الطقس من صفحة الطقس الرئيسية',
        tag: 'WeatherView',
      );
      try {
        context.read<WeatherCubit>().loadAllWeatherData();
      } catch (e) {
        LoggerService.error(
          'خطأ في تحميل بيانات الطقس',
          error: e,
          tag: 'WeatherView',
        );
      }
    });
    return Scaffold(
      backgroundColor: AssetsColors.primary,
      body: SafeArea(
        child: BlocConsumer<WeatherCubit, WeatherState>(
          listener: (BuildContext context, WeatherState state) {
            if (state is WeatherError) {
              // تحديد نوع الخطأ وعرض الرسالة المناسبة
              final errorType = _determineErrorType(state.message);

              // استخدام حوار الخطأ المحسن
              AnimatedErrorDialog.show(
                context: context,
                title: _getErrorTitle(errorType),
                message: state.message,
                onRetry:
                    () => context.read<WeatherCubit>().refreshWeatherData(),
              );
            }
          },
          builder: (context, state) {
            if (state is WeatherInitial || state is WeatherLoading) {
              // استخدام ويدجت التحميل المحسن
              return const WeatherLoadingWidget(
                loadingType: WeatherLoadingType.full,
              );
            }
            if (state is WeatherError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.cloud_off,
                      size: 64,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'تعذر تحميل بيانات الطقس',
                      style: TextStyles.of(context).bodyLarge(
                        fontSize: 18,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed:
                          () =>
                              context.read<WeatherCubit>().refreshWeatherData(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AssetsColors.wether2,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }
            // if (state is GetWeatherLoading) {
            //   return CircularProgress();
            // }
            if (state is WeatherLoaded) {
              WeatherModel weather = state.currentWeather;
              return Padding(
                padding: const EdgeInsets.only(left: 18.0, right: 18),
                // إضافة ميزة السحب للتحديث
                child: RefreshIndicator(
                  onRefresh:
                      () => context.read<WeatherCubit>().refreshWeatherData(),
                  color: AssetsColors.wether2,
                  backgroundColor: Colors.white,
                  displacement: 40,
                  strokeWidth: 3,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconAppBar(
                              backgroundColor: AssetsColors.wether2,
                              colorIcon: AssetsColors.kWhite,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) =>
                                            const WeatherSettingsPage(),
                                  ),
                                );
                              },
                              icon: Icons.settings,
                            ),
                            Text(
                              Jiffy.now().format(pattern: 'dd MMMM yyyy'),
                              style: TextStyles.of(context).bodyLarge(
                                fontSize: 17,
                                fontFamily: AssetsFonts.cairo,
                                fontWeight: FontWeight.bold,
                                color: AssetsColors.kWhite,
                              ),
                            ),
                            IconAppBar(
                              backgroundColor: AssetsColors.wether2,
                              colorIcon: AssetsColors.kWhite,
                              onTap: () {
                                // استخدام دالة refreshWeatherData بدلاً من loadAllWeatherData
                                // لضمان جلب بيانات محدثة من الخادم دائمًا
                                context
                                    .read<WeatherCubit>()
                                    .refreshWeatherData();
                              },
                              icon: Icons.refresh,
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        Container(
                          decoration: BoxDecoration(
                            color: AssetsColors.wether2,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              SizedBox(height: 20),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                spacing: 15,
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    color: AssetsColors.kWhite,
                                    size: 30,
                                  ),
                                  Text(
                                    weather.name != null
                                        ? weather.name.toString()
                                        : 'غير معروف',
                                    //cubit.weather.name,
                                    style: TextStyles.of(context).bodyLarge(
                                      fontSize: 18,
                                      fontFamily: AssetsFonts.cairo,
                                      // fontWeight: FontWeight.bold,
                                      color: AssetsColors.kWhite,
                                    ),
                                  ),
                                ],
                              ),
                              StackHomeWeather(
                                numberWeather:
                                    weather.main?.temp != null
                                        ? weather.main!.temp!.round().toString()
                                        : '0',
                                color: AssetsColors.kWhite,
                                imageAssets:
                                    'assets/icons/${weather.weather![0].icon!.replaceAll('n', 'd')}.png',
                              ),
                              Text(
                                weather.weather != null &&
                                        weather.weather!.isNotEmpty &&
                                        weather.weather![0].description != null
                                    ? weather.weather![0].description.toString()
                                    : 'سماء صافية',
                                style: TextStyles.of(context).bodyLarge(
                                  fontSize: 16,
                                  fontFamily: AssetsFonts.messiri,
                                  fontWeight: FontWeight.bold,
                                  color: AssetsColors.kWhite,
                                ),
                              ),
                              SizedBox(height: 20),
                              Padding(
                                padding: const EdgeInsets.only(bottom: 10.0),
                                child: RowWeather(
                                  temperature:
                                      weather.main?.temp != null
                                          ? weather.main!.temp!
                                              .round()
                                              .toString()
                                          : '0',
                                  humidity:
                                      weather.main?.humidity != null
                                          ? weather.main!.humidity.toString()
                                          : '0',
                                  windSpeed:
                                      weather.wind?.speed != null
                                          ? weather.wind!.speed!.toString()
                                          : '0',
                                  color: AssetsColors.wether2,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'اليوم',
                              style: TextStyles.of(context).bodyLarge(
                                fontSize: 17,
                                fontFamily: AssetsFonts.messiri,
                                fontWeight: FontWeight.bold,
                                color: AssetsColors.kWhite,
                              ),
                            ),
                            InkWell(
                              splashColor: Colors.red,
                              splashFactory: InkSplash.splashFactory,
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  RouteConstants.dayWeather,
                                );
                              },
                              child: Text(
                                'توقعات الطقس لـ 7 ايام',
                                style: TextStyles.of(context).bodyLarge(
                                  fontSize: 17,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: AssetsFonts.messiri,
                                  color: AssetsColors.kWhite,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        EnhancedHourlyForecastView(),
                        SizedBox(height: 20),

                        // التنبيهات الذكية
                        BlocProvider(
                          create:
                              (context) =>
                                  WeatherAlertsCubit()..analyzeWeatherData(
                                    currentWeather: state.currentWeather,
                                  ),
                          child: WeatherAlertsWidget(
                            compact: true,
                            maxAlerts: 3,
                          ),
                        ),
                        SizedBox(height: 20),

                        // زر الرسوم البيانية
                        _buildChartsButton(context),
                        SizedBox(height: 20),

                        // معلومات الطقس الزراعية
                        WeatherAgriculturalInfo(weather: weather),
                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              );
            }
            return const Center(child: Text('حدث خطأ غير متوقع'));
          },
        ),
      ),
    );
  }

  /// بناء زر الرسوم البيانية
  Widget _buildChartsButton(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: WeatherDimensions.cardPadding),
      child: ElevatedButton.icon(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const WeatherChartsPage()),
          );
        },
        icon: Icon(Icons.bar_chart, size: WeatherDimensions.mediumIconSize),
        label: Text(
          'عرض الرسوم البيانية التفاعلية',
          style: TextStyle(
            fontSize: WeatherDimensions.bodyFontSize,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: WeatherColors.primaryBackground,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(
            vertical: WeatherDimensions.mediumPadding,
            horizontal: WeatherDimensions.cardPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              WeatherDimensions.buttonBorderRadius,
            ),
          ),
          elevation: 4,
        ),
      ),
    );
  }

  /// تحديد نوع الخطأ من الرسالة
  WeatherErrorType _determineErrorType(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains('network') ||
        lowerMessage.contains('internet') ||
        lowerMessage.contains('connection')) {
      return WeatherErrorType.network;
    } else if (lowerMessage.contains('location') ||
        lowerMessage.contains('موقع')) {
      return WeatherErrorType.location;
    } else if (lowerMessage.contains('permission') ||
        lowerMessage.contains('إذن')) {
      return WeatherErrorType.permission;
    } else if (lowerMessage.contains('server') ||
        lowerMessage.contains('خادم')) {
      return WeatherErrorType.server;
    } else if (lowerMessage.contains('timeout') ||
        lowerMessage.contains('مهلة')) {
      return WeatherErrorType.timeout;
    }

    return WeatherErrorType.general;
  }

  /// الحصول على عنوان الخطأ
  String _getErrorTitle(WeatherErrorType errorType) {
    switch (errorType) {
      case WeatherErrorType.network:
        return 'مشكلة في الاتصال';
      case WeatherErrorType.location:
        return 'تعذر تحديد الموقع';
      case WeatherErrorType.permission:
        return 'إذن مطلوب';
      case WeatherErrorType.server:
        return 'خطأ في الخادم';
      case WeatherErrorType.timeout:
        return 'انتهت مهلة الاتصال';
      case WeatherErrorType.general:
        return 'خطأ في بيانات الطقس';
    }
  }
}

class IconAppBar extends StatelessWidget {
  const IconAppBar({
    super.key,
    required this.backgroundColor,
    required this.colorIcon,
    required this.onTap,
    required this.icon,
  });

  final Color backgroundColor;
  final GestureTapCallback onTap;
  final IconData icon;
  final Color colorIcon;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        child: Icon(icon, color: colorIcon, size: 30),
      ),
    );
  }
}

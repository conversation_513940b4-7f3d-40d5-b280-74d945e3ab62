import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';

/// صفحة التعلم التفاعلي للآفات والأمراض
///
/// تقدم محتوى تعليمي تفاعلي متنوع يشمل الفيديوهات والاختبارات
/// والألعاب التعليمية والمحاكاة ثلاثية الأبعاد
class InteractiveLearningPage extends StatefulWidget {
  const InteractiveLearningPage({super.key});

  @override
  State<InteractiveLearningPage> createState() =>
      _InteractiveLearningPageState();
}

class _InteractiveLearningPageState extends State<InteractiveLearningPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<LearningModule> _learningModules = [];
  List<InteractiveQuiz> _quizzes = [];
  List<EducationalGame> _games = [];
  List<LearningVideo> _videos = [];

  bool _isLoading = true;
  double _learningProgress = 0.0;

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _setupAnimations();
    _loadLearningContent();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _setupControllers() {
    _tabController = TabController(length: 4, vsync: this);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
  }

  Future<void> _loadLearningContent() async {
    // تحميل المحتوى التعليمي الحقيقي
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _learningModules = _generateLearningModules();
      _quizzes = _generateQuizzes();
      _games = _generateGames();
      _videos = _generateVideos();
      _learningProgress = 0.35; // تقدم المستخدم
      _isLoading = false;
    });

    _animationController.forward();
  }

  List<LearningModule> _generateLearningModules() {
    return [
      LearningModule(
        id: '1',
        title: 'أساسيات تشخيص الآفات والأمراض',
        description:
            'تعلم كيفية التعرف على الأعراض المختلفة للآفات والأمراض الزراعية',
        difficulty: LearningDifficulty.beginner,
        duration: 45,
        lessonsCount: 8,
        completedLessons: 3,
        topics: [
          'مقدمة في أمراض النباتات',
          'أنواع الآفات الزراعية',
          'طرق التشخيص البصري',
          'استخدام العدسة المكبرة',
          'تحليل الأعراض',
          'التوثيق والتسجيل',
          'الأخطاء الشائعة في التشخيص',
          'التطبيق العملي',
        ],
        imageUrl:
            'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
        isCompleted: false,
        rating: 4.7,
        studentsCount: 1250,
      ),
      LearningModule(
        id: '2',
        title: 'طرق المكافحة المتكاملة',
        description:
            'استراتيجيات شاملة لمكافحة الآفات والأمراض بطرق آمنة وفعالة',
        difficulty: LearningDifficulty.intermediate,
        duration: 60,
        lessonsCount: 10,
        completedLessons: 0,
        topics: [
          'مفهوم المكافحة المتكاملة',
          'المكافحة البيولوجية',
          'المكافحة الكيميائية الآمنة',
          'المكافحة الزراعية',
          'المكافحة الفيزيائية',
          'تقييم فعالية المكافحة',
          'إدارة مقاومة المبيدات',
          'الاقتصاديات والتكلفة',
          'التأثير البيئي',
          'دراسات حالة عملية',
        ],
        imageUrl:
            'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
        isCompleted: false,
        rating: 4.9,
        studentsCount: 890,
      ),
      LearningModule(
        id: '3',
        title: 'الوقاية والإدارة المستدامة',
        description: 'تطبيق مبادئ الزراعة المستدامة للوقاية من الآفات والأمراض',
        difficulty: LearningDifficulty.advanced,
        duration: 75,
        lessonsCount: 12,
        completedLessons: 0,
        topics: [
          'مبادئ الزراعة المستدامة',
          'تحسين صحة التربة',
          'إدارة التنوع البيولوجي',
          'الدورات الزراعية',
          'النباتات المصاحبة',
          'إدارة المياه',
          'التسميد المتوازن',
          'مراقبة النظام البيئي',
          'التكيف مع التغير المناخي',
          'التقنيات الحديثة',
          'الشهادات والمعايير',
          'التطبيق في المزرعة',
        ],
        imageUrl:
            'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        isCompleted: false,
        rating: 4.8,
        studentsCount: 567,
      ),
    ];
  }

  List<InteractiveQuiz> _generateQuizzes() {
    return [
      InteractiveQuiz(
        id: '1',
        title: 'اختبار تشخيص الآفات',
        description: 'اختبر معرفتك في تشخيص الآفات الحشرية الشائعة',
        questionsCount: 15,
        timeLimit: 20,
        difficulty: QuizDifficulty.easy,
        category: 'تشخيص',
        imageUrl:
            'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
        isCompleted: true,
        lastScore: 85,
        bestScore: 92,
        attemptsCount: 3,
      ),
      InteractiveQuiz(
        id: '2',
        title: 'اختبار أمراض النباتات',
        description: 'تحدي شامل حول أمراض النباتات وطرق علاجها',
        questionsCount: 20,
        timeLimit: 30,
        difficulty: QuizDifficulty.medium,
        category: 'أمراض',
        imageUrl:
            'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
        isCompleted: false,
        lastScore: 0,
        bestScore: 0,
        attemptsCount: 0,
      ),
      InteractiveQuiz(
        id: '3',
        title: 'اختبار المكافحة المتكاملة',
        description: 'اختبار متقدم حول استراتيجيات المكافحة المتكاملة',
        questionsCount: 25,
        timeLimit: 40,
        difficulty: QuizDifficulty.hard,
        category: 'مكافحة',
        imageUrl:
            'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
        isCompleted: false,
        lastScore: 0,
        bestScore: 0,
        attemptsCount: 0,
      ),
    ];
  }

  List<EducationalGame> _generateGames() {
    return [
      EducationalGame(
        id: '1',
        title: 'مطابقة الآفات',
        description: 'اربط كل آفة بالمحصول الذي تصيبه في هذه اللعبة التفاعلية',
        gameType: GameType.matching,
        difficulty: GameDifficulty.easy,
        estimatedTime: 10,
        imageUrl:
            'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
        isUnlocked: true,
        isCompleted: true,
        bestScore: 450,
        stars: 3,
      ),
      EducationalGame(
        id: '2',
        title: 'مزرعة الدفاع',
        description: 'احم مزرعتك من الآفات باستخدام طرق المكافحة المناسبة',
        gameType: GameType.strategy,
        difficulty: GameDifficulty.medium,
        estimatedTime: 20,
        imageUrl:
            'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        isUnlocked: true,
        isCompleted: false,
        bestScore: 0,
        stars: 0,
      ),
      EducationalGame(
        id: '3',
        title: 'محاكي المزرعة الذكية',
        description: 'أدر مزرعة افتراضية وطبق مبادئ الزراعة المستدامة',
        gameType: GameType.simulation,
        difficulty: GameDifficulty.hard,
        estimatedTime: 45,
        imageUrl:
            'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
        isUnlocked: false,
        isCompleted: false,
        bestScore: 0,
        stars: 0,
      ),
    ];
  }

  List<LearningVideo> _generateVideos() {
    return [
      LearningVideo(
        id: '1',
        title: 'كيفية تشخيص اللفحة المتأخرة',
        description:
            'فيديو تعليمي مفصل يوضح خطوات تشخيص اللفحة المتأخرة في الطماطم',
        duration: 8,
        category: 'تشخيص',
        instructor: 'د. أحمد الزراعي',
        thumbnailUrl:
            'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
        videoUrl:
            'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
        viewsCount: 2340,
        rating: 4.8,
        isWatched: true,
        watchProgress: 1.0,
      ),
      LearningVideo(
        id: '2',
        title: 'المكافحة البيولوجية للآفات',
        description: 'تعرف على طرق المكافحة البيولوجية الآمنة والفعالة',
        duration: 12,
        category: 'مكافحة',
        instructor: 'م. فاطمة الحكيمي',
        thumbnailUrl:
            'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
        videoUrl:
            'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
        viewsCount: 1890,
        rating: 4.9,
        isWatched: false,
        watchProgress: 0.0,
      ),
      LearningVideo(
        id: '3',
        title: 'إدارة مقاومة المبيدات',
        description: 'استراتيجيات منع وإدارة مقاومة الآفات للمبيدات',
        duration: 15,
        category: 'إدارة',
        instructor: 'د. محمد السالمي',
        thumbnailUrl:
            'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
        videoUrl:
            'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4',
        viewsCount: 1456,
        rating: 4.7,
        isWatched: false,
        watchProgress: 0.3,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'التعلم التفاعلي',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.leaderboard),
            onPressed: _showLeaderboard,
            tooltip: 'لوحة المتصدرين',
          ),
          IconButton(
            icon: Icon(Icons.emoji_events),
            onPressed: _showAchievements,
            tooltip: 'الإنجازات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
          tabs: [
            Tab(text: 'الدروس'),
            Tab(text: 'الاختبارات'),
            Tab(text: 'الألعاب'),
            Tab(text: 'الفيديوهات'),
          ],
        ),
      ),
      body:
          _isLoading
              ? _buildLoadingScreen()
              : FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    // شريط التقدم
                    _buildProgressHeader(),

                    // محتوى التبويبات
                    Expanded(
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            _buildLessonsTab(),
                            _buildQuizzesTab(),
                            _buildGamesTab(),
                            _buildVideosTab(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  /// بناء شاشة التحميل
  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AssetsColors.primary),
          ),
          const SizedBox(height: 20),
          Text(
            'جاري تحميل المحتوى التعليمي...',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس التقدم
  Widget _buildProgressHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AssetsColors.primary.withValues(alpha: 0.1),
                child: Icon(Icons.school, color: AssetsColors.primary),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تقدمك التعليمي',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${(_learningProgress * 100).toInt()}% مكتمل',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                'المستوى ${_calculateLevel()}',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.primary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          LinearProgressIndicator(
            value: _learningProgress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AssetsColors.primary),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  /// بناء تبويب الدروس
  Widget _buildLessonsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _learningModules.length,
      itemBuilder: (context, index) {
        final module = _learningModules[index];
        return _buildModuleCard(module);
      },
    );
  }

  /// بناء تبويب الاختبارات
  Widget _buildQuizzesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _quizzes.length,
      itemBuilder: (context, index) {
        final quiz = _quizzes[index];
        return _buildQuizCard(quiz);
      },
    );
  }

  /// بناء تبويب الألعاب
  Widget _buildGamesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _games.length,
      itemBuilder: (context, index) {
        final game = _games[index];
        return _buildGameCard(game);
      },
    );
  }

  /// بناء تبويب الفيديوهات
  Widget _buildVideosTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _videos.length,
      itemBuilder: (context, index) {
        final video = _videos[index];
        return _buildVideoCard(video);
      },
    );
  }

  /// بناء بطاقة وحدة تعليمية
  Widget _buildModuleCard(LearningModule module) {
    final progress = module.completedLessons / module.lessonsCount;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _startModule(module),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الوحدة
            ClipRRect(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              child: Stack(
                children: [
                  Image.network(
                    module.imageUrl,
                    height: 150,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 150,
                        color: Colors.grey.shade200,
                        child: Icon(Icons.image, size: 50, color: Colors.grey),
                      );
                    },
                  ),

                  // شارة المستوى
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getDifficultyColor(module.difficulty),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getDifficultyText(module.difficulty),
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // شارة الإكمال
                  if (module.isCompleted)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.check, color: Colors.white, size: 16),
                      ),
                    ),
                ],
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    module.title,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.kGrey100,
                    ),
                  ),

                  const SizedBox(height: 8),

                  Text(
                    module.description,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 13,
                      color: AssetsColors.kGrey70,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 12),

                  // معلومات الوحدة
                  Row(
                    children: [
                      _buildInfoChip(
                        Icons.schedule,
                        '${module.duration} دقيقة',
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(Icons.book, '${module.lessonsCount} درس'),
                      const SizedBox(width: 8),
                      _buildInfoChip(Icons.people, '${module.studentsCount}'),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // شريط التقدم
                  Row(
                    children: [
                      Expanded(
                        child: LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AssetsColors.primary,
                          ),
                          minHeight: 6,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AssetsColors.primary,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // التقييم
                  Row(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            module.rating.toString(),
                            style: TextStyle(
                              fontFamily: AssetsFonts.cairo,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),

                      const Spacer(),

                      Text(
                        module.completedLessons > 0
                            ? 'متابعة التعلم'
                            : 'بدء التعلم',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: AssetsColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة اختبار
  Widget _buildQuizCard(InteractiveQuiz quiz) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              quiz.isCompleted
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.2),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _startQuiz(quiz),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AssetsColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.quiz,
                      color: AssetsColors.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          quiz.title,
                          style: TextStyle(
                            fontFamily: AssetsFonts.cairo,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          quiz.category,
                          style: TextStyle(
                            fontFamily: AssetsFonts.cairo,
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (quiz.isCompleted)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${quiz.lastScore}%',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              Text(
                quiz.description,
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 13,
                  color: AssetsColors.kGrey70,
                  height: 1.4,
                ),
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  _buildInfoChip(Icons.help, '${quiz.questionsCount} سؤال'),
                  const SizedBox(width: 8),
                  _buildInfoChip(Icons.timer, '${quiz.timeLimit} دقيقة'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة لعبة
  Widget _buildGameCard(EducationalGame game) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: InkWell(
        onTap: game.isUnlocked ? () => _startGame(game) : null,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة اللعبة
            ClipRRect(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              child: Stack(
                children: [
                  Image.network(
                    game.imageUrl,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 120,
                        color: Colors.grey.shade200,
                        child: Icon(Icons.games, size: 40, color: Colors.grey),
                      );
                    },
                  ),

                  // قفل اللعبة
                  if (!game.isUnlocked)
                    Container(
                      height: 120,
                      color: Colors.black54,
                      child: Center(
                        child: Icon(Icons.lock, color: Colors.white, size: 32),
                      ),
                    ),

                  // النجوم
                  if (game.isCompleted)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Row(
                        children: List.generate(
                          3,
                          (index) => Icon(
                            index < game.stars ? Icons.star : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    game.title,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color:
                          game.isUnlocked ? AssetsColors.kGrey100 : Colors.grey,
                    ),
                  ),

                  const SizedBox(height: 8),

                  Text(
                    game.description,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 13,
                      color:
                          game.isUnlocked ? AssetsColors.kGrey70 : Colors.grey,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 12),

                  Row(
                    children: [
                      _buildInfoChip(
                        Icons.schedule,
                        '${game.estimatedTime} دقيقة',
                      ),

                      const Spacer(),

                      Text(
                        game.isUnlocked ? 'لعب الآن' : 'مقفل',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color:
                              game.isUnlocked
                                  ? AssetsColors.primary
                                  : Colors.grey,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة فيديو
  Widget _buildVideoCard(LearningVideo video) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _playVideo(video),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الفيديو
            ClipRRect(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              child: Stack(
                children: [
                  Image.network(
                    video.thumbnailUrl,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 120,
                        color: Colors.grey.shade200,
                        child: Icon(
                          Icons.video_library,
                          size: 40,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),

                  // زر التشغيل
                  Positioned.fill(
                    child: Container(
                      color: Colors.black26,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.play_arrow,
                            color: AssetsColors.primary,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // مدة الفيديو
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${video.duration} دقيقة',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // مؤشر المشاهدة
                  if (video.isWatched)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.check, color: Colors.white, size: 12),
                      ),
                    ),
                ],
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    video.title,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.kGrey100,
                    ),
                  ),

                  const SizedBox(height: 8),

                  Text(
                    video.description,
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 13,
                      color: AssetsColors.kGrey70,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Text(
                        video.instructor,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: AssetsColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const Spacer(),

                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 14),
                          const SizedBox(width: 4),
                          Text(
                            video.rating.toString(),
                            style: TextStyle(
                              fontFamily: AssetsFonts.cairo,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // شريط التقدم
                  if (video.watchProgress > 0)
                    Column(
                      children: [
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: video.watchProgress,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AssetsColors.primary,
                          ),
                          minHeight: 4,
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رقاقة معلومات
  Widget _buildInfoChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: Colors.grey.shade600),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// حساب المستوى
  int _calculateLevel() {
    return ((_learningProgress * 10) + 1).toInt();
  }

  /// الحصول على لون الصعوبة
  Color _getDifficultyColor(LearningDifficulty difficulty) {
    switch (difficulty) {
      case LearningDifficulty.beginner:
        return Colors.green;
      case LearningDifficulty.intermediate:
        return Colors.orange;
      case LearningDifficulty.advanced:
        return Colors.red;
    }
  }

  /// الحصول على نص الصعوبة
  String _getDifficultyText(LearningDifficulty difficulty) {
    switch (difficulty) {
      case LearningDifficulty.beginner:
        return 'مبتدئ';
      case LearningDifficulty.intermediate:
        return 'متوسط';
      case LearningDifficulty.advanced:
        return 'متقدم';
    }
  }

  /// بدء وحدة تعليمية
  void _startModule(LearningModule module) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'بدء الوحدة التعليمية: ${module.title}',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// بدء اختبار
  void _startQuiz(InteractiveQuiz quiz) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'بدء الاختبار: ${quiz.title}',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// بدء لعبة
  void _startGame(EducationalGame game) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'بدء اللعبة: ${game.title}',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// تشغيل فيديو
  void _playVideo(LearningVideo video) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تشغيل الفيديو: ${video.title}',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض لوحة المتصدرين
  void _showLeaderboard() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'لوحة المتصدرين قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض الإنجازات
  void _showAchievements() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'صفحة الإنجازات قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }
}

/// نموذج الوحدة التعليمية
class LearningModule {
  final String id;
  final String title;
  final String description;
  final LearningDifficulty difficulty;
  final int duration;
  final int lessonsCount;
  final int completedLessons;
  final List<String> topics;
  final String imageUrl;
  final bool isCompleted;
  final double rating;
  final int studentsCount;

  LearningModule({
    required this.id,
    required this.title,
    required this.description,
    required this.difficulty,
    required this.duration,
    required this.lessonsCount,
    required this.completedLessons,
    required this.topics,
    required this.imageUrl,
    required this.isCompleted,
    required this.rating,
    required this.studentsCount,
  });
}

/// نموذج الاختبار التفاعلي
class InteractiveQuiz {
  final String id;
  final String title;
  final String description;
  final int questionsCount;
  final int timeLimit;
  final QuizDifficulty difficulty;
  final String category;
  final String imageUrl;
  final bool isCompleted;
  final int lastScore;
  final int bestScore;
  final int attemptsCount;

  InteractiveQuiz({
    required this.id,
    required this.title,
    required this.description,
    required this.questionsCount,
    required this.timeLimit,
    required this.difficulty,
    required this.category,
    required this.imageUrl,
    required this.isCompleted,
    required this.lastScore,
    required this.bestScore,
    required this.attemptsCount,
  });
}

/// نموذج اللعبة التعليمية
class EducationalGame {
  final String id;
  final String title;
  final String description;
  final GameType gameType;
  final GameDifficulty difficulty;
  final int estimatedTime;
  final String imageUrl;
  final bool isUnlocked;
  final bool isCompleted;
  final int bestScore;
  final int stars;

  EducationalGame({
    required this.id,
    required this.title,
    required this.description,
    required this.gameType,
    required this.difficulty,
    required this.estimatedTime,
    required this.imageUrl,
    required this.isUnlocked,
    required this.isCompleted,
    required this.bestScore,
    required this.stars,
  });
}

/// نموذج الفيديو التعليمي
class LearningVideo {
  final String id;
  final String title;
  final String description;
  final int duration;
  final String category;
  final String instructor;
  final String thumbnailUrl;
  final String videoUrl;
  final int viewsCount;
  final double rating;
  final bool isWatched;
  final double watchProgress;

  LearningVideo({
    required this.id,
    required this.title,
    required this.description,
    required this.duration,
    required this.category,
    required this.instructor,
    required this.thumbnailUrl,
    required this.videoUrl,
    required this.viewsCount,
    required this.rating,
    required this.isWatched,
    required this.watchProgress,
  });
}

/// مستويات صعوبة التعلم
enum LearningDifficulty {
  beginner, // مبتدئ
  intermediate, // متوسط
  advanced, // متقدم
}

/// مستويات صعوبة الاختبار
enum QuizDifficulty {
  easy, // سهل
  medium, // متوسط
  hard, // صعب
}

/// مستويات صعوبة اللعبة
enum GameDifficulty {
  easy, // سهل
  medium, // متوسط
  hard, // صعب
}

/// أنواع الألعاب
enum GameType {
  matching, // مطابقة
  strategy, // استراتيجية
  simulation, // محاكاة
  puzzle, // ألغاز
  quiz, // اختبار
}

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/utils/logging/logger_service.dart';
import '../../../../core/helpers/experts_error_handler.dart';
import '../../../../data/models/experts/index.dart';
import '../../../../domain/usecases/experts/index.dart';
import 'expert_ratings_state.dart';

/// Cubit إدارة حالة تقييمات الخبراء
///
/// يدير جميع العمليات المتعلقة بتقييمات ومراجعات الخبراء الزراعيين
/// وفقاً للتفضيلات المحددة باستخدام Cubit بدلاً من StatefulWidget
class ExpertRatingsCubit extends Cubit<ExpertRatingsState> {
  /// حالة استخدام إضافة التقييم
  final AddExpertRating _addExpertRating;

  /// حالة استخدام جلب التقييمات
  final GetExpertRatings _getExpertRatings;

  /// حالة استخدام إدارة التقييم
  final ManageExpertRating _manageExpertRating;

  /// إنشاء Cubit تقييمات الخبراء
  ExpertRatingsCubit(
    this._addExpertRating,
    this._getExpertRatings,
    this._manageExpertRating,
  ) : super(const ExpertRatingsInitial());

  /// تحميل تقييمات خبير محدد
  Future<void> loadExpertRatings(String expertId) async {
    try {
      emit(const ExpertRatingsLoading());
      LoggerService.info('بدء تحميل تقييمات الخبير: $expertId');

      final result = await _getExpertRatings(expertId: expertId);
      final ratings = result['ratings'] as List<ExpertRatingModel>;

      // حساب الإحصائيات
      final averageRating = _calculateAverageRating(ratings);
      final ratingsDistribution = _calculateRatingsDistribution(ratings);

      LoggerService.info('تم تحميل ${ratings.length} تقييم للخبير');

      emit(
        ExpertRatingsLoaded(
          ratings: ratings,
          filteredRatings: ratings,
          expertId: expertId,
          averageRating: averageRating,
          totalRatings: ratings.length,
          ratingsDistribution: ratingsDistribution,
        ),
      );
    } catch (e) {
      LoggerService.error('خطأ في تحميل تقييمات الخبير', error: e);
      final errorMessage = _getErrorMessage(e, 'تحميل تقييمات الخبير');
      emit(ExpertRatingsError(errorMessage));
    }
  }

  /// إضافة تقييم جديد
  Future<void> addRating({
    required String expertId,
    required String farmerId,
    required int rating,
    required String comment,
    String? consultationId,
  }) async {
    try {
      LoggerService.info('بدء إضافة تقييم جديد للخبير: $expertId');

      final ratingId = await _addExpertRating(
        expertId: expertId,
        expertName: 'خبير زراعي', // اسم افتراضي للخبير
        farmerId: farmerId,
        farmerName: 'مزارع', // اسم افتراضي للمزارع
        consultationId: consultationId ?? '',
        consultationTitle: 'استشارة زراعية', // عنوان افتراضي للاستشارة
        overallRating: rating.toDouble(),
        responseQuality: rating.toDouble(),
        responseSpeed: rating.toDouble(),
        expertise: rating.toDouble(),
        communication: rating.toDouble(),
        helpfulness: rating.toDouble(),
        comment: comment,
        wouldRecommend: rating >= 4,
      );

      LoggerService.info('تم إضافة التقييم بنجاح: $ratingId');

      // إنشاء نموذج التقييم للعرض
      final newRating = ExpertRatingModel(
        id: ratingId,
        expertId: expertId,
        expertName: 'الخبير',
        farmerId: farmerId,
        farmerName: 'المزارع',
        consultationId: consultationId ?? '',
        consultationTitle: 'استشارة',
        overallRating: rating.toDouble(),
        responseQuality: rating.toDouble(),
        responseSpeed: rating.toDouble(),
        expertise: rating.toDouble(),
        communication: rating.toDouble(),
        helpfulness: rating.toDouble(),
        comment: comment,
        wouldRecommend: rating >= 4,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isHidden: false,
      );

      emit(RatingAdded(newRating));

      // إعادة تحميل التقييمات
      await loadExpertRatings(expertId);
    } catch (e) {
      LoggerService.error('خطأ في إضافة التقييم', error: e);
      final errorMessage = _getErrorMessage(e, 'إضافة التقييم');
      emit(ExpertRatingsError(errorMessage));
    }
  }

  /// تحديث تقييم موجود
  Future<void> updateRating({
    required String ratingId,
    required int rating,
    required String comment,
  }) async {
    try {
      LoggerService.info('بدء تحديث التقييم: $ratingId');

      final success = await _manageExpertRating.updateRating(
        ratingId: ratingId,
        expertId: 'expert_default', // معرف افتراضي للخبير
        rating: rating,
        comment: comment,
      );

      if (success) {
        LoggerService.info('تم تحديث التقييم بنجاح');

        // إنشاء نموذج التقييم المحدث للعرض
        final updatedRating = ExpertRatingModel(
          id: ratingId,
          expertId: 'expert_default', // معرف افتراضي للخبير
          expertName: 'خبير زراعي', // اسم افتراضي للخبير
          farmerId: 'farmer_default', // معرف افتراضي للمزارع
          farmerName: 'مزارع', // اسم افتراضي للمزارع
          consultationId: 'consultation_default', // معرف افتراضي للاستشارة
          consultationTitle: 'استشارة زراعية', // عنوان افتراضي للاستشارة
          overallRating: rating.toDouble(),
          responseQuality: rating.toDouble(),
          responseSpeed: rating.toDouble(),
          expertise: rating.toDouble(),
          communication: rating.toDouble(),
          helpfulness: rating.toDouble(),
          comment: comment,
          wouldRecommend: rating >= 4,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        emit(RatingUpdated(updatedRating));

        // تحديث القائمة المحلية
        _updateLocalRating(updatedRating);
      } else {
        throw Exception('فشل في تحديث التقييم');
      }
    } catch (e) {
      LoggerService.error('خطأ في تحديث التقييم', error: e);
      final errorMessage = _getErrorMessage(e, 'تحديث التقييم');
      emit(ExpertRatingsError(errorMessage));
    }
  }

  /// حذف تقييم
  Future<void> deleteRating(String ratingId) async {
    try {
      LoggerService.info('بدء حذف التقييم: $ratingId');

      await _manageExpertRating.deleteRating(
        ratingId: ratingId,
        expertId: 'expert_default', // معرف افتراضي للخبير
      );

      LoggerService.info('تم حذف التقييم بنجاح');

      // إزالة التقييم من القائمة المحلية
      _removeLocalRating(ratingId);
    } catch (e) {
      LoggerService.error('خطأ في حذف التقييم', error: e);
      final errorMessage = _getErrorMessage(e, 'حذف التقييم');
      emit(ExpertRatingsError(errorMessage));
    }
  }

  /// البحث في التقييمات
  void searchRatings(String query) {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    try {
      LoggerService.info('البحث في التقييمات: $query');

      List<ExpertRatingModel> searchResults;

      if (query.trim().isEmpty) {
        searchResults = currentState.ratings;
      } else {
        final lowerQuery = query.toLowerCase();
        searchResults =
            currentState.ratings.where((rating) {
              return (rating.comment?.toLowerCase().contains(lowerQuery) ??
                      false) ||
                  rating.farmerName.toLowerCase().contains(lowerQuery);
            }).toList();
      }

      // تطبيق الفلاتر الحالية
      final filteredResults = _applyFilters(
        searchResults,
        currentState.ratingFilter,
        currentState.dateFromFilter,
        currentState.dateToFilter,
      );

      // ترتيب النتائج
      final sortedResults = _sortRatings(
        filteredResults,
        currentState.sortCriteria,
      );

      emit(
        currentState.copyWith(
          filteredRatings: sortedResults,
          searchQuery: query,
        ),
      );

      LoggerService.info('تم العثور على ${sortedResults.length} نتيجة للبحث');
    } catch (e) {
      LoggerService.error('خطأ في البحث', error: e);
    }
  }

  /// فلترة التقييمات حسب عدد النجوم
  void filterByRating(int? rating) {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    try {
      LoggerService.info('فلترة التقييمات حسب عدد النجوم: $rating');

      final filteredResults = _applyFilters(
        currentState.ratings,
        rating,
        currentState.dateFromFilter,
        currentState.dateToFilter,
      );

      final sortedResults = _sortRatings(
        filteredResults,
        currentState.sortCriteria,
      );

      emit(
        currentState.copyWith(
          filteredRatings: sortedResults,
          ratingFilter: rating,
        ),
      );

      LoggerService.info('تم فلترة ${sortedResults.length} تقييم');
    } catch (e) {
      LoggerService.error('خطأ في الفلترة', error: e);
    }
  }

  /// فلترة التقييمات حسب التاريخ
  void filterByDateRange(DateTime? dateFrom, DateTime? dateTo) {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    try {
      LoggerService.info('فلترة التقييمات حسب التاريخ: $dateFrom - $dateTo');

      final filteredResults = _applyFilters(
        currentState.ratings,
        currentState.ratingFilter,
        dateFrom,
        dateTo,
      );

      final sortedResults = _sortRatings(
        filteredResults,
        currentState.sortCriteria,
      );

      emit(
        currentState.copyWith(
          filteredRatings: sortedResults,
          dateFromFilter: dateFrom,
          dateToFilter: dateTo,
        ),
      );

      LoggerService.info('تم فلترة ${sortedResults.length} تقييم');
    } catch (e) {
      LoggerService.error('خطأ في الفلترة', error: e);
    }
  }

  /// ترتيب التقييمات
  void sortRatings(RatingSortCriteria criteria) {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    try {
      LoggerService.info('ترتيب التقييمات حسب: $criteria');

      final sortedResults = _sortRatings(
        currentState.filteredRatings,
        criteria,
      );

      emit(
        currentState.copyWith(
          filteredRatings: sortedResults,
          sortCriteria: criteria,
        ),
      );

      LoggerService.info('تم ترتيب ${sortedResults.length} تقييم');
    } catch (e) {
      LoggerService.error('خطأ في الترتيب', error: e);
    }
  }

  /// مسح جميع الفلاتر
  void clearFilters() {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    try {
      LoggerService.info('مسح جميع الفلاتر');

      final sortedRatings = _sortRatings(
        currentState.ratings,
        currentState.sortCriteria,
      );

      emit(
        currentState.copyWith(
          filteredRatings: sortedRatings,
          searchQuery: '',
          ratingFilter: null,
          dateFromFilter: null,
          dateToFilter: null,
        ),
      );

      LoggerService.info('تم مسح الفلاتر وعرض ${sortedRatings.length} تقييم');
    } catch (e) {
      LoggerService.error('خطأ في مسح الفلاتر', error: e);
    }
  }

  /// إعادة تحميل البيانات
  Future<void> refresh(String expertId) async {
    LoggerService.info('إعادة تحميل بيانات التقييمات');
    await loadExpertRatings(expertId);
  }

  /// تحديث التقييم في القائمة المحلية
  void _updateLocalRating(ExpertRatingModel updatedRating) {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    final updatedRatings =
        currentState.ratings.map((rating) {
          return rating.id == updatedRating.id ? updatedRating : rating;
        }).toList();

    final updatedFilteredRatings =
        currentState.filteredRatings.map((rating) {
          return rating.id == updatedRating.id ? updatedRating : rating;
        }).toList();

    // إعادة حساب الإحصائيات
    final averageRating = _calculateAverageRating(updatedRatings);
    final ratingsDistribution = _calculateRatingsDistribution(updatedRatings);

    emit(
      currentState.copyWith(
        ratings: updatedRatings,
        filteredRatings: updatedFilteredRatings,
        averageRating: averageRating,
        totalRatings: updatedRatings.length,
        ratingsDistribution: ratingsDistribution,
      ),
    );
  }

  /// إزالة التقييم من القائمة المحلية
  void _removeLocalRating(String ratingId) {
    final currentState = state;
    if (currentState is! ExpertRatingsLoaded) return;

    final updatedRatings =
        currentState.ratings.where((rating) => rating.id != ratingId).toList();

    final updatedFilteredRatings =
        currentState.filteredRatings
            .where((rating) => rating.id != ratingId)
            .toList();

    // إعادة حساب الإحصائيات
    final averageRating = _calculateAverageRating(updatedRatings);
    final ratingsDistribution = _calculateRatingsDistribution(updatedRatings);

    emit(
      currentState.copyWith(
        ratings: updatedRatings,
        filteredRatings: updatedFilteredRatings,
        averageRating: averageRating,
        totalRatings: updatedRatings.length,
        ratingsDistribution: ratingsDistribution,
      ),
    );
  }

  /// تطبيق الفلاتر على قائمة التقييمات
  List<ExpertRatingModel> _applyFilters(
    List<ExpertRatingModel> ratings,
    int? ratingFilter,
    DateTime? dateFrom,
    DateTime? dateTo,
  ) {
    var filteredRatings = ratings;

    // فلترة حسب عدد النجوم
    if (ratingFilter != null) {
      filteredRatings =
          filteredRatings
              .where((rating) => rating.rating == ratingFilter)
              .toList();
    }

    // فلترة حسب التاريخ
    if (dateFrom != null) {
      filteredRatings =
          filteredRatings
              .where((rating) => rating.createdAt.isAfter(dateFrom))
              .toList();
    }

    if (dateTo != null) {
      filteredRatings =
          filteredRatings
              .where((rating) => rating.createdAt.isBefore(dateTo))
              .toList();
    }

    return filteredRatings;
  }

  /// ترتيب التقييمات
  List<ExpertRatingModel> _sortRatings(
    List<ExpertRatingModel> ratings,
    RatingSortCriteria criteria,
  ) {
    final sortedList = List<ExpertRatingModel>.from(ratings);

    switch (criteria) {
      case RatingSortCriteria.dateCreated:
        sortedList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case RatingSortCriteria.rating:
        sortedList.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case RatingSortCriteria.farmerName:
        sortedList.sort((a, b) => a.farmerName.compareTo(b.farmerName));
        break;
    }

    return sortedList;
  }

  /// حساب متوسط التقييم
  double _calculateAverageRating(List<ExpertRatingModel> ratings) {
    if (ratings.isEmpty) return 0.0;

    final sum = ratings.fold<int>(0, (sum, rating) => sum + rating.rating);
    return sum / ratings.length;
  }

  /// حساب توزيع التقييمات
  Map<int, int> _calculateRatingsDistribution(List<ExpertRatingModel> ratings) {
    final distribution = <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

    for (final rating in ratings) {
      distribution[rating.rating] = (distribution[rating.rating] ?? 0) + 1;
    }

    return distribution;
  }

  /// الحصول على رسالة خطأ مناسبة
  String _getErrorMessage(dynamic error, String operation) {
    return ExpertsErrorHandler.getErrorMessage(error, operation);
  }
}

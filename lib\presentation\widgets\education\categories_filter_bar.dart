
import '../../../imports.dart';
import 'category_filter_chip.dart';

/// ويدجت شريط تصفية التصنيفات
///
/// يعرض قائمة أفقية قابلة للتمرير من تصنيفات الدورات
/// مع إمكانية تحديد تصنيف لتصفية الدورات
class CategoriesFilterBar extends StatelessWidget {
  /// التصنيف المحدد حالياً
  final String selectedCategory;

  /// دالة استدعاء عند تغيير التصنيف
  final Function(String) onCategoryChanged;

  /// خريطة عدد الدورات لكل تصنيف (اختياري)
  final Map<String, int>? categoriesCount;

  /// إنشاء ويدجت شريط تصفية التصنيفات
  ///
  /// المعلمات:
  /// - [selectedCategory]: التصنيف المحدد حالياً
  /// - [onCategoryChanged]: دالة استدعاء عند تغيير التصنيف
  /// - [categoriesCount]: خريطة عدد الدورات لكل تصنيف (اختياري)
  const CategoriesFilterBar({
    super.key,
    required this.selectedCategory,
    required this.onCategoryChanged,
    this.categoriesCount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: EducationCategoriesConstants.allCategories.length,
        itemBuilder: (context, index) {
          final categoryId = EducationCategoriesConstants.allCategories[index];
          final isSelected = selectedCategory == categoryId;
          final count = categoriesCount?[categoryId];

          return CategoryFilterChip(
            categoryId: categoryId,
            isSelected: isSelected,
            coursesCount: count,
            onTap: () => onCategoryChanged(categoryId),
          );
        },
      ),
    );
  }
}

/// ويدجت شبكة تصنيفات الدورات
///
/// يعرض التصنيفات في شكل شبكة مع بطاقات كبيرة
/// مناسب للعرض في صفحة منفصلة أو قسم مخصص
class CategoriesGrid extends StatelessWidget {
  /// دالة استدعاء عند النقر على تصنيف
  final Function(String) onCategoryTap;

  /// خريطة عدد الدورات لكل تصنيف (اختياري)
  final Map<String, int>? categoriesCount;

  /// إنشاء ويدجت شبكة تصنيفات الدورات
  ///
  /// المعلمات:
  /// - [onCategoryTap]: دالة استدعاء عند النقر على تصنيف
  /// - [categoriesCount]: خريطة عدد الدورات لكل تصنيف (اختياري)
  const CategoriesGrid({
    super.key,
    required this.onCategoryTap,
    this.categoriesCount,
  });

  @override
  Widget build(BuildContext context) {
    // عرض التصنيفات بدون "الكل"
    final categories = EducationCategoriesConstants.displayCategories;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final categoryId = categories[index];
        return _buildCategoryCard(context, categoryId);
      },
    );
  }

  /// بناء بطاقة التصنيف
  Widget _buildCategoryCard(BuildContext context, String categoryId) {
    final categoryName = EducationCategoriesConstants.getCategoryName(categoryId);
    EducationCategoriesConstants.getCategoryDescription(categoryId);
    final categoryColor = Color(EducationCategoriesConstants.getCategoryColor(categoryId));
    final iconCode = EducationCategoriesConstants.getCategoryIconCode(categoryId);
    final count = categoriesCount?[categoryId] ?? 0;

    return GestureDetector(
      onTap: () => onCategoryTap(categoryId),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              categoryColor.withOpacity(0.8),
              categoryColor,
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: categoryColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // أيقونة التصنيف
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AssetsColors.kWhite.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  IconData(iconCode, fontFamily: 'MaterialIcons'),
                  size: 24,
                  color: AssetsColors.kWhite,
                ),
              ),

              const SizedBox(height: 12),

              // اسم التصنيف
              Text(
                categoryName,
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kWhite,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const Spacer(),

              // عدد الدورات
              if (count > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AssetsColors.kWhite.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$count دورة',
                    style: TextStyles.of(context).bodySmall(
                      fontSize: 12,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.w500,
                      color: AssetsColors.kWhite,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
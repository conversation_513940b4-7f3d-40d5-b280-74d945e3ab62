
import 'package:url_launcher/url_launcher.dart';

import '../../../imports.dart';
import '../../../data/datasources/remote/firebase/expert_ratings_firebase_service.dart';
import '../../widgets/reach_engineer/expert_rating_widget.dart';

/// صفحة ملف الخبير الشخصي
///
/// تعرض تفاصيل الخبير مع التقييمات والمراجعات
class ExpertProfilePage extends StatefulWidget {
  /// بيانات الخبير
  final AgriculturalExpertModel expert;

  /// معرف المزارع الحالي
  final String? currentFarmerId;

  /// اسم المزارع الحالي
  final String? currentFarmerName;

  /// إنشاء صفحة ملف الخبير
  const ExpertProfilePage({
    super.key,
    required this.expert,
    this.currentFarmerId,
    this.currentFarmerName,
  });

  @override
  State<ExpertProfilePage> createState() => _ExpertProfilePageState();
}

class _ExpertProfilePageState extends State<ExpertProfilePage> {
  final ExpertRatingsFirebaseService _ratingsService =
      ExpertRatingsFirebaseService();

  // متغيرات التقييمات
  double? _realAverageRating;
  int? _realRatingsCount;
  bool _isLoadingRatings = true;

  @override
  void initState() {
    super.initState();
    _loadExpertRatings();
  }

  /// تحميل تقييمات الخبير الحقيقية
  Future<void> _loadExpertRatings() async {
    try {
      setState(() {
        _isLoadingRatings = true;
      });

      final averageRating = await _ratingsService.getExpertAverageRating(
        widget.expert.id,
      );
      final ratingsCount = await _ratingsService.getExpertRatingsCount(
        widget.expert.id,
      );

      if (mounted) {
        setState(() {
          _realAverageRating = averageRating;
          _realRatingsCount = ratingsCount;
          _isLoadingRatings = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _realAverageRating = widget.expert.rating; // fallback للقيمة الأصلية
          _realRatingsCount = widget.expert.consultationsCount; // fallback
          _isLoadingRatings = false;
        });
      }
    }
  }

  /// إنشاء نسخة محدثة من الخبير مع التقييمات الحقيقية
  AgriculturalExpertModel _getUpdatedExpert() {
    return widget.expert.copyWith(
      rating: _realAverageRating ?? widget.expert.rating,
      consultationsCount: _realRatingsCount ?? widget.expert.consultationsCount,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.expert.name),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
        actions: [
          IconButton(onPressed: _shareExpert, icon: const Icon(Icons.share)),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // معلومات الخبير الأساسية
            _buildExpertHeader(),

            // الإحصائيات
            _buildStatistics(),

            // النبذة التعريفية
            _buildBioSection(),

            // المؤهلات والشهادات
            _buildCertificationsSection(),

            // التقييمات والمراجعات
            _buildRatingsSection(),

            const SizedBox(height: 20),
          ],
        ),
      ),
      bottomNavigationBar: _buildActionButtons(),
    );
  }

  /// بناء رأس معلومات الخبير
  Widget _buildExpertHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
        ),
      ),
      child: Column(
        children: [
          // صورة الخبير
          CircleAvatar(
            radius: 60,
            backgroundImage: NetworkImage(widget.expert.profileImage),
            backgroundColor: AppColors.surface,
          ),

          const SizedBox(height: 16),

          // اسم الخبير
          Text(
            widget.expert.name,
            style: AppTextStyles.headlineMedium.copyWith(
              color: AppColors.onPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // التخصص
          Text(
            widget.expert.specialization,
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.onPrimary.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // الموقع
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.onPrimary.withValues(alpha: 0.8),
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${widget.expert.city}, ${widget.expert.governorate}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.onPrimary.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // حالة التوفر
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color:
                  widget.expert.isAvailable
                      ? AppColors.success.withValues(alpha: 0.2)
                      : AppColors.error.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color:
                    widget.expert.isAvailable
                        ? AppColors.success
                        : AppColors.error,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.expert.isAvailable ? Icons.check_circle : Icons.cancel,
                  color:
                      widget.expert.isAvailable
                          ? AppColors.success
                          : AppColors.error,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  widget.expert.isAvailable ? 'متاح' : 'غير متاح',
                  style: AppTextStyles.bodySmall.copyWith(
                    color:
                        widget.expert.isAvailable
                            ? AppColors.success
                            : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات
  Widget _buildStatistics() {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // التقييم
            Expanded(
              child: _buildStatItem(
                icon: Icons.star,
                value:
                    _isLoadingRatings
                        ? '...'
                        : (_realAverageRating?.toStringAsFixed(1) ?? '0.0'),
                label: 'التقييم',
                color: AppColors.warning,
              ),
            ),

            _buildDivider(),

            // سنوات الخبرة
            Expanded(
              child: _buildStatItem(
                icon: Icons.work,
                value: '${widget.expert.experienceYears}',
                label: 'سنة خبرة',
                color: AppColors.info,
              ),
            ),

            _buildDivider(),

            // عدد التقييمات
            Expanded(
              child: _buildStatItem(
                icon: Icons.chat,
                value: _isLoadingRatings ? '...' : '${_realRatingsCount ?? 0}',
                label: 'تقييم',
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.headlineSmall.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء فاصل
  Widget _buildDivider() {
    return Container(
      height: 40,
      width: 1,
      color: AppColors.textSecondary.withValues(alpha: 0.3),
      margin: const EdgeInsets.symmetric(horizontal: 16),
    );
  }

  /// بناء قسم النبذة التعريفية
  Widget _buildBioSection() {
    if (widget.expert.bio.isEmpty) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'نبذة تعريفية',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              widget.expert.bio,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المؤهلات والشهادات
  Widget _buildCertificationsSection() {
    if (widget.expert.certifications.isEmpty) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'المؤهلات والشهادات',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...widget.expert.certifications.map(
              (cert) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Icon(Icons.verified, color: AppColors.success, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        cert,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الاتصال
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _callExpert,
              icon: const Icon(Icons.phone),
              label: const Text('اتصال'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.success,
                side: BorderSide(color: AppColors.success),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // زر طلب استشارة
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed:
                  widget.expert.isAvailable ? _requestConsultation : null,
              icon: const Icon(Icons.help),
              label: const Text('طلب استشارة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الاتصال بالخبير
  void _callExpert() {
    _makePhoneCall(widget.expert.phone, widget.expert.name);
  }

  /// إجراء مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber, String expertName) async {
    try {
      LoggerService.info('محاولة الاتصال بـ $expertName: $phoneNumber');
      final phoneUrl = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(phoneUrl)) {
        await launchUrl(phoneUrl);
        LoggerService.info('تم فتح تطبيق الهاتف للاتصال');
      } else {
        _showErrorMessage('لا يمكن إجراء المكالمة');
      }
    } catch (e) {
      LoggerService.error('خطأ في إجراء المكالمة', error: e);
      _showErrorMessage('خطأ في إجراء المكالمة');
    }
  }

  /// بناء قسم التقييمات
  Widget _buildRatingsSection() {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'التقييمات والمراجعات',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: الانتقال لصفحة جميع التقييمات
                  },
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // استخدام ويدجت التقييمات المحسن مع البيانات الحقيقية
            ExpertRatingWidget(
              expert: _getUpdatedExpert(),
              showDetailedRatings: true,
              ratingsLimit: 3,
            ),
          ],
        ),
      ),
    );
  }

  /// طلب استشارة
  void _requestConsultation() {
    if (widget.currentFarmerId == null || widget.currentFarmerName == null) {
      _showErrorMessage('يجب تسجيل الدخول أولاً');
      return;
    }

    // التنقل لصفحة طلب الاستشارة
    Navigator.of(context).pushNamed(
      RouteConstants.consultationRequest,
      arguments: {'expert': widget.expert},
    );
  }

  /// مشاركة الخبير
  void _shareExpert() {
    // TODO: تنفيذ المشاركة
    _showInfoMessage('سيتم إضافة ميزة المشاركة قريباً');
  }

  // ========== الوظائف المساعدة ==========

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// عرض رسالة معلومات
  void _showInfoMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}


import 'package:jiffy/jiffy.dart';

import '../../../core/constants/weather/index.dart';
import '../../../imports.dart';
import '../../../data/models/weather/weather_model.dart';

/// رأس صفحة الطقس المحسن
///
/// يعرض معلومات الطقس الحالية مع تأثيرات بصرية جميلة
class EnhancedWeatherHeader extends StatefulWidget {
  final WeatherModel weather;
  final VoidCallback? onRefresh;
  final VoidCallback? onSettings;

  const EnhancedWeatherHeader({
    super.key,
    required this.weather,
    this.onRefresh,
    this.onSettings,
  });

  @override
  State<EnhancedWeatherHeader> createState() => _EnhancedWeatherHeaderState();
}

class _EnhancedWeatherHeaderState extends State<EnhancedWeatherHeader>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<double>(begin: -50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              padding: EdgeInsets.all(WeatherDimensions.cardPadding),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.15),
                    Colors.white.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(
                  WeatherDimensions.cardBorderRadius,
                ),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // شريط التنقل العلوي
                  _buildTopBar(),
                  SizedBox(height: WeatherDimensions.largeMargin),

                  // معلومات الطقس الرئيسية
                  _buildMainWeatherInfo(),
                  SizedBox(height: WeatherDimensions.mediumMargin),

                  // معلومات إضافية
                  _buildAdditionalInfo(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء شريط التنقل العلوي
  Widget _buildTopBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // زر الإعدادات
        _buildActionButton(icon: Icons.settings, onTap: widget.onSettings),

        // التاريخ والوقت
        Column(
          children: [
            Text(
              Jiffy.now().format(pattern: 'EEEE'),
              style: TextStyle(
                fontSize: WeatherDimensions.bodyFontSize,
                fontFamily: AssetsFonts.cairo,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            Text(
              Jiffy.now().format(pattern: 'dd MMMM yyyy'),
              style: TextStyle(
                fontSize: WeatherDimensions.subtitleFontSize,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),

        // زر التحديث
        _buildActionButton(
          icon: Icons.refresh,
          onTap: widget.onRefresh,
          withPulse: true,
        ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    VoidCallback? onTap,
    bool withPulse = false,
  }) {
    Widget button = Container(
      padding: EdgeInsets.all(WeatherDimensions.smallPadding),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(
          WeatherDimensions.buttonBorderRadius,
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: WeatherDimensions.mediumIconSize,
      ),
    );

    if (withPulse) {
      button = AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(scale: _pulseAnimation.value, child: button);
        },
      );
    }

    return GestureDetector(onTap: onTap, child: button);
  }

  /// بناء معلومات الطقس الرئيسية
  Widget _buildMainWeatherInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // أيقونة الطقس
        Container(
          padding: EdgeInsets.all(WeatherDimensions.mediumPadding),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Icon(_getWeatherIcon(), size: 60, color: Colors.white),
        ),

        // درجة الحرارة والوصف
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${widget.weather.main?.temp?.round() ?? '--'}°',
              style: TextStyle(
                fontSize: 48,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              widget.weather.weather?.first.description ?? 'غير متوفر',
              style: TextStyle(
                fontSize: WeatherDimensions.bodyFontSize,
                fontFamily: AssetsFonts.cairo,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
            Text(
              widget.weather.name ?? 'موقع غير محدد',
              style: TextStyle(
                fontSize: WeatherDimensions.smallFontSize,
                fontFamily: AssetsFonts.cairo,
                color: Colors.white.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildInfoCard(
          'الرطوبة',
          '${widget.weather.main?.humidity ?? 0}%',
          Icons.water_drop,
        ),
        _buildInfoCard(
          'الرياح',
          '${widget.weather.wind?.speed?.toStringAsFixed(1) ?? 0} م/ث',
          Icons.air,
        ),
        _buildInfoCard(
          'الضغط',
          '${widget.weather.main?.pressure ?? 0} هـ ب',
          Icons.speed,
        ),
      ],
    );
  }

  /// بناء بطاقة معلومة
  Widget _buildInfoCard(String title, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: WeatherDimensions.mediumPadding,
        vertical: WeatherDimensions.smallPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(
          WeatherDimensions.buttonBorderRadius,
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white.withValues(alpha: 0.8),
            size: WeatherDimensions.smallIconSize,
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: WeatherDimensions.smallFontSize,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: WeatherDimensions.smallFontSize,
              fontFamily: AssetsFonts.cairo,
              color: Colors.white.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الطقس
  IconData _getWeatherIcon() {
    final weatherId = widget.weather.weather?.first.id ?? 800;

    if (weatherId >= 200 && weatherId < 300) {
      return Icons.thunderstorm;
    } else if (weatherId >= 300 && weatherId < 400) {
      return Icons.grain;
    } else if (weatherId >= 500 && weatherId < 600) {
      return Icons.umbrella;
    } else if (weatherId >= 600 && weatherId < 700) {
      return Icons.ac_unit;
    } else if (weatherId >= 700 && weatherId < 800) {
      return Icons.foggy;
    } else if (weatherId == 800) {
      return Icons.wb_sunny;
    } else {
      return Icons.cloud;
    }
  }
}

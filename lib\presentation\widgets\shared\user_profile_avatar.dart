

import '../../../imports.dart';

/// مكون صورة المستخدم الدائرية
///
/// يعرض صورة المستخدم الحالي في شكل دائري مع إمكانية التحكم في الحجم
/// ويمكن استخدامه في أماكن مختلفة من التطبيق مثل الأبار وصفحة الملف الشخصي
class UserProfileAvatar extends StatelessWidget {
  /// نصف قطر الدائرة
  final double radius;

  /// دالة يتم استدعاؤها عند النقر على الصورة
  final VoidCallback? onTap;

  /// لون خلفية الصورة في حالة عدم وجود صورة
  final Color? backgroundColor;

  /// لون أيقونة المستخدم الافتراضية
  final Color iconColor;

  /// حجم أيقونة المستخدم الافتراضية
  final double iconSize;

  /// ما إذا كان يجب عرض حدود حول الصورة
  final bool showBorder;

  /// لون حدود الصورة
  final Color borderColor;

  /// سمك حدود الصورة
  final double borderWidth;

  /// دالة مخصصة لبناء محتوى الصورة
  /// تسمح بتخصيص محتوى الصورة بشكل كامل
  final Widget? customWidget;

  /// إنشاء مكون صورة المستخدم
  const UserProfileAvatar({
    super.key,
    this.radius = 18,
    this.onTap,
    this.backgroundColor,
    this.iconColor = Colors.white,
    this.iconSize = 20,
    this.showBorder = false,
    this.borderColor = Colors.white,
    this.borderWidth = 2,
    this.customWidget,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        // الحصول على المستخدم الحالي من AuthCubit
        final userAccount = context.read<AuthCubit>().currentUser;

        // إنشاء مكون الصورة
        final avatarWidget = customWidget != null
            ? _buildCustomAvatar()
            : userAccount?.image != null && userAccount!.image.isNotEmpty
                ? _buildAvatarWithImage(userAccount.image)
                : _buildDefaultAvatar();

        // إضافة التفاعل عند النقر إذا كان مطلوباً
        if (onTap != null) {
          return GestureDetector(
            onTap: onTap,
            child: avatarWidget,
          );
        }

        return avatarWidget;
      },
    );
  }

  /// بناء الصورة الدائرية مع صورة المستخدم
  Widget _buildAvatarWithImage(String imageUrl) {
    return Container(
      decoration: showBorder
          ? BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: borderColor,
                width: borderWidth,
              ),
            )
          : null,
      child: CircleAvatar(
        radius: radius,
        backgroundColor: backgroundColor ?? Colors.white.withAlpha(77),
        child: ClipOval(
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey.shade300,
              child: Icon(
                Icons.person,
                color: iconColor,
                size: iconSize,
              ),
            ),
            errorWidget: (context, url, error) => Icon(
              Icons.person,
              color: iconColor,
              size: iconSize,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء الصورة الدائرية الافتراضية (بدون صورة)
  Widget _buildDefaultAvatar() {
    return Container(
      decoration: showBorder
          ? BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: borderColor,
                width: borderWidth,
              ),
            )
          : null,
      child: CircleAvatar(
        radius: radius,
        backgroundColor: backgroundColor ?? Colors.white.withAlpha(77),
        child: Icon(
          Icons.person,
          color: iconColor,
          size: iconSize,
        ),
      ),
    );
  }

  /// بناء صورة مخصصة باستخدام دالة البناء المخصصة
  Widget _buildCustomAvatar() {
    return Container(
      decoration: showBorder
          ? BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: borderColor,
                width: borderWidth,
              ),
            )
          : null,
      child: SizedBox(
        width: radius * 2,
        height: radius * 2,
        child: customWidget ?? Icon(
          Icons.person,
          color: iconColor,
          size: iconSize,
        ),
      ),
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

/// مشغل فيديو محسن
///
/// يوفر هذا المكون واجهة محسنة لتشغيل الفيديو مع دعم التحكم الكامل والعرض بملء الشاشة.
class EnhancedVideoPlayer extends StatefulWidget {
  /// رابط الفيديو
  final String videoUrl;

  /// نسبة العرض إلى الارتفاع (اختياري)
  final double? aspectRatio;

  /// تشغيل تلقائي
  final bool autoPlay;

  /// تكرار
  final bool looping;

  /// عرض عناصر التحكم
  final bool showControls;

  /// تمكين كتم الصوت
  final bool allowMuting;

  /// تمكين ملء الشاشة
  final bool allowFullScreen;

  /// ملء الحاوية بالكامل بدون مراعاة نسبة العرض إلى الارتفاع
  final bool fillContainer;

  /// منشئ مشغل الفيديو المحسن
  const EnhancedVideoPlayer({
    super.key,
    required this.videoUrl,
    this.aspectRatio,
    this.autoPlay = false,
    this.looping = false,
    this.showControls = true,
    this.allowMuting = true,
    this.allowFullScreen = true,
    this.fillContainer = false,
  });

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer> {
  /// تحكم في مشغل الفيديو
  VideoPlayerController? _videoPlayerController;

  /// تحكم في واجهة مشغل الفيديو
  ChewieController? _chewieController;

  /// حالة التحميل
  bool _isLoading = true;

  /// رسالة الخطأ
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  /// تهيئة مشغل الفيديو
  Future<void> _initializePlayer() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // إنشاء متحكم الفيديو
      _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));

      // محاولة تهيئة الفيديو مع مهلة زمنية
      await _videoPlayerController!.initialize().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw 'انتهت مهلة تحميل الفيديو. يرجى التحقق من اتصالك بالإنترنت.';
        },
      );

      // التحقق من أن الفيديو تم تهيئته بنجاح
      if (!_videoPlayerController!.value.isInitialized) {
        throw 'فشل تهيئة الفيديو';
      }

      // حساب نسبة العرض إلى الارتفاع
      final aspectRatio = widget.aspectRatio ?? _videoPlayerController!.value.aspectRatio;

      // إنشاء متحكم Chewie
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        aspectRatio: widget.fillContainer ? null : aspectRatio,
        autoPlay: widget.autoPlay,
        looping: widget.looping,
        showControls: widget.showControls,
        allowMuting: widget.allowMuting,
        allowFullScreen: widget.allowFullScreen,
        placeholder: Container(
          color: Colors.black.withValues(alpha: 0.1),
          child: const Center(child: CircularProgressIndicator()),
        ),
        materialProgressColors: ChewieProgressColors(
          playedColor: Colors.green,
          handleColor: Colors.green,
          backgroundColor: Colors.grey,
          bufferedColor: Colors.grey.shade400,
        ),
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.red,
                  size: 42,
                ),
                const SizedBox(height: 8),
                Text(
                  'خطأ في تشغيل الفيديو',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  errorMessage,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white70,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        },
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      // إذا حدث خطأ، نتأكد من تحرير الموارد
      _videoPlayerController?.dispose();
      _videoPlayerController = null;

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد نسبة العرض إلى الارتفاع الافتراضية
    final double finalAspectRatio = widget.aspectRatio ?? 16 / 9;

    // عرض مؤشر التحميل
    if (_isLoading) {
      return AspectRatio(
        aspectRatio: finalAspectRatio,
        child: Container(
          color: Colors.black.withValues(alpha: 0.1),
          child: const Center(child: CircularProgressIndicator()),
        ),
      );
    }

    // عرض رسالة الخطأ
    if (_errorMessage != null || _chewieController == null) {
      return AspectRatio(
        aspectRatio: finalAspectRatio,
        child: Container(
          color: Colors.black,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error,
                  color: Colors.red,
                  size: 42,
                ),
                const SizedBox(height: 8),
                Text(
                  'خطأ في تشغيل الفيديو',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                      ),
                ),
                if (_errorMessage != null) ...[
                  const SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      _errorMessage!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.white70,
                          ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _initializePlayer,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // عرض مشغل الفيديو
    if (widget.fillContainer) {
      // ملء الحاوية بالكامل بدون مراعاة نسبة العرض إلى الارتفاع
      return SizedBox.expand(
        child: Stack(
          fit: StackFit.expand,
          children: [
            // استخدام الفيديو مباشرة لملء الحاوية بالكامل
            Center(
              child: SizedBox.expand(
                child: FittedBox(
                  fit: BoxFit.cover, // لملء الحاوية بالكامل مع قص الأجزاء التي تتجاوز الحاوية
                  child: SizedBox(
                    width: _videoPlayerController!.value.size.width,
                    height: _videoPlayerController!.value.size.height,
                    child: VideoPlayer(_videoPlayerController!),
                  ),
                ),
              ),
            ),
            // عناصر التحكم فوق الفيديو
            Chewie(controller: _chewieController!),
          ],
        ),
      );
    } else {
      // عرض مع مراعاة نسبة العرض إلى الارتفاع
      return AspectRatio(
        aspectRatio: finalAspectRatio,
        child: Chewie(controller: _chewieController!),
      );
    }
  }
}

/// مشغل فيديو بملء الشاشة
///
/// يوفر هذا المكون واجهة لتشغيل الفيديو بملء الشاشة.
class FullScreenVideoPlayer extends StatelessWidget {
  /// رابط الفيديو
  final String videoUrl;

  /// عنوان الفيديو
  final String? title;

  /// منشئ مشغل الفيديو بملء الشاشة
  const FullScreenVideoPlayer({
    super.key,
    required this.videoUrl,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: Text(
          title ?? 'تشغيل الفيديو',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child: Center(
          child: EnhancedVideoPlayer(
            videoUrl: videoUrl,
            autoPlay: true,
            allowFullScreen: false,
            showControls: true,
            looping: false,
            fillContainer: true,
          ),
        ),
      ),
    );
  }
}

/// امتداد لفتح مشغل الفيديو بملء الشاشة
extension FullScreenVideoPlayerExtension on BuildContext {
  /// فتح مشغل الفيديو بملء الشاشة
  void openFullScreenVideo(String videoUrl, {String? title}) {
    Navigator.push(
      this,
      MaterialPageRoute(
        builder: (context) => FullScreenVideoPlayer(
          videoUrl: videoUrl,
          title: title,
        ),
      ),
    );
  }
}



import '../../../imports.dart';

/// صفحة طلب الاستشارة
/// تسمح للمزارعين بطلب استشارة من خبير زراعي محدد
class ConsultationRequestPage extends StatefulWidget {
  /// الخبير المطلوب الاستشارة معه
  final AgriculturalExpertModel expert;

  /// معرف المزارع الحالي
  final String farmerId;

  /// اسم المزارع الحالي
  final String farmerName;

  const ConsultationRequestPage({
    super.key,
    required this.expert,
    required this.farmerId,
    required this.farmerName,
  });

  @override
  State<ConsultationRequestPage> createState() =>
      _ConsultationRequestPageState();
}

class _ConsultationRequestPageState extends State<ConsultationRequestPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _cropTypeController = TextEditingController();
  final _locationController = TextEditingController();

  ConsultationPriority _selectedPriority = ConsultationPriority.normal;
  String _selectedConsultationType = 'عام';
  bool _isLoading = false;

  final List<ConsultationPriority> _priorities = [
    ConsultationPriority.normal,
    ConsultationPriority.urgent,
    ConsultationPriority.emergency,
  ];

  final List<String> _consultationTypes = [
    'عام',
    'أمراض النباتات',
    'الآفات الزراعية',
    'التسميد',
    'الري',
    'زراعة المحاصيل',
    'تربية الحيوانات',
    'البيوت المحمية',
    'الزراعة العضوية',
    'أخرى',
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _cropTypeController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('طلب استشارة من ${widget.expert.name}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildExpertCard(),
              const SizedBox(height: 24),
              _buildConsultationForm(),
              const SizedBox(height: 32),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات الخبير
  Widget _buildExpertCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundImage:
                  widget.expert.profileImageUrl?.isNotEmpty == true
                      ? NetworkImage(widget.expert.profileImageUrl!)
                      : null,
              child:
                  widget.expert.profileImageUrl?.isEmpty != false
                      ? const Icon(Icons.person, size: 30)
                      : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.expert.name, style: AppTextStyles.headlineMedium),
                  const SizedBox(height: 4),
                  Text(
                    widget.expert.specialization,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.expert.governorate,
                        style: AppTextStyles.bodySmall,
                      ),
                      const SizedBox(width: 16),
                      const Icon(Icons.star, size: 16, color: Colors.amber),
                      const SizedBox(width: 4),
                      Text(
                        widget.expert.rating.toStringAsFixed(1),
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء نموذج طلب الاستشارة
  Widget _buildConsultationForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('تفاصيل الاستشارة', style: AppTextStyles.headlineMedium),
        const SizedBox(height: 16),

        // عنوان الاستشارة
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'عنوان الاستشارة *',
            hintText: 'مثال: مشكلة في أوراق الطماطم',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال عنوان الاستشارة';
            }
            if (value.trim().length < 5) {
              return 'يجب أن يكون العنوان 5 أحرف على الأقل';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // نوع الاستشارة
        DropdownButtonFormField<String>(
          value: _selectedConsultationType,
          decoration: const InputDecoration(
            labelText: 'نوع الاستشارة *',
            border: OutlineInputBorder(),
          ),
          items:
              _consultationTypes.map((type) {
                return DropdownMenuItem(value: type, child: Text(type));
              }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedConsultationType = value!;
            });
          },
        ),
        const SizedBox(height: 16),

        // نوع المحصول
        TextFormField(
          controller: _cropTypeController,
          decoration: const InputDecoration(
            labelText: 'نوع المحصول أو النبات',
            hintText: 'مثال: طماطم، خيار، قمح',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // الموقع
        TextFormField(
          controller: _locationController,
          decoration: const InputDecoration(
            labelText: 'الموقع أو المنطقة',
            hintText: 'مثال: صنعاء، تعز، الحديدة',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // أولوية الاستشارة
        DropdownButtonFormField<ConsultationPriority>(
          value: _selectedPriority,
          decoration: const InputDecoration(
            labelText: 'أولوية الاستشارة *',
            border: OutlineInputBorder(),
          ),
          items:
              _priorities.map((priority) {
                return DropdownMenuItem(
                  value: priority,
                  child: Text(_getPriorityDisplayName(priority)),
                );
              }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPriority = value!;
            });
          },
        ),
        const SizedBox(height: 16),

        // وصف المشكلة
        TextFormField(
          controller: _descriptionController,
          maxLines: 5,
          decoration: const InputDecoration(
            labelText: 'وصف المشكلة أو السؤال *',
            hintText: 'اشرح المشكلة بالتفصيل...',
            border: OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى وصف المشكلة أو السؤال';
            }
            if (value.trim().length < 20) {
              return 'يجب أن يكون الوصف 20 حرف على الأقل';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitConsultationRequest,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child:
            _isLoading
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : const Text(
                  'إرسال طلب الاستشارة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
      ),
    );
  }

  /// إرسال طلب الاستشارة
  Future<void> _submitConsultationRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إرسال الطلب باستخدام CreateConsultation use case
      final createConsultation = CreateConsultation(ExpertsRepository());
      await createConsultation.call(
        farmerId: widget.farmerId,
        farmerName: widget.farmerName,
        expertId: widget.expert.id,
        expertName: widget.expert.name,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedConsultationType,
        priority: _selectedPriority,
        images: [], // لا توجد صور في هذا الإصدار
      );

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلب الاستشارة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للصفحة السابقة
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      LoggerService.error('خطأ في إرسال طلب الاستشارة', error: e);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إرسال الطلب: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// الحصول على اسم الأولوية للعرض
  String _getPriorityDisplayName(ConsultationPriority priority) {
    switch (priority) {
      case ConsultationPriority.normal:
        return 'عادية';
      case ConsultationPriority.urgent:
        return 'عاجلة';
      case ConsultationPriority.emergency:
        return 'طارئة';
    }
  }
}

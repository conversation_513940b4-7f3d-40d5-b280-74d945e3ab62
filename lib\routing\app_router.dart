
import 'package:dio/dio.dart';

import 'package:flutter/services.dart';


import '../data/models/education/article_model.dart';
import '../data/models/education/daily_tip_model.dart';
import '../data/models/education/lesson_model.dart';

import '../data/repositories/post_repository.dart';

import '../data/repositories/user_repository.dart';
import '../domain/usecases/admin/get_users_list.dart';
import '../domain/usecases/admin/update_user_role.dart';

import '../imports.dart';
import '../presentation/bloc/admin/admin_users_cubit.dart';
import '../presentation/pages/admin/admin_dashboard_page.dart';
import '../presentation/pages/admin/admin_users_page.dart';
// تم حذف صفحات الاختبار والتشخيص وفق التفضيلات المحددة
import '../presentation/widgets/shared/admin_auth_wrapper.dart';

// استيرادات شبكة الخبراء الزراعيين (متوفرة في imports.dart)

import '../presentation/bloc/experts/favorites/favorites_cubit.dart';

// تم تعطيل صفحات الاختبار للعمل بالصفحات الحقيقية

class AppRouter {
  /// الحصول على معلومات المستخدم الحالي
  static Map<String, String?> _getCurrentUserInfo() {
    try {
      final currentUser = getCurrentUser();
      if (currentUser != null) {
        return {
          'userId': currentUser.uid,
          'userName': currentUser.displayName ?? 'مستخدم',
          'userPhone': currentUser.phoneNumber ?? '',
          'userEmail': currentUser.email ?? '',
        };
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في الحصول على معلومات المستخدم الحالي',
        error: e,
      );
    }

    return {
      'userId': null,
      'userName': null,
      'userPhone': null,
      'userEmail': null,
    };
  }

  /// الحصول على معرف المستخدم الحالي
  static String? _getCurrentUserId() {
    return _getCurrentUserInfo()['userId'];
  }

  /// الحصول على اسم المستخدم الحالي
  static String _getCurrentUserName() {
    return _getCurrentUserInfo()['userName'] ?? 'مستخدم';
  }

  static List<RouteModel> routes() => [
    RouteModel(name: RouteConstants.onBoarding, view: OnBoardingPage()),
    RouteModel(name: RouteConstants.login, view: LoginPage()),
    RouteModel(name: RouteConstants.register, view: RegisterPage()),
    RouteModel(name: RouteConstants.phoneInput, view: PhoneInputPage()),
    RouteModel(
      name: RouteConstants.registerProfile,
      view: RegisterProfilePage(),
    ),
    RouteModel(name: RouteConstants.home, view: HomePage()),
    RouteModel(name: RouteConstants.profile, view: const ProfilePage()),
    //updateProfile
    RouteModel(name: RouteConstants.updateProfile, view: UpdateProfile()),
    // comunityForum
    RouteModel(name: RouteConstants.communityForum, view: ComunityForum()),
    // تم تعطيل صفحات الاختبار للعمل بالصفحات الحقيقية
    // RouteModel(
    //   name: RouteConstants.mediaTest,
    //   view: const MediaTestPage(),
    // ),
    // RouteModel(
    //   name: RouteConstants.mediaOptimizationTest,
    //   view: const MediaOptimizationTestPage(),
    // ),
    // تم نقل education إلى onGenerateRoute لإضافة مزودي BLoC المطلوبين
    // تم نقل courseDetails إلى onGenerateRoute لمعالجة المعلمات
    //enviroment
    RouteModel(
      name: RouteConstants.agriculturalCrops,
      view: AgriculturalCrops(),
    ),
    //government
    RouteModel(name: RouteConstants.government, view: Government()),
    //information
    RouteModel(name: RouteConstants.landmarks, view: Information()),
    //marketingProducts
    RouteModel(name: RouteConstants.marketingProducts, view: ProductsPage()),
    //pestsAndDiseases
    RouteModel(name: RouteConstants.pestsAndDiseases, view: PestsAndDiseases()),
    //ReachEngineer
    RouteModel(name: RouteConstants.reachEngineer, view: ReachEngineer()),
    //search
    RouteModel(name: RouteConstants.search, view: Search()),
    //settings
    RouteModel(name: RouteConstants.settings, view: Settinges()),
    //weather
    RouteModel(name: RouteConstants.weather, view: WeatherView()),
    //ShowData
    RouteModel(name: RouteConstants.dayWeather, view: DayWeatherView()),
    //Weather Charts
    RouteModel(name: RouteConstants.weatherCharts, view: WeatherChartsPage()),

    // صفحات الإدارة
    RouteModel(
      name: RouteConstants.adminDashboard,
      view: const AdminAuthWrapper(child: AdminDashboardPage()),
    ),
    RouteModel(
      name: RouteConstants.adminUsers,
      view: const AdminAuthWrapper(child: AdminUsersPage()),
    ),
    // تم حذف مسارات صفحات الاختبار والتشخيص وفق التفضيلات المحددة
    RouteModel(
      name: RouteConstants.adminQuizzes,
      view: const AdminAuthWrapper(child: AdminQuizzesPage()),
    ),
    RouteModel(
      name: RouteConstants.adminCourses,
      view: const AdminAuthWrapper(child: AdminCoursesPage()),
    ),
    RouteModel(
      name: RouteConstants.adminCourseForm,
      view: const AdminAuthWrapper(child: AdminCourseFormPage()),
    ),
    RouteModel(
      name: RouteConstants.adminDailyTips,
      view: const AdminAuthWrapper(child: AdminDailyTipsPage()),
    ),
    RouteModel(
      name: RouteConstants.adminArticles,
      view: const AdminAuthWrapper(child: AdminArticlesPage()),
    ),

    // تم نقل adminCourseLessons و adminLessonForm و adminDailyTipForm و adminArticleForm إلى onGenerateRoute لمعالجة المعلمات
  ];

  /// توليد المسار المناسب بناءً على حالة المستخدم
  ///
  /// يقوم بتوليد المسار المناسب بناءً على حالة المستخدم (مسجل الدخول أم لا)
  /// وحالة صفحة الترحيب (رآها المستخدم من قبل أم لا).
  static Route? onGenerateRoute(RouteSettings settings) {
    // لا يمكن استخدام async/await في onGenerateRoute
    // لذلك سنستخدم طريقة أخرى للتحقق من اكتمال بيانات البروفايل
    if (settings.name != null) {
      String nextRoute = settings.name!;

      try {
        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'توليد مسار: المسار المطلوب = $nextRoute, uid = "$uid", isOnBoarding = $isOnBoarding',
          tag: 'AppRouter.onGenerateRoute',
        );

        // التحقق من حالة المصادقة
        final bool isAuthenticated = uid.isNotEmpty;

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'حالة المصادقة: ${isAuthenticated ? "مسجل الدخول" : "غير مسجل الدخول"}',
          tag: 'AppRouter.onGenerateRoute',
        );

        // قائمة المسارات المسموح بها للمستخدم غير المسجل
        final List<String> unauthenticatedRoutes = [
          RouteConstants.login,
          RouteConstants.onBoarding,
          RouteConstants.register,
          RouteConstants.phoneInput,
          RouteConstants.otpVerification,
          RouteConstants.registerProfile,
        ];

        // إذا كان المستخدم مسجل الدخول
        if (isAuthenticated) {
          // التحقق من اكتمال بيانات البروفايل باستخدام SharedPrefs
          final hasCompletedProfile =
              SharedPrefs.getBool('hasCompletedProfile') ?? false;

          LoggerService.debug(
            'التحقق من اكتمال بيانات البروفايل: $hasCompletedProfile',
            tag: 'AppRouter.onGenerateRoute',
          );

          // إذا لم يكمل المستخدم بيانات البروفايل وكان المسار المطلوب ليس صفحة إكمال البروفايل
          if (!hasCompletedProfile &&
              nextRoute != RouteConstants.registerProfile) {
            LoggerService.debug(
              'المستخدم لم يكمل بيانات البروفايل، توجيهه إلى صفحة إكمال البروفايل',
              tag: 'AppRouter.onGenerateRoute',
            );

            nextRoute = RouteConstants.registerProfile;
          }
          // إذا كان المستخدم قد أكمل بيانات البروفايل وكان المسار المطلوب هو أحد مسارات المصادقة (باستثناء صفحة إكمال البروفايل)
          else if (hasCompletedProfile &&
              unauthenticatedRoutes.contains(nextRoute) &&
              nextRoute != RouteConstants.registerProfile) {
            LoggerService.debug(
              'المستخدم مسجل الدخول وأكمل بيانات البروفايل ويحاول الوصول إلى مسار مصادقة ($nextRoute)، توجيهه إلى الصفحة الرئيسية',
              tag: 'AppRouter.onGenerateRoute',
            );

            nextRoute = RouteConstants.home;
          }
        }
        // إذا كان المستخدم غير مسجل الدخول
        else {
          // إذا كان المسار المطلوب ليس أحد مسارات المصادقة
          if (!unauthenticatedRoutes.contains(nextRoute)) {
            LoggerService.debug(
              'المستخدم غير مسجل الدخول ويحاول الوصول إلى مسار محمي ($nextRoute)',
              tag: 'AppRouter.onGenerateRoute',
            );

            // إذا كان المستخدم قد رأى صفحة الترحيب من قبل، توجيهه إلى صفحة تسجيل الدخول
            if (isOnBoarding) {
              LoggerService.debug(
                'المستخدم رأى صفحة الترحيب من قبل، توجيهه إلى صفحة تسجيل الدخول',
                tag: 'AppRouter.onGenerateRoute',
              );

              nextRoute = RouteConstants.login;
            }
            // إذا كان المستخدم لم ير صفحة الترحيب من قبل، توجيهه إلى صفحة الترحيب
            else {
              LoggerService.debug(
                'المستخدم لم ير صفحة الترحيب من قبل، توجيهه إلى صفحة الترحيب',
                tag: 'AppRouter.onGenerateRoute',
              );

              nextRoute = RouteConstants.onBoarding;
            }
          }
        }

        // تسجيل إضافي للتصحيح
        LoggerService.debug(
          'المسار النهائي: $nextRoute',
          tag: 'AppRouter.onGenerateRoute',
        );
      } catch (e) {
        LoggerService.error(
          'خطأ في توليد المسار: $e',
          error: e,
          tag: 'AppRouter.onGenerateRoute',
        );
      }

      // التعامل مع المسارات التي تحتاج إلى معلمات
      if (nextRoute == RouteConstants.courseDetails &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;
        final courseId = args['courseId'] as String;
        return MaterialPageRoute(
          builder: (context) => CourseDetailsPage(courseId: courseId),
        );
      }
      // مسار عرض فيديو الدرس
      else if (nextRoute == RouteConstants.lessonVideo &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;
        final lesson = args['lesson'] as LessonModel;
        return MaterialPageRoute(
          builder: (context) => LessonVideoPage(lesson: lesson),
        );
      }
      // مسار صفحة المقالات الزراعية
      else if (nextRoute == RouteConstants.articles) {
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _educationProviders(),
                child: const ArticlesPage(),
              ),
        );
      }
      // مسار تفاصيل المقالة
      else if (nextRoute == RouteConstants.articleDetails &&
          settings.arguments != null) {
        final articleId = settings.arguments as String;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _educationProviders(),
                child: ArticleDetailsPage(articleId: articleId),
              ),
        );
      }
      // مسار تفاصيل الاختبار
      else if (nextRoute == RouteConstants.quizDetails &&
          settings.arguments != null) {
        final quizId = settings.arguments as String;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: [
                  ..._educationProviders(),
                  BlocProvider<QuizExecutionCubit>(
                    create: (context) => QuizExecutionCubit(),
                  ),
                ],
                child: QuizDetailsPage(quizId: quizId),
              ),
        );
      }
      // مسار تنفيذ الاختبار
      else if (nextRoute == RouteConstants.quizExecution &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;
        final quizId = args['quizId'] as String;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: [
                  BlocProvider<QuizExecutionCubit>(
                    create: (context) => QuizExecutionCubit(),
                  ),
                ],
                child: QuizExecutionPage(quizId: quizId),
              ),
        );
      }
      // مسار نتائج الاختبار
      else if (nextRoute == RouteConstants.quizResult &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;
        final quiz = args['quiz'] as QuizModel;
        final userAnswers = args['userAnswers'] as List<int?>;
        final earnedPoints = args['earnedPoints'] as int;
        final timeSpentSeconds = args['timeSpentSeconds'] as int;
        return MaterialPageRoute(
          builder:
              (context) => QuizResultPage(
                quiz: quiz,
                userAnswers: userAnswers,
                earnedPoints: earnedPoints,
                timeSpentSeconds: timeSpentSeconds,
              ),
        );
      }
      // مسار صفحة دروس الدورة
      else if (nextRoute == RouteConstants.adminCourseLessons &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;

        LoggerService.debug(
          '🔥 onGenerateRoute - AdminCourseLessons - استقبال المعلمات:',
          tag: 'AppRouter',
        );
        LoggerService.debug('📦 المعلمات المستلمة: $args', tag: 'AppRouter');
        LoggerService.debug(
          '📚 معرف الدورة: "${args['courseId'] ?? ''}"',
          tag: 'AppRouter',
        );
        LoggerService.debug(
          '📝 عنوان الدورة: "${args['courseTitle'] ?? ''}"',
          tag: 'AppRouter',
        );

        final courseId = args['courseId'] as String? ?? '';
        final courseTitle = args['courseTitle'] as String? ?? '';

        if (courseId.isEmpty) {
          LoggerService.error(
            '❌ خطأ حرج: معرف الدورة فارغ في onGenerateRoute!',
            tag: 'AppRouter',
          );
        }

        return MaterialPageRoute(
          builder:
              (context) => AdminAuthWrapper(
                child: AdminCourseLessonsPage(
                  courseId: courseId,
                  courseTitle: courseTitle,
                ),
              ),
        );
      }
      // مسار صفحة نموذج الدرس
      else if (nextRoute == RouteConstants.adminLessonForm &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;

        LoggerService.debug(
          '🔥 onGenerateRoute - AdminLessonForm - استقبال المعلمات:',
          tag: 'AppRouter',
        );
        LoggerService.debug('📦 المعلمات المستلمة: $args', tag: 'AppRouter');
        LoggerService.debug(
          '📚 معرف الدورة: "${args['courseId'] ?? ''}"',
          tag: 'AppRouter',
        );
        LoggerService.debug(
          '📝 عنوان الدورة: "${args['courseTitle'] ?? ''}"',
          tag: 'AppRouter',
        );
        LoggerService.debug(
          '🆔 معرف الدرس: "${args['lessonId'] ?? 'null'}"',
          tag: 'AppRouter',
        );

        final courseId = args['courseId'] as String? ?? '';
        final courseTitle = args['courseTitle'] as String? ?? '';
        final lessonId = args['lessonId'] as String?;

        if (courseId.isEmpty) {
          LoggerService.error(
            '❌ خطأ حرج: معرف الدورة فارغ في onGenerateRoute للدرس!',
            tag: 'AppRouter',
          );
        }

        return MaterialPageRoute(
          builder:
              (context) => AdminAuthWrapper(
                child: AdminLessonFormPage(
                  courseId: courseId,
                  courseTitle: courseTitle,
                  lessonId: lessonId,
                ),
              ),
        );
      }
      // مسار صفحة نموذج النصيحة اليومية
      else if (nextRoute == RouteConstants.adminDailyTipForm) {
        final args = settings.arguments as DailyTipModel?;

        return MaterialPageRoute(
          builder:
              (context) => AdminAuthWrapper(
                child: AdminDailyTipFormPage(
                  tip: args, // تمرير بيانات النصيحة للتعديل (إذا كانت موجودة)
                ),
              ),
        );
      }
      // مسار صفحة نموذج المقالة
      else if (nextRoute == RouteConstants.adminArticleForm) {
        final args = settings.arguments;

        return MaterialPageRoute(
          builder:
              (context) => AdminAuthWrapper(
                child: AdminArticleFormPage(
                  article:
                      args != null
                          ? args as ArticleModel
                          : null, // تمرير بيانات المقالة للتعديل (إذا كانت موجودة)
                ),
              ),
        );
      }
      // مسار صفحة التعليم والتدريب مع مزودي BLoC
      else if (nextRoute == RouteConstants.education) {
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _educationProviders(),
                child: const Education(),
              ),
        );
      }
      // مسار صفحة السلة
      else if (nextRoute == RouteConstants.cart) {
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _marketingProductsProviders(),
                child: const CartPage(),
              ),
        );
      }
      // مسار صفحة البحث في المنتجات
      else if (nextRoute == RouteConstants.searchProducts) {
        final initialQuery = settings.arguments as String?;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _marketingProductsProviders(),
                child: SearchProductsPage(initialQuery: initialQuery),
              ),
        );
      }
      // مسار تفاصيل المنتج
      else if (nextRoute == RouteConstants.productDetails &&
          settings.arguments != null) {
        // التعامل مع نوعين من المعاملات: ProductEntity أو Map مع productId
        if (settings.arguments is ProductEntity) {
          final product = settings.arguments as ProductEntity;
          return MaterialPageRoute(
            builder:
                (context) => MultiBlocProvider(
                  providers: _marketingProductsProviders(),
                  child: ProductDetailsPage(product: product),
                ),
          );
        } else if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          final productId = args['productId'] as String?;
          if (productId != null) {
            return MaterialPageRoute(
              builder:
                  (context) => MultiBlocProvider(
                    providers: _marketingProductsProviders(),
                    child: ProductDetailsPage(productId: productId),
                  ),
            );
          }
        }
      }
      // مسار إضافة منتج جديد
      else if (nextRoute == RouteConstants.addProduct) {
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _marketingProductsProviders(),
                child: AddProductPage(
                  farmerId: args?['farmerId'] ?? '',
                  farmerName: args?['farmerName'] ?? '',
                ),
              ),
        );
      }
      // مسار تعديل المنتج
      else if (nextRoute == RouteConstants.editProduct) {
        final args = settings.arguments as Map<String, dynamic>?;
        if (args != null) {
          // التعامل مع ProductModel مباشرة أو productId + farmerId
          final product = args['product'] as ProductModel?;
          final productId = args['productId'] as String?;
          final farmerId = args['farmerId'] as String?;

          if (product != null) {
            // تحويل ProductModel إلى ProductEntity
            final productEntity = _convertProductModelToEntity(product);
            return MaterialPageRoute(
              builder:
                  (context) => MultiBlocProvider(
                    providers: _marketingProductsProviders(),
                    child: EditProductPage(product: productEntity),
                  ),
            );
          } else if (productId != null && farmerId != null) {
            return MaterialPageRoute(
              builder:
                  (context) => MultiBlocProvider(
                    providers: _marketingProductsProviders(),
                    child: EditProductPage(
                      productId: productId,
                      farmerId: farmerId,
                    ),
                  ),
            );
          }
        }
      }
      // مسار صفحة الدفع
      else if (nextRoute == RouteConstants.checkout) {
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _marketingProductsProviders(),
                child: const Scaffold(
                  body: Center(
                    child: Text(
                      'صفحة الدفع قيد التطوير',
                      style: TextStyle(fontSize: 18),
                    ),
                  ),
                ),
              ),
        );
      }
      // مسار تاريخ الطلبات
      else if (nextRoute == RouteConstants.orderHistory) {
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _marketingProductsProviders(),
                child: const Scaffold(
                  body: Center(
                    child: Text(
                      'تاريخ الطلبات قيد التطوير',
                      style: TextStyle(fontSize: 18),
                    ),
                  ),
                ),
              ),
        );
      }
      // مسار إدارة منتجات المزارع
      else if (nextRoute == RouteConstants.myProducts) {
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _marketingProductsProviders(),
                child: FarmerProductsPage(
                  farmerId: args?['farmerId'] ?? '',
                  farmerName: args?['farmerName'] ?? '',
                ),
              ),
        );
      }
      // مسارات شبكة الخبراء الزراعيين
      else if (nextRoute == RouteConstants.expertProfile &&
          settings.arguments != null) {
        final expert = settings.arguments as AgriculturalExpertModel;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: ExpertProfilePage(
                  expert: expert,
                  currentFarmerId: _getCurrentUserId(),
                  currentFarmerName: _getCurrentUserName(),
                ),
              ),
        );
      } else if (nextRoute == RouteConstants.expertRatings &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;
        final expert = args['expert'] as AgriculturalExpertModel;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: ExpertRatingsPage(
                  expert: expert,
                  currentFarmerId: args['currentFarmerId'],
                ),
              ),
        );
      } else if (nextRoute == RouteConstants.consultationRequest &&
          settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>;
        final expert = args['expert'] as AgriculturalExpertModel;
        final farmerId = _getCurrentUserId();
        final farmerName = _getCurrentUserName();

        if (farmerId == null) {
          // إذا لم يكن المستخدم مسجل دخول، توجيهه لصفحة تسجيل الدخول
          return MaterialPageRoute(
            builder:
                (context) => const Scaffold(
                  body: Center(
                    child: Text('يجب تسجيل الدخول أولاً لطلب استشارة'),
                  ),
                ),
          );
        }

        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: ConsultationRequestPage(
                  expert: expert,
                  farmerId: farmerId,
                  farmerName: farmerName,
                ),
              ),
        );
      } else if (nextRoute == RouteConstants.consultationDetails &&
          settings.arguments != null) {
        final consultation = settings.arguments as ConsultationModel;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: ConsultationDetailsPage(
                  consultation: consultation,
                  expert: AgriculturalExpertModel(
                    id: consultation.expertId,
                    userId: consultation.expertId,
                    name: consultation.expertName,
                    profileImage: '',
                    specialization: 'خبير زراعي',
                    governorate: '',
                    city: '',
                    phone: '',
                    email: '',
                    rating: 0.0,
                    consultationsCount: 0,
                    isAvailable: true,
                    bio: '',
                    experienceYears: 0,
                    certifications: [],
                    joinedAt: DateTime.now(),
                    lastUpdated: DateTime.now(),
                  ),
                  currentFarmerId: consultation.farmerId,
                  currentFarmerName: consultation.farmerName,
                ),
              ),
        );
      } else if (nextRoute == RouteConstants.myConsultations) {
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: MyConsultationsPage(
                  farmerId: _getCurrentUserId() ?? 'unknown_user',
                  farmerName: _getCurrentUserName(),
                ),
              ),
        );
      } else if (nextRoute == RouteConstants.farmerConsultations &&
          settings.arguments != null) {
        final farmerId = settings.arguments as String;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: FarmerConsultationsPage(
                  farmerId: farmerId,
                  farmerName: _getCurrentUserName(),
                ),
              ),
        );
      } else if (nextRoute == RouteConstants.expertConsultations &&
          settings.arguments != null) {
        final expertId = settings.arguments as String;
        return MaterialPageRoute(
          builder:
              (context) => MultiBlocProvider(
                providers: _expertsProviders(),
                child: ExpertConsultationsPage(
                  expertId: expertId,
                  expertName: 'خبير زراعي', // سيتم تحديثه من البيانات
                ),
              ),
        );
      }
      // التعامل مع المسارات العادية
      else {
        Iterable<RouteModel> result = routes().where(
          (element) => element.name == nextRoute,
        );
        if (result.isNotEmpty) {
          return MaterialPageRoute(builder: (context) => result.first.view);
        }
      }
    }

    return null;
  }

  /// قائمة مزودي BLoC/Cubit
  ///
  /// تقوم هذه الدالة بإنشاء قائمة بجميع مزودي BLoC/Cubit المستخدمة في التطبيق.
  /// يتم تنظيم المزودين حسب الميزات لتسهيل الصيانة.
  static List<BlocProvider> allBlocProviders() {
    return [
      // مزودي المصادقة
      _authProviders(),

      // مزودي المعالم الزراعية
      _landmarksProviders(),

      // مزودي المحاصيل الزراعية
      _cropsProviders(),

      // مزودي الطقس
      _weatherProviders(),

      // مزودي المنتدى المجتمعي
      _communityForumProviders(),

      // مزودي التعليم والتدريب
      _educationProviders(),

      // مزودي السوق الزراعي
      _marketingProductsProviders(),

      // مزودي الإدارة
      _adminProviders(),

      // مزودي البروفايل
      _profileProviders(),
    ].expand((providers) => providers).toList();
  }

  /// مزودي المصادقة
  static List<BlocProvider> _authProviders() {
    // إنشاء مستودع المصادقة مرة واحدة
    final authRepository = AuthRepository();

    // إنشاء AuthCubit مرة واحدة
    final authCubit = AuthCubit(authRepository);

    return [
      // مزود AuthCubit الرئيسي
      BlocProvider<AuthCubit>(create: (context) => authCubit),

      // مزود LoginCubit
      BlocProvider<LoginCubit>(
        create: (context) => LoginCubit(authRepository, authCubit),
      ),

      // مزود RegisterCubit
      BlocProvider<RegisterCubit>(
        create: (context) => RegisterCubit(authRepository, authCubit),
      ),

      // مزود PhoneAuthCubit
      BlocProvider<PhoneAuthCubit>(create: (context) => PhoneAuthCubit()),

      // مزود RegisterProfileCubit
      BlocProvider<RegisterProfileCubit>(
        create: (context) => RegisterProfileCubit(authCubit),
      ),
    ];
  }

  /// مزودي المعالم الزراعية
  static List<BlocProvider> _landmarksProviders() {
    return [
      BlocProvider<InformationCubit>(
        create: (context) {
          // إنشاء كيوبت المعلومات
          final cubit = InformationCubit();

          // تحميل البيانات الأساسية فقط (المعالم الزراعية)
          cubit.loadInitialData();

          return cubit;
        },
      ),
    ];
  }

  /// مزودي المحاصيل الزراعية
  static List<BlocProvider> _cropsProviders() {
    return [
      BlocProvider<CropsCubit>(
        // إنشاء CropsCubit بدون تحميل البيانات مباشرة
        // سيتم تحميل البيانات عند الحاجة إليها في صفحة المحاصيل الزراعية
        create: (context) => CropsCubit(),
      ),
    ];
  }

  /// مزودي الطقس
  static List<BlocProvider> _weatherProviders() {
    return [
      BlocProvider<WeatherCubit>(
        create: (context) {
          final dio = Dio(
            BaseOptions(
              baseUrl: 'https://api.openweathermap.org/data/2.5/',
              connectTimeout: const Duration(seconds: 10),
              receiveTimeout: const Duration(seconds: 10),
            ),
          );
          final weatherService = WeatherService(dio: dio);
          return WeatherCubit(weatherService: weatherService);
        },
      ),
    ];
  }

  /// مزودي المنتدى المجتمعي
  static List<BlocProvider> _communityForumProviders() {
    return [
      BlocProvider<PostsCubit>(
        create: (context) {
          final storageRepository = StorageRepositoryFactory.create();
          return PostsCubit(
            postRepository: PostRepositoryFactory.create(storageRepository),
            storageRepository: storageRepository,
          );
        },
      ),
    ];
  }

  /// مزودي التعليم والتدريب
  static List<BlocProvider> _educationProviders() {
    // إنشاء حاوية حقن التبعية للتعليم والتدريب
    final getIt = GetIt.instance;

    // تسجيل SharedPreferences في GetIt
    if (!getIt.isRegistered<SharedPreferences>()) {
      getIt.registerSingletonAsync<SharedPreferences>(() async {
        return await SharedPreferences.getInstance();
      });

      // انتظار اكتمال تسجيل SharedPreferences
      getIt.isReady<SharedPreferences>().then((_) {
        // تسجيل التبعيات بعد اكتمال تسجيل SharedPreferences
        registerEducationDependencies(getIt);
      });
    } else {
      // تسجيل التبعيات إذا كان SharedPreferences مسجلاً بالفعل
      registerEducationDependencies(getIt);
    }

    return [
      BlocProvider<EducationCubit>(
        create: (context) => getIt<EducationCubit>(),
      ),
      BlocProvider<ArticlesCubit>(create: (context) => getIt<ArticlesCubit>()),
    ];
  }

  /// مزودي السوق الزراعي
  static List<BlocProvider> _marketingProductsProviders() {
    return [
      BlocProvider<ProductsCubit>(
        create: (context) {
          try {
            return GetIt.instance<ProductsCubit>();
          } catch (e) {
            // إذا لم يكن مسجلاً، أنشئه مباشرة
            final repository = MarketingProductsRepository(
              productsFirebaseService: ProductsFirebaseService(),
              categoriesFirebaseService: CategoriesFirebaseService(),
              cartFirebaseService: CartFirebaseService(),
              ordersFirebaseService: OrdersFirebaseService(),
              reviewsFirebaseService: ReviewsFirebaseService(),
              localService: MarketingProductsLocalService(),
              connectivity: Connectivity(),
            );

            return ProductsCubit(
              getProducts: GetProducts(repository),
              getProductsStream: GetProductsStream(repository),
            );
          }
        },
      ),
      BlocProvider<ProductDetailsCubit>(
        create: (context) {
          try {
            return GetIt.instance<ProductDetailsCubit>();
          } catch (e) {
            // إذا لم يكن مسجلاً، أنشئه مباشرة
            return ProductDetailsCubit(
              getProductDetails: GetProductDetails(
                MarketingProductsRepository(
                  productsFirebaseService: ProductsFirebaseService(),
                  categoriesFirebaseService: CategoriesFirebaseService(),
                  cartFirebaseService: CartFirebaseService(),
                  ordersFirebaseService: OrdersFirebaseService(),
                  reviewsFirebaseService: ReviewsFirebaseService(),
                  localService: MarketingProductsLocalService(),
                  connectivity: Connectivity(),
                ),
              ),
            );
          }
        },
      ),
      BlocProvider<CartCubit>(
        create: (context) {
          try {
            return GetIt.instance<CartCubit>();
          } catch (e) {
            // إذا لم يكن مسجلاً، أنشئه مباشرة
            return CartCubit(
              manageCart: ManageCart(
                MarketingProductsRepository(
                  productsFirebaseService: ProductsFirebaseService(),
                  categoriesFirebaseService: CategoriesFirebaseService(),
                  cartFirebaseService: CartFirebaseService(),
                  ordersFirebaseService: OrdersFirebaseService(),
                  reviewsFirebaseService: ReviewsFirebaseService(),
                  localService: MarketingProductsLocalService(),
                  connectivity: Connectivity(),
                ),
              ),
            );
          }
        },
      ),
      BlocProvider<FarmerProductsCubit>(
        create: (context) {
          try {
            return GetIt.instance<FarmerProductsCubit>();
          } catch (e) {
            // إذا لم يكن مسجلاً، أنشئه مباشرة
            return FarmerProductsCubit(
              manageFarmerProducts: ManageFarmerProducts(
                MarketingProductsRepository(
                  productsFirebaseService: ProductsFirebaseService(),
                  categoriesFirebaseService: CategoriesFirebaseService(),
                  cartFirebaseService: CartFirebaseService(),
                  ordersFirebaseService: OrdersFirebaseService(),
                  reviewsFirebaseService: ReviewsFirebaseService(),
                  localService: MarketingProductsLocalService(),
                  connectivity: Connectivity(),
                ),
              ),
            );
          }
        },
      ),
      BlocProvider<ProductsViewCubit>(
        create: (context) {
          try {
            return GetIt.instance<ProductsViewCubit>();
          } catch (e) {
            // إذا لم يكن مسجلاً، أنشئه مباشرة
            return ProductsViewCubit();
          }
        },
      ),
    ];
  }

  /// مزودي الإدارة
  static List<BlocProvider> _adminProviders() {
    // إنشاء حاوية حقن التبعية للإدارة
    final getIt = GetIt.instance;

    // تسجيل التبعيات إذا لم تكن مسجلة بالفعل
    if (!getIt.isRegistered<AdminUsersCubit>()) {
      // تسجيل التبعيات بشكل متزامن
      // ملاحظة: يجب أن تكون التبعيات مسجلة مسبقاً في التطبيق
      _initAdminDependenciesSync(getIt);
    }

    return [
      BlocProvider<AdminUsersCubit>(
        create: (context) => getIt<AdminUsersCubit>(),
      ),
    ];
  }

  /// تهيئة تبعيات الإدارة بشكل متزامن
  /// تهيئة تبعيات الإدارة بشكل متزامن
  static void _initAdminDependenciesSync(GetIt sl) {
    try {
      // تسجيل StorageRepository إذا لم يكن مسجلاً
      if (!sl.isRegistered<StorageRepositoryInterface>()) {
        sl.registerLazySingleton<StorageRepositoryInterface>(
          () => StorageRepositoryFactory.create(),
        );
      }

      // تسجيل AdminUsersCubit
      if (!sl.isRegistered<AdminUsersCubit>()) {
        sl.registerFactory<AdminUsersCubit>(
          () => AdminUsersCubit(
            getUsersList:
                sl.isRegistered<GetUsersList>() ? sl<GetUsersList>() : null,
            updateUserRole:
                sl.isRegistered<UpdateUserRole>() ? sl<UpdateUserRole>() : null,
            storageRepository: sl<StorageRepositoryInterface>(),
          ),
        );
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في تهيئة تبعيات الإدارة',
        error: e,
        tag: 'AppRouter',
      );
    }
  }

  /// مزودي البروفايل
  static List<BlocProvider> _profileProviders() {
    return [
      BlocProvider<ProfileCubit>(
        create:
            (context) => ProfileCubit(
              userRepository: UserRepository(),
              authCubit: context.read<AuthCubit>(),
            ),
      ),
    ];
  }

  /// مزودي شبكة الخبراء الزراعيين
  static List<BlocProvider> _expertsProviders() {
    final expertsRepository = ExpertsRepository();

    return [
      BlocProvider<ExpertsCubit>(
        create: (context) => ExpertsCubit(GetExperts(expertsRepository)),
      ),
      BlocProvider<ExpertsPaginationCubit>(
        create:
            (context) => ExpertsPaginationCubit(GetExperts(expertsRepository)),
      ),
      BlocProvider<FavoritesCubit>(create: (context) => FavoritesCubit()),
      BlocProvider<ConsultationsCubit>(
        create:
            (context) => ConsultationsCubit(
              CreateConsultation(expertsRepository),
              GetConsultations(expertsRepository),
              ManageConsultation(expertsRepository),
            ),
      ),
      BlocProvider<ConsultationFormCubit>(
        create:
            (context) =>
                ConsultationFormCubit(CreateConsultation(expertsRepository)),
      ),
      BlocProvider<ExpertRatingsCubit>(
        create:
            (context) => ExpertRatingsCubit(
              AddExpertRating(repository: expertsRepository),
              GetExpertRatings(repository: expertsRepository),
              ManageExpertRating(expertsRepository),
            ),
      ),
    ];
  }

  /// تهيئة الموجه
  ///
  /// يقوم بتهيئة الموجه والتحقق من حالة تسجيل الدخول.
  /// ملاحظة: هذه الدالة لا تقوم بتصحيح التناقضات بين Firebase والتخزين المحلي،
  /// حيث يتم ذلك في `_synchronizeAuthenticationState` في `AppInitializationService`.
  static Future<void> init() async {
    try {
      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'بدء تهيئة AppRouter. uid = "$uid", isOnBoarding = $isOnBoarding',
        tag: 'AppRouter.init',
      );

      // تسجيل إضافي للتصحيح
      LoggerService.debug(
        'اكتملت تهيئة AppRouter. uid = "$uid", isOnBoarding = $isOnBoarding',
        tag: 'AppRouter.init',
      );
    } catch (e) {
      LoggerService.error(
        'فشل في تهيئة AppRouter: $e',
        error: e,
        tag: 'AppRouter.init',
      );
    }
  }

  /// التنقل إلى مسار
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  /// - [replace]: ما إذا كان يجب استبدال المسار الحالي (اختياري)
  static void navigateTo(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool replace = false,
  }) {
    if (replace) {
      Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
    } else {
      Navigator.pushNamed(context, routeName, arguments: arguments);
    }
  }

  /// التنقل إلى المسار الرئيسي
  ///
  /// يقوم بتوجيه المستخدم إلى الصفحة الرئيسية وإزالة جميع المسارات السابقة.
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  static void navigateToHome(BuildContext context) {
    try {
      LoggerService.debug(
        'بدء التنقل إلى الصفحة الرئيسية. uid = "$uid"',
        tag: 'AppRouter.navigateToHome',
      );

      // التحقق من أن uid غير فارغ
      if (uid.isEmpty) {
        LoggerService.warning(
          'محاولة التنقل إلى الصفحة الرئيسية ولكن uid فارغ. توجيه المستخدم إلى صفحة تسجيل الدخول بدلاً من ذلك.',
          tag: 'AppRouter.navigateToHome',
        );

        // توجيه المستخدم إلى صفحة تسجيل الدخول بدلاً من الصفحة الرئيسية
        Navigator.pushNamedAndRemoveUntil(
          context,
          RouteConstants.login,
          (route) => false,
        );

        return;
      }

      // توجيه المستخدم إلى الصفحة الرئيسية
      Navigator.pushNamedAndRemoveUntil(
        context,
        RouteConstants.home,
        (route) => false,
      );

      LoggerService.debug(
        'تم التنقل إلى الصفحة الرئيسية بنجاح',
        tag: 'AppRouter.navigateToHome',
      );
    } catch (e) {
      LoggerService.error(
        'فشل في التنقل إلى الصفحة الرئيسية: $e',
        error: e,
        tag: 'AppRouter.navigateToHome',
      );

      // محاولة التنقل إلى الصفحة الرئيسية بطريقة أخرى
      try {
        Navigator.pushReplacementNamed(context, RouteConstants.home);
      } catch (e2) {
        LoggerService.error(
          'فشل في التنقل إلى الصفحة الرئيسية بالطريقة البديلة: $e2',
          error: e2,
          tag: 'AppRouter.navigateToHome',
        );
      }
    }
  }

  /// التنقل إلى مسار وإزالة جميع المسارات السابقة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [routeName]: اسم المسار
  /// - [arguments]: معلمات المسار (اختياري)
  static void navigateAndRemoveUntil(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// الرجوع إلى المسار السابق
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [result]: نتيجة الرجوع (اختياري)
  static void goBack(BuildContext context, {dynamic result}) {
    // التحقق مما إذا كان يمكن الرجوع
    if (Navigator.canPop(context)) {
      // التحقق من المسار الحالي
      final currentRoute = ModalRoute.of(context)?.settings.name;

      // تسجيل المسار الحالي للتصحيح
      LoggerService.debug(
        'محاولة الرجوع من المسار: $currentRoute',
        tag: 'AppRouter',
      );

      // إذا كان المستخدم في صفحة تسجيل الدخول وقد رأى صفحة الترحيب من قبل
      if (currentRoute == RouteConstants.login && isOnBoarding) {
        // منع الرجوع إلى صفحة الترحيب
        LoggerService.debug(
          'منع الرجوع من صفحة تسجيل الدخول إلى صفحة الترحيب',
          tag: 'AppRouter',
        );

        // استخدام WillPopScope في الصفحة نفسها لمعالجة الرجوع
        // لا نحتاج لعمل أي شيء هنا لأن الصفحة ستتعامل مع الرجوع
      } else {
        // الرجوع بشكل طبيعي
        Navigator.pop(context, result);
      }
    } else {
      // لا يمكن الرجوع، عرض مربع حوار للخروج من التطبيق
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
              contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
              actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              title: Text(
                'الخروج من التطبيق',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: AssetsFonts.messiri,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              content: Text(
                'هل تريد الخروج من التطبيق؟',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: AssetsFonts.messiri,
                  fontSize: 16,
                  color: Colors.black54,
                ),
              ),
              actionsAlignment: MainAxisAlignment.spaceEvenly,
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 10,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                    backgroundColor: Colors.white,
                  ),
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      fontFamily: AssetsFonts.messiri,
                      fontSize: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    // إغلاق مربع الحوار
                    Navigator.pop(context);

                    // إغلاق التطبيق
                    SystemNavigator.pop();
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 10,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: AppColors.primary,
                  ),
                  child: Text(
                    'خروج',
                    style: TextStyle(
                      fontFamily: AssetsFonts.messiri,
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
      );
    }
  }

  /// تحويل ProductModel إلى ProductEntity
  static ProductEntity _convertProductModelToEntity(ProductModel model) {
    return ProductEntity(
      id: model.id,
      name: model.name,
      description: model.description,
      price: model.price,
      categoryId: model.categoryId,
      categoryName: model.categoryName,
      images: model.images,
      quantity: model.quantity,
      unit: model.unit,
      farmerId: model.farmerId,
      farmerName: model.farmerName,
      farmerPhone: model.farmerPhone,
      location: model.location,
      status: _convertProductStatus(model.status),
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
      isOrganic: model.isOrganic,
      rating: model.rating,
      reviewsCount: model.reviewsCount,
      tags: model.tags,
      isFeatured: model.isFeatured,
      notes: model.notes,
    );
  }

  /// تحويل ProductStatus إلى ProductStatusEntity
  static ProductStatusEntity _convertProductStatus(ProductStatus status) {
    switch (status) {
      case ProductStatus.available:
        return ProductStatusEntity.available;
      case ProductStatus.outOfStock:
        return ProductStatusEntity.outOfStock;
      case ProductStatus.pending:
        return ProductStatusEntity.pending;
      case ProductStatus.deleted:
        return ProductStatusEntity.deleted;
    }
  }
}

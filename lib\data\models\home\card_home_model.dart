

import '../../../imports.dart';

class CardHomeModel {
  final String titel;
  final IconData icon;
  final String router;

  const CardHomeModel({
    required this.titel,
    required this.icon,
    required this.router,
  });
}

const cardHome = [
  CardHomeModel(
    titel: 'الطقس',
    icon: Icons.cloud,
    router: RouteConstants.weather,
  ),
  CardHomeModel(
    titel: 'المحاصيل الزراعية',
    icon: Icons.agriculture,
    router: RouteConstants.agriculturalCrops,
  ),
  CardHomeModel(
    titel: 'المعالم الزراعية',
    icon: Icons.landscape,
    router: RouteConstants.landmarks,
  ),
  CardHomeModel(
    titel: 'المنتدى الزراعي',
    icon: Icons.forum,
    router: RouteConstants.communityForum,
  ),
  CardHomeModel(
    titel: "مركز التعلم الزراعي",
    icon: Icons.school,
    router: RouteConstants.education,
  ),
  CardHomeModel(
    titel: 'مركز الخدمات الزراعية',
    icon: Icons.support_agent,
    router: RouteConstants.government,
  ),
  CardHomeModel(
    titel: 'السوق الزراعي للمحاصيل',
    icon: Icons.shopping_cart,
    router: RouteConstants.marketingProducts,
  ),
  CardHomeModel(
    titel: 'نظام إدارة صحة المحاصيل',
    icon: Icons.bug_report,
    router: RouteConstants.pestsAndDiseases,
  ),
  CardHomeModel(
    titel: 'شبكة الخبراء الزراعيين',
    icon: Icons.engineering,
    router: RouteConstants.reachEngineer,
  ),
];

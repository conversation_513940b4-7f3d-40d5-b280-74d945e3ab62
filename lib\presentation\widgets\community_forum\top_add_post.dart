import 'package:agriculture/presentation/widgets/shared/user_profile_avatar.dart';


import '../../../imports.dart';

class TopAddPost extends StatefulWidget {
  /// دالة يتم استدعاؤها عند النقر على مكون إضافة منشور جديد
  final VoidCallback? onTap;

  const TopAddPost({super.key, this.onTap});

  @override
  State<TopAddPost> createState() => _TopAddPostState();
}

class _TopAddPostState extends State<TopAddPost>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.03).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AssetsColors.primary.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: _isHovered ? 3 : 1,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(16),
                  splashColor: AssetsColors.primary.withOpacity(0.1),
                  highlightColor: AssetsColors.primary.withOpacity(0.05),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    child: Row(
                      children: [
                        // صورة المستخدم
                        UserProfileAvatar(
                          radius: 20.0,
                          backgroundColor: AssetsColors.primary.withOpacity(
                            0.1,
                          ),
                          iconColor: AssetsColors.primary,
                          showBorder: true,
                          borderColor: AssetsColors.primary.withOpacity(0.3),
                          onTap: widget.onTap,
                        ),
                        const SizedBox(width: 12),

                        // مربع النص
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              border: Border.all(
                                color:
                                    _isHovered
                                        ? AssetsColors.primary
                                        : Colors.grey.shade300,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(50.0),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 10,
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.edit,
                                  size: 18,
                                  color:
                                      _isHovered
                                          ? AssetsColors.primary
                                          : Colors.grey.shade600,
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    'قم بكتابة أفكارك...',
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    style: TextStyles.of(context).bodyMedium(
                                      fontFamily: AssetsFonts.cairo,
                                      color:
                                          _isHovered
                                              ? AssetsColors.primary
                                              : Colors.grey.shade600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(width: 12),

                        // أزرار الوسائط
                        _buildMediaButton(Icons.image, 'صورة'),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMediaButton(IconData icon, String tooltip) {
    return Tooltip(
      message: 'إضافة $tooltip',
      child: Container(
        decoration: BoxDecoration(
          color:
              _isHovered
                  ? AssetsColors.primary.withOpacity(0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: IconButton(
          onPressed: widget.onTap,
          icon: Icon(
            icon,
            size: 22,
            color: _isHovered ? AssetsColors.primary : Colors.grey.shade600,
          ),
          splashRadius: 24,
          tooltip: 'إضافة $tooltip',
        ),
      ),
    );
  }
}



import '../../../core/constants/weather/index.dart';
import '../../../data/models/weather/weather_alert_model.dart';
import '../../../imports.dart';
import '../../bloc/weather/weather_alerts_cubit.dart';

/// ويدجت عرض تنبيهات الطقس
///
/// يعرض التنبيهات الذكية للطقس مع إمكانية التفاعل
class WeatherAlertsWidget extends StatelessWidget {
  /// تفعيل العرض المضغوط
  final bool compact;

  /// الحد الأقصى لعدد التنبيهات المعروضة
  final int? maxAlerts;

  /// دالة عند النقر على التنبيه
  final Function(WeatherAlertModel)? onAlertTap;

  const WeatherAlertsWidget({
    super.key,
    this.compact = false,
    this.maxAlerts,
    this.onAlertTap,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WeatherAlertsCubit, WeatherAlertsState>(
      builder: (context, state) {
        if (state is WeatherAlertsLoading) {
          return _buildLoadingWidget();
        }

        if (state is WeatherAlertsError) {
          return _buildErrorWidget(state.message);
        }

        if (state is WeatherAlertsLoaded) {
          if (state.alerts.isEmpty) {
            return _buildNoAlertsWidget();
          }

          return _buildAlertsWidget(context, state);
        }

        return const SizedBox.shrink();
      },
    );
  }

  /// بناء ويدجت التحميل
  Widget _buildLoadingWidget() {
    return Container(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      child: Row(
        children: [
          SizedBox(
            width: WeatherDimensions.mediumIconSize,
            height: WeatherDimensions.mediumIconSize,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: WeatherColors.primaryBackground,
            ),
          ),
          SizedBox(width: WeatherDimensions.mediumMargin),
          Text(
            'جاري تحليل بيانات الطقس...',
            style: TextStyle(
              fontSize: WeatherDimensions.bodyFontSize,
              fontFamily: AssetsFonts.cairo,
              color: WeatherColors.lightText,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت الخطأ
  Widget _buildErrorWidget(String message) {
    return Container(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      decoration: BoxDecoration(
        color: WeatherColors.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        border: Border.all(
          color: WeatherColors.errorColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: WeatherColors.errorColor,
            size: WeatherDimensions.mediumIconSize,
          ),
          SizedBox(width: WeatherDimensions.mediumMargin),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: WeatherDimensions.bodyFontSize,
                fontFamily: AssetsFonts.cairo,
                color: WeatherColors.errorColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت عدم وجود تنبيهات
  Widget _buildNoAlertsWidget() {
    return Container(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      decoration: BoxDecoration(
        color: WeatherColors.successColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        border: Border.all(
          color: WeatherColors.successColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: WeatherColors.successColor,
            size: WeatherDimensions.mediumIconSize,
          ),
          SizedBox(width: WeatherDimensions.mediumMargin),
          Expanded(
            child: Text(
              'لا توجد تنبيهات طقس حالياً',
              style: TextStyle(
                fontSize: WeatherDimensions.bodyFontSize,
                fontFamily: AssetsFonts.cairo,
                color: WeatherColors.successColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت التنبيهات
  Widget _buildAlertsWidget(BuildContext context, WeatherAlertsLoaded state) {
    final alerts =
        maxAlerts != null
            ? state.alerts.take(maxAlerts!).toList()
            : state.alerts;

    // ترتيب التنبيهات حسب الأولوية والوقت
    alerts.sort((a, b) {
      final priorityComparison = b.priority.weight.compareTo(a.priority.weight);
      if (priorityComparison != 0) return priorityComparison;
      return b.createdAt.compareTo(a.createdAt);
    });

    if (compact) {
      return _buildCompactAlertsWidget(context, alerts);
    } else {
      return _buildFullAlertsWidget(context, alerts);
    }
  }

  /// بناء العرض المضغوط للتنبيهات
  Widget _buildCompactAlertsWidget(
    BuildContext context,
    List<WeatherAlertModel> alerts,
  ) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: alerts.length,
        itemBuilder: (context, index) {
          final alert = alerts[index];
          return Container(
            width: 280,
            margin: EdgeInsets.only(right: WeatherDimensions.mediumMargin),
            child: _buildAlertCard(context, alert, compact: true),
          );
        },
      ),
    );
  }

  /// بناء العرض الكامل للتنبيهات
  Widget _buildFullAlertsWidget(
    BuildContext context,
    List<WeatherAlertModel> alerts,
  ) {
    return Column(
      children:
          alerts
              .map(
                (alert) => Padding(
                  padding: EdgeInsets.only(
                    bottom: WeatherDimensions.mediumMargin,
                  ),
                  child: _buildAlertCard(context, alert),
                ),
              )
              .toList(),
    );
  }

  /// بناء بطاقة التنبيه
  Widget _buildAlertCard(
    BuildContext context,
    WeatherAlertModel alert, {
    bool compact = false,
  }) {
    final priorityColor = Color(alert.priority.colorValue);
    final typeColor = Color(alert.type.colorValue);

    return GestureDetector(
      onTap: () {
        if (onAlertTap != null) {
          onAlertTap!(alert);
        } else {
          _showAlertDetails(context, alert);
        }
      },
      child: Container(
        padding: EdgeInsets.all(WeatherDimensions.cardPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            WeatherDimensions.cardBorderRadius,
          ),
          border: Border.all(
            color: priorityColor.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: priorityColor.withValues(alpha: 0.1),
              blurRadius: WeatherDimensions.shadowBlurRadius,
              spreadRadius: WeatherDimensions.shadowSpreadRadius,
              offset: Offset(
                WeatherDimensions.shadowOffsetX,
                WeatherDimensions.shadowOffsetY,
              ),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس التنبيه
            Row(
              children: [
                // أيقونة نوع التنبيه
                Container(
                  padding: EdgeInsets.all(WeatherDimensions.smallPadding),
                  decoration: BoxDecoration(
                    color: typeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    alert.type.icon,
                    style: TextStyle(
                      fontSize: WeatherDimensions.mediumIconSize,
                    ),
                  ),
                ),
                SizedBox(width: WeatherDimensions.mediumMargin),

                // عنوان التنبيه
                Expanded(
                  child: Text(
                    alert.title,
                    style: TextStyle(
                      fontSize:
                          compact
                              ? WeatherDimensions.bodyFontSize
                              : WeatherDimensions.subtitleFontSize,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: WeatherColors.darkText,
                    ),
                    maxLines: compact ? 1 : 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // مؤشر الأولوية
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: priorityColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),

            if (!compact) ...[
              SizedBox(height: WeatherDimensions.mediumMargin),

              // رسالة التنبيه
              Text(
                alert.message,
                style: TextStyle(
                  fontSize: WeatherDimensions.bodyFontSize,
                  fontFamily: AssetsFonts.cairo,
                  color: WeatherColors.lightText,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: WeatherDimensions.mediumMargin),

              // الأنشطة المتأثرة
              Wrap(
                spacing: WeatherDimensions.smallMargin,
                children:
                    alert.affectedActivities
                        .take(3)
                        .map(
                          (activity) => Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: WeatherDimensions.smallPadding,
                              vertical: WeatherDimensions.smallPadding / 2,
                            ),
                            decoration: BoxDecoration(
                              color: typeColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getActivityName(activity),
                              style: TextStyle(
                                fontSize: WeatherDimensions.microFontSize,
                                fontFamily: AssetsFonts.cairo,
                                color: typeColor,
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل التنبيه
  void _showAlertDetails(BuildContext context, WeatherAlertModel alert) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => WeatherAlertDetailsSheet(alert: alert),
    );
  }

  /// الحصول على اسم النشاط
  String _getActivityName(AgriculturalActivity activity) {
    switch (activity) {
      case AgriculturalActivity.irrigation:
        return 'الري';
      case AgriculturalActivity.spraying:
        return 'الرش';
      case AgriculturalActivity.harvesting:
        return 'الحصاد';
      case AgriculturalActivity.planting:
        return 'الزراعة';
      case AgriculturalActivity.pruning:
        return 'التقليم';
      case AgriculturalActivity.fertilizing:
        return 'التسميد';
      case AgriculturalActivity.pestControl:
        return 'مكافحة الآفات';
      case AgriculturalActivity.fieldWork:
        return 'العمل الحقلي';
      case AgriculturalActivity.general:
        return 'عام';
    }
  }
}

/// ورقة تفاصيل التنبيه
class WeatherAlertDetailsSheet extends StatelessWidget {
  final WeatherAlertModel alert;

  const WeatherAlertDetailsSheet({super.key, required this.alert});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(WeatherDimensions.cardBorderRadius),
          topRight: Radius.circular(WeatherDimensions.cardBorderRadius),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(WeatherDimensions.extraLargePadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الورقة
            Row(
              children: [
                Text(
                  alert.type.icon,
                  style: TextStyle(
                    fontSize: WeatherDimensions.extraLargeIconSize,
                  ),
                ),
                SizedBox(width: WeatherDimensions.mediumMargin),
                Expanded(
                  child: Text(
                    alert.title,
                    style: TextStyle(
                      fontSize: WeatherDimensions.largeTitleFontSize,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: WeatherColors.darkText,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close),
                ),
              ],
            ),

            SizedBox(height: WeatherDimensions.largeMargin),

            // رسالة التنبيه
            Text(
              alert.message,
              style: TextStyle(
                fontSize: WeatherDimensions.bodyFontSize,
                fontFamily: AssetsFonts.cairo,
                color: WeatherColors.lightText,
                height: 1.5,
              ),
            ),

            SizedBox(height: WeatherDimensions.largeMargin),

            // التوصيات
            Text(
              'التوصيات:',
              style: TextStyle(
                fontSize: WeatherDimensions.subtitleFontSize,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: WeatherColors.darkText,
              ),
            ),

            SizedBox(height: WeatherDimensions.mediumMargin),

            ...alert.recommendations
                .map(
                  (recommendation) => Padding(
                    padding: EdgeInsets.only(
                      bottom: WeatherDimensions.smallMargin,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '• ',
                          style: TextStyle(
                            fontSize: WeatherDimensions.bodyFontSize,
                            color: WeatherColors.primaryBackground,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            recommendation,
                            style: TextStyle(
                              fontSize: WeatherDimensions.bodyFontSize,
                              fontFamily: AssetsFonts.cairo,
                              color: WeatherColors.lightText,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                ,

            SizedBox(height: WeatherDimensions.extraLargeMargin),

            // زر الإغلاق
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  context.read<WeatherAlertsCubit>().markAlertAsRead(alert.id);
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: WeatherColors.primaryBackground,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: WeatherDimensions.mediumPadding,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      WeatherDimensions.buttonBorderRadius,
                    ),
                  ),
                ),
                child: Text(
                  'فهمت',
                  style: TextStyle(
                    fontSize: WeatherDimensions.bodyFontSize,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

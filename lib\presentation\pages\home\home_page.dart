import 'package:carousel_slider/carousel_slider.dart';


import '../../../imports.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // تسجيل بداية بناء صفحة الهوم
    LoggerService.debug(
      'بدء بناء صفحة الهوم. uid = "$uid"',
      tag: 'HomePage',
    );

    // التحقق من أن المستخدم مسجل الدخول
    if (uid.isEmpty) {
      LoggerService.warning(
        'محاولة بناء صفحة الهوم ولكن uid فارغ. سيتم توجيه المستخدم إلى صفحة تسجيل الدخول.',
        tag: 'HomePage',
      );

      // تأخير التنقل لضمان اكتمال بناء الصفحة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          AppRouter.navigateTo(context, RouteConstants.login, replace: true);
        }
      });
    }
    return Scaffold(
      appBar: const HomeAppBar(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(
            left: 1.0,
            right: 1.0,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //************************الشريط في راس شاشة الهوم**************
              CarouselSlider.builder(
                itemCount: service.length,
                itemBuilder: (BuildContext context, int index, int realIndex) {
                  return CarousSlider(
                    index: index,
                    text: service[index].nameService,
                    image: service[index].image,
                  );
                },
                options: CarouselOptions(
                  height: 220.0,
                  initialPage: 0,
                  enableInfiniteScroll: true,
                  viewportFraction: 1.0,
                  reverse: false,
                  autoPlay: true,
                  autoPlayInterval: const Duration(seconds: 5),
                  autoPlayAnimationDuration: const Duration(seconds: 1),
                  autoPlayCurve: Curves.fastLinearToSlowEaseIn,
                  scrollDirection: Axis.horizontal,
                ),
              ),
              //****************************نص الخدمات*****************************,
              TextService(
                textLef: "الخدمات",
                textReit: "عرض جميع الخدمات",
              ),
              //*******************************بطاقة الخدمات******************

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 6.0),
                child: GridView(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                    maxCrossAxisExtent: 150,
                    mainAxisSpacing: 2.0,
                    crossAxisSpacing: 2.0,
                  ),
                  children: List.generate(
                    cardHome.length,
                    (index) => CardHomeItem(
                      text: cardHome[index].titel,
                      icon: cardHome[index].icon,
                      router: cardHome[index].router,
                    ),
                  ),
                ),
              ),

              //******************** نص الاخبار ****
              TextService(
                textLef: "الأخبار",
              ),

              //****************تصميم كرت الاخبار************
              ListView.separated(
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                itemBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3.0),
                  child: SizedBox(
                    child: ColoredBox(
                      color: Colors.grey.shade200,
                      child: InkWell(
                        onTap: () {
                          // navigateTO(
                          //     context,
                          //     WebViweScreen(
                          //       url: article['url'],
                          //     ));
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Row(
                            children: [
                              Container(
                                height: 90.0,
                                width: 90.0,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  image: DecorationImage(
                                    image: AssetImage(
                                        'assets/images/wether_icon/wether_suny.png'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              const SizedBox(
                                width: 20.0,
                              ),
                              Expanded(
                                child: SizedBox(
                                  height: 90.0,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          " h uuiefskjfsdfksdfafkjsalfdsiu اليمن تسجل اقوى دولة في الزراعة والانتاج حيث وتقوم بالزراعة من قديم الزمان",
                                          style: const TextStyle(
                                              fontSize: 14.0,
                                              fontWeight: FontWeight.w600),
                                          maxLines: 3,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      Text(
                                        ' 12/12/2025',
                                        style: const TextStyle(
                                          color: Colors.grey,
                                          fontSize: 10.0,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                separatorBuilder: (context, index) => Divider(
                  color: Colors.grey[300],
                  thickness: 2,
                  height: 1,
                  indent: 20,
                  //*soldan bosluk
                  endIndent: 20, //*sagdan bosluk
                ),
                itemCount: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

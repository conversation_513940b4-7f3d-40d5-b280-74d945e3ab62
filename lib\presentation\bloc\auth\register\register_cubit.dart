import 'package:equatable/equatable.dart';

import '../../../../imports.dart';

part 'register_state.dart';

/// كيوبت التسجيل
///
/// يدير حالة التسجيل ويوفر وظائف للتسجيل بطرق مختلفة:
/// - التسجيل بالبريد الإلكتروني وكلمة المرور
/// - التسجيل باستخدام Google
class RegisterCubit extends Cubit<RegisterState> {
  final AuthRepository _authRepository;
  final AuthCubit _authCubit;

  // وحدات التحكم في الحقول
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // البناء
  RegisterCubit(this._authRepository, this._authCubit)
    : super(RegisterInitial());

  @override
  Future<void> close() {
    emailController.dispose();
    passwordController.dispose();
    return super.close();
  }

  /// التسجيل بالبريد الإلكتروني وكلمة المرور
  Future<void> registerWithEmailAndPassword() async {
    // لا نعتمد على formKey.currentState?.validate() هنا
    // لأن النموذج قد يكون في شاشة أخرى

    final email = emailController.text;
    final password = passwordController.text;

    // التحقق من صحة البريد الإلكتروني وكلمة المرور
    if (email.isEmpty ||
        !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      emit(RegisterFailure("يرجى إدخال بريد إلكتروني صحيح"));
      return;
    }

    if (password.isEmpty || password.length < 6) {
      emit(RegisterFailure("كلمة المرور يجب أن تكون 6 أحرف على الأقل"));
      return;
    }

    LoggerService.debug(
      'بدء عملية التسجيل بالبريد الإلكتروني وكلمة المرور في RegisterCubit',
      tag: 'RegisterCubit',
    );

    emit(RegisterLoading());
    try {
      LoggerService.debug(
        'محاولة إنشاء حساب جديد: البريد الإلكتروني = $email',
        tag: 'RegisterCubit',
      );

      // استخدام AuthCubit للتسجيل
      await _authCubit.createUserWithEmailAndPassword(
        "", // الاسم سيتم إدخاله في صفحة ملف المستخدم
        email,
        password,
        "", // رقم الهاتف سيتم إدخاله في صفحة ملف المستخدم
      );

      // الحصول على المستخدم من AuthCubit
      final user = _authCubit.currentUser;

      if (user != null) {
        LoggerService.info(
          'تم إنشاء الحساب بنجاح: معرف المستخدم = ${user.id}',
          tag: 'RegisterCubit',
        );
        emit(RegisterSuccess(user));
      } else {
        LoggerService.warning(
          'فشل إنشاء الحساب: المستخدم فارغ',
          tag: 'RegisterCubit',
        );
        emit(RegisterFailure("فشل إنشاء الحساب"));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في إنشاء الحساب',
        error: e,
        tag: 'RegisterCubit',
      );

      String errorMessage = "حدث خطأ أثناء التسجيل";

      if (e.toString().contains("email-already-in-use")) {
        errorMessage = "البريد الإلكتروني مستخدم بالفعل";
      } else if (e.toString().contains("weak-password")) {
        errorMessage = "كلمة المرور ضعيفة جدًا";
      } else if (e.toString().contains("invalid-email")) {
        errorMessage = "البريد الإلكتروني غير صالح";
      } else if (e.toString().contains("network-request-failed")) {
        errorMessage = "تأكد من اتصالك بالإنترنت وحاول مرة أخرى";
      }

      emit(RegisterFailure(errorMessage));
    }
  }

  /// التسجيل باستخدام Google
  Future<void> registerWithGoogle() async {
    emit(RegisterLoading());
    try {
      LoggerService.debug(
        'بدء عملية التسجيل باستخدام حساب Google في RegisterCubit',
        tag: 'RegisterCubit',
      );

      // استخدام AuthCubit للتسجيل باستخدام Google
      await _authCubit.signInWithGoogle();

      // الحصول على المستخدم من AuthCubit
      final user = _authCubit.currentUser;

      if (user != null) {
        // تأكد من تعيين متغيرات الجلسة
        SharedPrefs.setBool('isAuth', true);
        SharedPrefs.setString('uid', user.id);

        LoggerService.debug(
          'تم تعيين متغيرات الجلسة: isAuth=true, uid=${user.id}',
          tag: 'RegisterCubit',
        );

        // تحديث المتغير العام uid
        uid = user.id;

        LoggerService.debug(
          'تم تحديث المتغير العام uid = "$uid"',
          tag: 'RegisterCubit',
        );
        LoggerService.info(
          'تم التسجيل باستخدام حساب Google بنجاح في RegisterCubit',
          tag: 'RegisterCubit',
        );
        emit(RegisterSuccess(user));
      } else {
        LoggerService.warning(
          'فشل التسجيل باستخدام حساب Google في RegisterCubit: المستخدم فارغ',
          tag: 'RegisterCubit',
        );
        emit(RegisterFailure("فشل التسجيل باستخدام Google"));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في التسجيل باستخدام حساب Google في RegisterCubit',
        error: e,
        tag: 'RegisterCubit',
      );

      String errorMessage = "حدث خطأ أثناء التسجيل باستخدام Google";

      if (e.toString().contains("network_error")) {
        errorMessage = "تأكد من اتصالك بالإنترنت وحاول مرة أخرى";
      } else if (e.toString().contains("popup_closed_by_user")) {
        errorMessage = "تم إغلاق نافذة التسجيل";
      } else if (e.toString().contains("sign_in_failed")) {
        errorMessage = "فشل التسجيل، يرجى المحاولة مرة أخرى";
      }

      emit(RegisterFailure(errorMessage));
    }
  }

  /// التسجيل باستخدام Facebook
  Future<void> registerWithFacebook() async {
    emit(RegisterLoading());
    try {
      LoggerService.debug(
        'بدء عملية التسجيل باستخدام حساب Facebook في RegisterCubit',
        tag: 'RegisterCubit',
      );

      // استخدام AuthCubit للتسجيل باستخدام Facebook
      // ملاحظة: يجب تنفيذ هذه الدالة في AuthCubit
      // await _authCubit.signInWithFacebook();

      // حاليًا، سنعيد حالة فشل لأن الميزة غير منفذة بعد
      LoggerService.warning(
        'التسجيل باستخدام Facebook غير منفذ بعد',
        tag: 'RegisterCubit',
      );
      emit(RegisterFailure("ميزة التسجيل باستخدام Facebook غير متاحة حاليًا"));

      /* عند تنفيذ الميزة، استخدم الكود التالي:

      // الحصول على المستخدم من AuthCubit
      final user = _authCubit.currentUser;

      if (user != null) {
        LoggerService.info(
          'تم التسجيل باستخدام حساب Facebook بنجاح في RegisterCubit',
          tag: 'RegisterCubit',
        );
        emit(RegisterSuccess(user));
      } else {
        LoggerService.warning(
          'فشل التسجيل باستخدام حساب Facebook في RegisterCubit: المستخدم فارغ',
          tag: 'RegisterCubit',
        );
        emit(RegisterFailure("فشل التسجيل باستخدام Facebook"));
      }
      */
    } catch (e) {
      LoggerService.error(
        'خطأ في التسجيل باستخدام حساب Facebook في RegisterCubit',
        error: e,
        tag: 'RegisterCubit',
      );

      String errorMessage = "حدث خطأ أثناء التسجيل باستخدام Facebook";

      if (e.toString().contains("network_error")) {
        errorMessage = "تأكد من اتصالك بالإنترنت وحاول مرة أخرى";
      } else if (e.toString().contains("cancelled")) {
        errorMessage = "تم إلغاء عملية التسجيل";
      } else if (e.toString().contains(
        "account_exists_with_different_credential",
      )) {
        errorMessage = "هذا البريد الإلكتروني مرتبط بحساب آخر";
      }

      emit(RegisterFailure(errorMessage));
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    emailController.clear();
    passwordController.clear();
    emit(RegisterInitial());
  }

  /// الحصول على مستودع المصادقة
  AuthRepository get authRepository => _authRepository;
}



import '../../../core/constants/weather/index.dart';
import '../../../imports.dart';

/// ويدجت حالة الخطأ للطقس
///
/// يعرض رسائل خطأ موحدة لجميع صفحات الطقس
/// مع إمكانية إعادة المحاولة وتخصيص الرسالة
class WeatherErrorWidget extends StatelessWidget {
  /// رسالة الخطأ المراد عرضها
  final String message;

  /// دالة إعادة المحاولة
  final VoidCallback? onRetry;

  /// نوع الخطأ لتخصيص العرض
  final WeatherErrorType errorType;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص
  final Color? textColor;

  /// إنشاء ويدجت حالة الخطأ
  const WeatherErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.errorType = WeatherErrorType.general,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: backgroundColor,
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(WeatherDimensions.extraLargePadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الخطأ
              _buildErrorIcon(),
              SizedBox(height: WeatherDimensions.largeMargin),

              // عنوان الخطأ
              _buildErrorTitle(context),
              SizedBox(height: WeatherDimensions.mediumMargin),

              // رسالة الخطأ
              _buildErrorMessage(context),
              SizedBox(height: WeatherDimensions.extraLargeMargin),

              // زر إعادة المحاولة
              if (onRetry != null) _buildRetryButton(context),

              // نصائح إضافية
              if (errorType != WeatherErrorType.general) ...[
                SizedBox(height: WeatherDimensions.largeMargin),
                _buildErrorTips(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة الخطأ
  Widget _buildErrorIcon() {
    IconData iconData;
    Color iconColor;

    switch (errorType) {
      case WeatherErrorType.network:
        iconData = Icons.wifi_off;
        iconColor = WeatherColors.errorColor;
        break;
      case WeatherErrorType.location:
        iconData = Icons.location_off;
        iconColor = WeatherColors.warningColor;
        break;
      case WeatherErrorType.permission:
        iconData = Icons.block;
        iconColor = WeatherColors.warningColor;
        break;
      case WeatherErrorType.server:
        iconData = Icons.cloud_off;
        iconColor = WeatherColors.errorColor;
        break;
      case WeatherErrorType.timeout:
        iconData = Icons.access_time;
        iconColor = WeatherColors.warningColor;
        break;
      case WeatherErrorType.general:
        iconData = Icons.error_outline;
        iconColor = WeatherColors.errorColor;
        break;
    }

    return Icon(
      iconData,
      size: WeatherDimensions.weatherIconSize,
      color: iconColor,
    );
  }

  /// بناء عنوان الخطأ
  Widget _buildErrorTitle(BuildContext context) {
    String title;

    switch (errorType) {
      case WeatherErrorType.network:
        title = 'مشكلة في الاتصال';
        break;
      case WeatherErrorType.location:
        title = 'تعذر تحديد الموقع';
        break;
      case WeatherErrorType.permission:
        title = 'إذن مطلوب';
        break;
      case WeatherErrorType.server:
        title = 'خطأ في الخادم';
        break;
      case WeatherErrorType.timeout:
        title = 'انتهت مهلة الاتصال';
        break;
      case WeatherErrorType.general:
        title = 'تعذر تحميل بيانات الطقس';
        break;
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: WeatherDimensions.largeTitleFontSize,
        fontFamily: AssetsFonts.cairo,
        fontWeight: FontWeight.bold,
        color: textColor ?? WeatherColors.primaryText,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء رسالة الخطأ
  Widget _buildErrorMessage(BuildContext context) {
    return Text(
      message,
      style: TextStyle(
        fontSize: WeatherDimensions.bodyFontSize,
        fontFamily: AssetsFonts.cairo,
        color: (textColor ?? WeatherColors.primaryText).withValues(alpha: 0.8),
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء زر إعادة المحاولة
  Widget _buildRetryButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onRetry,
      icon: Icon(Icons.refresh, size: WeatherDimensions.mediumIconSize),
      label: Text(
        WeatherStrings.retryButton,
        style: TextStyle(
          fontSize: WeatherDimensions.bodyFontSize,
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: WeatherColors.primaryBackground,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: WeatherDimensions.extraLargePadding,
          vertical: WeatherDimensions.mediumPadding,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            WeatherDimensions.buttonBorderRadius,
          ),
        ),
        elevation: 2,
      ),
    );
  }

  /// بناء نصائح الخطأ
  Widget _buildErrorTips(BuildContext context) {
    String tips;

    switch (errorType) {
      case WeatherErrorType.network:
        tips = '• تأكد من اتصالك بالإنترنت\n• جرب الاتصال بشبكة أخرى';
        break;
      case WeatherErrorType.location:
        tips = '• تأكد من تشغيل خدمات الموقع\n• امنح التطبيق إذن الوصول للموقع';
        break;
      case WeatherErrorType.permission:
        tips = '• اذهب إلى إعدادات التطبيق\n• امنح إذن الوصول للموقع';
        break;
      case WeatherErrorType.server:
        tips = '• المشكلة من الخادم\n• جرب مرة أخرى بعد قليل';
        break;
      case WeatherErrorType.timeout:
        tips = '• تأكد من سرعة الاتصال\n• جرب مرة أخرى';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      decoration: BoxDecoration(
        color: WeatherColors.infoColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        border: Border.all(
          color: WeatherColors.infoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: WeatherDimensions.mediumIconSize,
                color: WeatherColors.infoColor,
              ),
              SizedBox(width: WeatherDimensions.smallMargin),
              Text(
                'نصائح للحل',
                style: TextStyle(
                  fontSize: WeatherDimensions.subtitleFontSize,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.w600,
                  color: WeatherColors.infoColor,
                ),
              ),
            ],
          ),
          SizedBox(height: WeatherDimensions.smallMargin),
          Text(
            tips,
            style: TextStyle(
              fontSize: WeatherDimensions.smallFontSize,
              fontFamily: AssetsFonts.cairo,
              color: (textColor ?? WeatherColors.primaryText).withValues(
                alpha: 0.7,
              ),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

/// أنواع الأخطاء المختلفة
enum WeatherErrorType {
  /// خطأ عام
  general,

  /// خطأ في الشبكة
  network,

  /// خطأ في تحديد الموقع
  location,

  /// خطأ في الأذونات
  permission,

  /// خطأ في الخادم
  server,

  /// انتهاء مهلة الاتصال
  timeout,
}

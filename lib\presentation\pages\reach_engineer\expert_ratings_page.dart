

import '../../../imports.dart';


/// صفحة عرض تقييمات الخبير
///
/// تعرض جميع التقييمات الخاصة بخبير معين
/// مبسطة وفق التفضيلات الـ18 مع عدم التكرار
class ExpertRatingsPage extends StatefulWidget {
  /// بيانات الخبير
  final AgriculturalExpertModel expert;

  /// معرف المزارع الحالي (للتحقق من صلاحيات التعديل)
  final String? currentFarmerId;

  /// إنشاء صفحة تقييمات الخبير
  const ExpertRatingsPage({
    super.key,
    required this.expert,
    this.currentFarmerId,
  });

  @override
  State<ExpertRatingsPage> createState() => _ExpertRatingsPageState();
}

class _ExpertRatingsPageState extends State<ExpertRatingsPage> {
  @override
  void initState() {
    super.initState();
    // تحميل التقييمات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ExpertRatingsCubit>().loadExpertRatings(widget.expert.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقييمات ${widget.expert.name}'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              context.read<ExpertRatingsCubit>().loadExpertRatings(
                widget.expert.id,
              );
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: BlocBuilder<ExpertRatingsCubit, ExpertRatingsState>(
        builder: (context, state) {
          if (state is ExpertRatingsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ExpertRatingsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ في تحميل التقييمات',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.error,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ExpertRatingsCubit>().loadExpertRatings(
                        widget.expert.id,
                      );
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (state is ExpertRatingsLoaded) {
            final ratings = state.ratings;

            if (ratings.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.star_border,
                      size: 64,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد تقييمات',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لم يتم تقييم هذا الخبير بعد',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // إحصائيات سريعة
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  color: AppColors.surface,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Column(
                        children: [
                          Text(
                            widget.expert.rating.toStringAsFixed(1),
                            style: AppTextStyles.headlineMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: List.generate(5, (index) {
                              return Icon(
                                index < widget.expert.rating.round()
                                    ? Icons.star
                                    : Icons.star_border,
                                color: Colors.amber,
                                size: 16,
                              );
                            }),
                          ),
                          Text(
                            'التقييم العام',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Text(
                            '${ratings.length}',
                            style: AppTextStyles.headlineMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          Text(
                            'تقييم',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // قائمة التقييمات
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: ratings.length,
                    itemBuilder: (context, index) {
                      final rating = ratings[index];
                      final canEdit = widget.currentFarmerId == rating.farmerId;

                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    rating.farmerName,
                                    style: AppTextStyles.bodyLarge.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: List.generate(5, (starIndex) {
                                      return Icon(
                                        starIndex < rating.rating
                                            ? Icons.star
                                            : Icons.star_border,
                                        color: Colors.amber,
                                        size: 16,
                                      );
                                    }),
                                  ),
                                ],
                              ),
                              if (rating.comment != null &&
                                  rating.comment!.isNotEmpty) ...[
                                const SizedBox(height: 8),
                                Text(
                                  rating.comment!,
                                  style: AppTextStyles.bodyMedium,
                                ),
                              ],
                              const SizedBox(height: 8),
                              Text(
                                'تاريخ التقييم: ${rating.createdAt.day}/${rating.createdAt.month}/${rating.createdAt.year}',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              if (canEdit) ...[
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _editRating(rating),
                                      child: const Text('تعديل'),
                                    ),
                                    TextButton(
                                      onPressed: () => _deleteRating(rating),
                                      child: const Text('حذف'),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          }

          return const Center(child: Text('حالة غير معروفة'));
        },
      ),
    );
  }

  /// تعديل التقييم
  void _editRating(ExpertRatingModel rating) {
    // TODO: تطبيق تعديل التقييم
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل التقييم - قيد التطوير')),
    );
  }

  /// حذف التقييم
  void _deleteRating(ExpertRatingModel rating) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف التقييم'),
            content: const Text('هل تريد حذف هذا التقييم؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: تطبيق حذف التقييم
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم حذف التقييم')),
                  );
                },
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }
}

import 'package:flutter/foundation.dart';

/// مدير التخزين المؤقت الذكي للبيانات
///
/// يوفر تخزين مؤقت محسن للبيانات مع انتهاء صلاحية ذكي
/// وفق التفضيلات الـ18 مع عدم التكرار
class DataCacheManager {
  static final Map<String, CacheEntry> _cache = {};
  static const Duration _defaultCacheDuration = Duration(minutes: 15);
  static const int _maxCacheSize = 100;

  /// تخزين البيانات في التخزين المؤقت
  static void cacheData<T>({
    required String key,
    required T data,
    Duration? duration,
  }) {
    try {
      // تنظيف التخزين المؤقت إذا امتلأ
      if (_cache.length >= _maxCacheSize) {
        _cleanOldEntries();
      }

      final entry = CacheEntry<T>(
        data: data,
        timestamp: DateTime.now(),
        duration: duration ?? _defaultCacheDuration,
      );

      _cache[key] = entry;

      if (kDebugMode) {
        print('تم تخزين البيانات: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في تخزين البيانات: $e');
      }
    }
  }

  /// استرجاع البيانات من التخزين المؤقت
  static T? getCachedData<T>(String key) {
    try {
      final entry = _cache[key];

      if (entry == null) {
        return null;
      }

      // التحقق من انتهاء صلاحية البيانات
      if (_isExpired(entry)) {
        _cache.remove(key);
        if (kDebugMode) {
          print('انتهت صلاحية البيانات: $key');
        }
        return null;
      }

      if (kDebugMode) {
        print('تم استرجاع البيانات من التخزين المؤقت: $key');
      }

      return entry.data as T?;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في استرجاع البيانات: $e');
      }
      return null;
    }
  }

  /// التحقق من وجود البيانات في التخزين المؤقت
  static bool hasValidCache(String key) {
    final entry = _cache[key];
    return entry != null && !_isExpired(entry);
  }

  /// حذف بيانات محددة من التخزين المؤقت
  static void removeCache(String key) {
    _cache.remove(key);
    if (kDebugMode) {
      print('تم حذف البيانات من التخزين المؤقت: $key');
    }
  }

  /// تنظيف جميع البيانات المنتهية الصلاحية
  static void cleanExpiredCache() {
    final expiredKeys = <String>[];

    _cache.forEach((key, entry) {
      if (_isExpired(entry)) {
        expiredKeys.add(key);
      }
    });

    for (final key in expiredKeys) {
      _cache.remove(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      debugPrint('تم تنظيف ${expiredKeys.length} عنصر منتهي الصلاحية');
    }
  }

  /// تنظيف جميع البيانات
  static void clearAllCache() {
    final count = _cache.length;
    _cache.clear();

    if (kDebugMode) {
      print('تم تنظيف جميع البيانات المؤقتة ($count عنصر)');
    }
  }

  /// الحصول على إحصائيات التخزين المؤقت
  static CacheStats getCacheStats() {
    int validEntries = 0;
    int expiredEntries = 0;

    _cache.forEach((key, entry) {
      if (_isExpired(entry)) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    });

    return CacheStats(
      totalEntries: _cache.length,
      validEntries: validEntries,
      expiredEntries: expiredEntries,
      maxSize: _maxCacheSize,
    );
  }

  /// تنظيف الإدخالات القديمة
  static void _cleanOldEntries() {
    // أولاً: حذف الإدخالات المنتهية الصلاحية
    cleanExpiredCache();

    // إذا ما زال التخزين المؤقت ممتلئاً، احذف الأقدم
    if (_cache.length >= _maxCacheSize) {
      final sortedEntries =
          _cache.entries.toList()
            ..sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));

      final entriesToRemove = sortedEntries.take(_maxCacheSize ~/ 4);
      for (final entry in entriesToRemove) {
        _cache.remove(entry.key);
      }

      if (kDebugMode) {
        print('تم حذف ${entriesToRemove.length} إدخال قديم');
      }
    }
  }

  /// التحقق من انتهاء صلاحية الإدخال
  static bool _isExpired(CacheEntry entry) {
    final now = DateTime.now();
    final expiryTime = entry.timestamp.add(entry.duration);
    return now.isAfter(expiryTime);
  }
}

/// مفاتيح التخزين المؤقت المحددة مسبقاً
class CacheKeys {
  static const String expertsList = 'experts_list';
  static const String expertsStatistics = 'experts_statistics';
  static const String expertDetails = 'expert_details_';
  static const String expertRatings = 'expert_ratings_';
  static const String consultations = 'consultations_';
  static const String searchResults = 'search_results_';
  static const String filterResults = 'filter_results_';
}

/// إدخال التخزين المؤقت
class CacheEntry<T> {
  final T data;
  final DateTime timestamp;
  final Duration duration;

  CacheEntry({
    required this.data,
    required this.timestamp,
    required this.duration,
  });
}

/// إحصائيات التخزين المؤقت
class CacheStats {
  final int totalEntries;
  final int validEntries;
  final int expiredEntries;
  final int maxSize;

  CacheStats({
    required this.totalEntries,
    required this.validEntries,
    required this.expiredEntries,
    required this.maxSize,
  });

  double get usagePercentage => (totalEntries / maxSize) * 100;
  double get validPercentage =>
      totalEntries > 0 ? (validEntries / totalEntries) * 100 : 0;

  @override
  String toString() {
    return 'CacheStats(total: $totalEntries, valid: $validEntries, expired: $expiredEntries, usage: ${usagePercentage.toStringAsFixed(1)}%)';
  }
}

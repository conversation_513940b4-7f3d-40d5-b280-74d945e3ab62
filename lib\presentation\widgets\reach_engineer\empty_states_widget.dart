import 'package:flutter/material.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// أنواع الحالات الفارغة المختلفة
enum EmptyStateType {
  /// لا توجد خبراء
  noExperts,

  /// لا توجد نتائج بحث
  noSearchResults,

  /// لا توجد خبراء متاحين
  noAvailableExperts,

  /// خطأ في الشبكة
  networkError,

  /// خطأ عام
  generalError,

  /// لا توجد استشارات
  noConsultations,

  /// لا توجد تقييمات
  noRatings,
}

/// ويدجت الحالات الفارغة المحسنة للخبراء الزراعيين
///
/// يوفر واجهات جذابة ومفيدة للحالات الفارغة المختلفة
/// مطبق وفق التفضيلات الـ18 مع عدم التكرار
class EmptyStatesWidget extends StatefulWidget {
  /// نوع الحالة الفارغة
  final EmptyStateType type;

  /// العنوان المخصص
  final String? customTitle;

  /// الوصف المخصص
  final String? customDescription;

  /// نص الزر المخصص
  final String? customButtonText;

  /// دالة استدعاء عند النقر على الزر
  final VoidCallback? onButtonPressed;

  /// دالة استدعاء عند النقر على إعادة المحاولة
  final VoidCallback? onRetry;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  /// أيقونة مخصصة
  final IconData? customIcon;

  /// لون مخصص
  final Color? customColor;

  const EmptyStatesWidget({
    super.key,
    required this.type,
    this.customTitle,
    this.customDescription,
    this.customButtonText,
    this.onButtonPressed,
    this.onRetry,
    this.showAnimations = true,
    this.customIcon,
    this.customColor,
  });

  @override
  State<EmptyStatesWidget> createState() => _EmptyStatesWidgetState();
}

class _EmptyStatesWidgetState extends State<EmptyStatesWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _bounceController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();

    if (widget.showAnimations) {
      _setupAnimations();
    }
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _bounceAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.elasticOut),
    );

    _fadeController.forward();
    _bounceController.forward();
  }

  @override
  void dispose() {
    if (widget.showAnimations) {
      _fadeController.dispose();
      _bounceController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _fadeController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: AnimatedBuilder(
              animation: _bounceController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _bounceAnimation.value,
                  child: _buildContent(),
                );
              },
            ),
          );
        },
      );
    }

    return _buildContent();
  }

  /// بناء المحتوى
  Widget _buildContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildIllustration(),
            const SizedBox(height: 24),
            _buildTitle(),
            const SizedBox(height: 12),
            _buildDescription(),
            const SizedBox(height: 32),
            _buildActionButton(),
          ],
        ),
      ),
    );
  }

  /// بناء الرسم التوضيحي
  Widget _buildIllustration() {
    final config = _getEmptyStateConfig();

    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: (widget.customColor ?? config.color).withValues(alpha: 0.1),
      ),
      child: Icon(
        widget.customIcon ?? config.icon,
        size: 60,
        color: widget.customColor ?? config.color,
      ),
    );
  }

  /// بناء العنوان
  Widget _buildTitle() {
    final config = _getEmptyStateConfig();

    return Text(
      widget.customTitle ?? config.title,
      style: AppTextStyles.headlineMedium.copyWith(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.bold,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء الوصف
  Widget _buildDescription() {
    final config = _getEmptyStateConfig();

    return Text(
      widget.customDescription ?? config.description,
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.textSecondary,
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء زر الإجراء
  Widget _buildActionButton() {
    final config = _getEmptyStateConfig();

    if (config.buttonText == null && widget.customButtonText == null) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        if (config.buttonText != null || widget.customButtonText != null)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: widget.onButtonPressed ?? widget.onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.customColor ?? config.color,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                widget.customButtonText ?? config.buttonText!,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

        // زر إعادة المحاولة للأخطاء
        if (_isErrorState() && widget.onRetry != null) ...[
          const SizedBox(height: 12),
          TextButton(
            onPressed: widget.onRetry,
            child: Text(
              'إعادة المحاولة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: widget.customColor ?? config.color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// الحصول على تكوين الحالة الفارغة
  EmptyStateConfig _getEmptyStateConfig() {
    switch (widget.type) {
      case EmptyStateType.noExperts:
        return EmptyStateConfig(
          icon: Icons.people_outline,
          title: 'لا توجد مرشدين زراعيين مسجلين',
          description:
              'لم يتم العثور على أي مرشدين زراعيين مسجلين في النظام حالياً. يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع الإدارة.',
          color: AppColors.primary,
          buttonText: 'تحديث القائمة',
        );

      case EmptyStateType.noSearchResults:
        return EmptyStateConfig(
          icon: Icons.search_off,
          title: 'لا توجد نتائج',
          description:
              'لم نتمكن من العثور على خبراء يطابقون معايير البحث الخاصة بك. جرب تعديل كلمات البحث أو الفلاتر.',
          color: AppColors.warning,
          buttonText: 'مسح البحث',
        );

      case EmptyStateType.noAvailableExperts:
        return EmptyStateConfig(
          icon: Icons.schedule,
          title: 'لا توجد خبراء متاحين حالياً',
          description:
              'جميع الخبراء مشغولون في الوقت الحالي. يرجى المحاولة مرة أخرى لاحقاً أو إزالة فلتر التوفر.',
          color: AppColors.accent,
          buttonText: 'عرض جميع الخبراء',
        );

      case EmptyStateType.networkError:
        return EmptyStateConfig(
          icon: Icons.wifi_off,
          title: 'مشكلة في الاتصال',
          description:
              'تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
          color: AppColors.error,
          buttonText: 'إعادة المحاولة',
        );

      case EmptyStateType.generalError:
        return EmptyStateConfig(
          icon: Icons.error_outline,
          title: 'حدث خطأ',
          description:
              'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.',
          color: AppColors.error,
          buttonText: 'إعادة المحاولة',
        );

      case EmptyStateType.noConsultations:
        return EmptyStateConfig(
          icon: Icons.chat_bubble_outline,
          title: 'لا توجد استشارات',
          description:
              'لم تقم بطلب أي استشارات بعد. ابدأ بطلب استشارة من أحد الخبراء المتاحين.',
          color: AppColors.primary,
          buttonText: 'تصفح الخبراء',
        );

      case EmptyStateType.noRatings:
        return EmptyStateConfig(
          icon: Icons.star_outline,
          title: 'لا توجد تقييمات',
          description: 'لم يتم تقييم هذا الخبير بعد. كن أول من يقيم خدماته.',
          color: AppColors.warning,
          buttonText: null,
        );
    }
  }

  /// التحقق من كون الحالة خطأ
  bool _isErrorState() {
    return widget.type == EmptyStateType.networkError ||
        widget.type == EmptyStateType.generalError;
  }
}

/// تكوين الحالة الفارغة
class EmptyStateConfig {
  final IconData icon;
  final String title;
  final String description;
  final Color color;
  final String? buttonText;

  const EmptyStateConfig({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
    this.buttonText,
  });
}


import '../../../imports.dart';

/// صفحة استشاراتي
///
/// تعرض قائمة الاستشارات الخاصة بالمزارع مع إمكانية البحث والفلترة
/// وعرض تفاصيل كل استشارة وحالتها
class MyConsultationsPage extends StatefulWidget {
  /// معرف المزارع
  final String farmerId;

  /// اسم المزارع
  final String farmerName;

  /// إنشاء صفحة استشاراتي
  const MyConsultationsPage({
    super.key,
    required this.farmerId,
    required this.farmerName,
  });

  @override
  State<MyConsultationsPage> createState() => _MyConsultationsPageState();
}

class _MyConsultationsPageState extends State<MyConsultationsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();

  List<ConsultationModel> _allConsultations = [];
  List<ConsultationModel> _filteredConsultations = [];
  Map<String, int>? _statistics;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadConsultations();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: defaultAppBar(
        context: context,
        titel: ExpertsConstants.myConsultationsTitle,
        color: AppColors.primary,
      ),
      body: BlocListener<ConsultationsCubit, ConsultationsState>(
        listener: _handleConsultationState,
        child: Column(
          children: [
            // شريط البحث والفلاتر
            _buildSearchAndFilters(),

            // التبويبات
            _buildTabBar(),

            // الإحصائيات
            if (_statistics != null) _buildStatistics(),

            // محتوى الاستشارات
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildConsultationsList(), // جميع الاستشارات
                  _buildConsultationsList(
                    status: ConsultationStatus.pending,
                  ), // قيد الانتظار
                  _buildConsultationsList(
                    status: ConsultationStatus.responded,
                  ), // تم الرد
                  _buildConsultationsList(
                    status: ConsultationStatus.completed,
                  ), // مكتملة
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToExpertsList(),
        backgroundColor: AppColors.primary,
        tooltip: 'طلب استشارة جديدة',
        child: const Icon(Icons.add, color: AppColors.onPrimary),
      ),
    );
  }

  /// بناء شريط البحث والفلاتر
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الاستشارات...',
              prefixIcon: const Icon(
                Icons.search,
                color: AppColors.textSecondary,
              ),
              suffixIcon:
                  _searchController.text.isNotEmpty
                      ? IconButton(
                        icon: const Icon(
                          Icons.clear,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          _filterConsultations();
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: AppColors.background,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {});
              _filterConsultations();
            },
          ),
        ],
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: [
          Tab(text: 'الكل', icon: Icon(Icons.list_alt, size: 20)),
          Tab(text: 'قيد الانتظار', icon: Icon(Icons.schedule, size: 20)),
          Tab(text: 'تم الرد', icon: Icon(Icons.reply, size: 20)),
          Tab(text: 'مكتملة', icon: Icon(Icons.check_circle, size: 20)),
        ],
      ),
    );
  }

  /// بناء الإحصائيات
  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'المجموع',
            _statistics!['total']?.toString() ?? '0',
            Icons.list_alt,
            AppColors.primary,
          ),
          _buildStatItem(
            'قيد الانتظار',
            _statistics!['pending']?.toString() ?? '0',
            Icons.schedule,
            AppColors.warning,
          ),
          _buildStatItem(
            'تم الرد',
            _statistics!['responded']?.toString() ?? '0',
            Icons.reply,
            AppColors.info,
          ),
          _buildStatItem(
            'مكتملة',
            _statistics!['completed']?.toString() ?? '0',
            Icons.check_circle,
            AppColors.success,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyles.of(
            context,
          ).headlineSmall(fontWeight: FontWeight.bold, color: color),
        ),
        Text(
          label,
          style: TextStyles.of(
            context,
          ).bodySmall(color: AppColors.textSecondary),
        ),
      ],
    );
  }

  /// بناء قائمة الاستشارات
  Widget _buildConsultationsList({ConsultationStatus? status}) {
    return BlocBuilder<ConsultationsCubit, ConsultationsState>(
      builder: (context, state) {
        if (state is ConsultationsLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is ConsultationsError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: AppColors.error),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ في تحميل الاستشارات',
                  style: TextStyles.of(
                    context,
                  ).bodyLarge(color: AppColors.error),
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: TextStyles.of(
                    context,
                  ).bodyMedium(color: AppColors.textSecondary),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadConsultations,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final consultations = _getFilteredConsultations(status);

        if (consultations.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.assignment_outlined,
                  size: 64,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(height: 16),
                Text(
                  status == null
                      ? 'لا توجد استشارات'
                      : 'لا توجد استشارات ${_getStatusText(status)}',
                  style: TextStyles.of(
                    context,
                  ).bodyLarge(color: AppColors.textSecondary),
                ),
                const SizedBox(height: 8),
                Text(
                  'يمكنك طلب استشارة جديدة من الخبراء',
                  style: TextStyles.of(
                    context,
                  ).bodyMedium(color: AppColors.textSecondary),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _navigateToExpertsList,
                  icon: const Icon(Icons.add),
                  label: const Text('طلب استشارة جديدة'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async => _loadConsultations(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: consultations.length,
            itemBuilder: (context, index) {
              final consultation = consultations[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Card(
                  elevation: 2,
                  child: ListTile(
                    title: Text(consultation.title),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(consultation.description),
                        const SizedBox(height: 4),
                        Text(
                          'الحالة: ${_getStatusText(consultation.status)}',
                          style: TextStyle(
                            color: _getStatusColor(consultation.status),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    trailing:
                        consultation.status == ConsultationStatus.responded
                            ? IconButton(
                              icon: const Icon(Icons.star_rate),
                              onPressed: () => _rateConsultation(consultation),
                              tooltip: 'تقييم الاستشارة',
                            )
                            : Icon(
                              _getStatusIcon(consultation.status),
                              color: _getStatusColor(consultation.status),
                            ),
                    onTap: () => _viewConsultationDetails(consultation),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// الحصول على الاستشارات المفلترة
  List<ConsultationModel> _getFilteredConsultations(
    ConsultationStatus? status,
  ) {
    var consultations = _filteredConsultations;

    if (status != null) {
      consultations = consultations.where((c) => c.status == status).toList();
    }

    return consultations;
  }

  /// الحصول على نص الحالة
  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'قيد الانتظار';
      case ConsultationStatus.responded:
        return 'تم الرد عليها';
      case ConsultationStatus.completed:
        return 'مكتملة';
      case ConsultationStatus.cancelled:
        return 'ملغية';
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return Colors.orange;
      case ConsultationStatus.responded:
        return Colors.blue;
      case ConsultationStatus.completed:
        return Colors.green;
      case ConsultationStatus.cancelled:
        return Colors.red;
    }
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return Icons.hourglass_empty;
      case ConsultationStatus.responded:
        return Icons.reply;
      case ConsultationStatus.completed:
        return Icons.check_circle;
      case ConsultationStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// تحميل الاستشارات
  void _loadConsultations() {
    context.read<ConsultationsCubit>().loadFarmerConsultations(widget.farmerId);
  }

  /// فلترة الاستشارات
  void _filterConsultations() {
    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      _filteredConsultations = List.from(_allConsultations);
    } else {
      _filteredConsultations =
          _allConsultations.where((consultation) {
            return consultation.title.toLowerCase().contains(query) ||
                consultation.description.toLowerCase().contains(query) ||
                consultation.expertName.toLowerCase().contains(query) ||
                consultation.category.toLowerCase().contains(query);
          }).toList();
    }

    setState(() {});
  }

  /// عرض تفاصيل الاستشارة
  void _viewConsultationDetails(ConsultationModel consultation) {
    // TODO: الانتقال لصفحة تفاصيل الاستشارة
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(consultation.title),
            content: Text(consultation.description),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// تقييم الاستشارة
  void _rateConsultation(ConsultationModel consultation) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تقييم الاستشارة'),
            content: Text('تقييم استشارة: ${consultation.title}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  // TODO: تطبيق نظام التقييم
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إضافة التقييم')),
                  );
                },
                child: const Text('تقييم'),
              ),
            ],
          ),
    ).then((result) {
      // إذا تم التقييم بنجاح، أعد تحميل البيانات
      if (result == true) {
        _loadConsultations();
      }
    });
  }

  /// الانتقال لقائمة الخبراء
  void _navigateToExpertsList() {
    Navigator.of(context).pop(); // العودة لصفحة الخبراء
  }

  /// معالجة حالة الاستشارة
  void _handleConsultationState(
    BuildContext context,
    ConsultationsState state,
  ) {
    if (state is ConsultationsLoaded) {
      _allConsultations = state.consultations;
      // _statistics = state.statistics; // مؤقتاً معطل
      _filterConsultations();
    }
  }
}

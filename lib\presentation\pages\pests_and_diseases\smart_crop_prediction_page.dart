import 'package:flutter/material.dart';
import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';

/// نموذج توقع المحصول
class CropPrediction {
  final String cropName;
  final double successRate;
  final double expectedYield;
  final String recommendation;
  final Color color;

  CropPrediction({
    required this.cropName,
    required this.successRate,
    required this.expectedYield,
    required this.recommendation,
    required this.color,
  });
}

/// نموذج اتجاه السوق
class MarketTrend {
  final String cropName;
  final double currentPrice;
  final double predictedPrice;
  final TrendDirection direction;
  final double changePercentage;

  MarketTrend({
    required this.cropName,
    required this.currentPrice,
    required this.predictedPrice,
    required this.direction,
    required this.changePercentage,
  });
}

/// اتجاه الاتجاه
enum TrendDirection { up, down, stable }

/// نموذج توقعات الطقس
class WeatherForecast {
  final String date;
  final double temperature;
  final double humidity;
  final double rainfall;
  final String condition;

  WeatherForecast({
    required this.date,
    required this.temperature,
    required this.humidity,
    required this.rainfall,
    required this.condition,
  });
}

/// صفحة التنبؤ الذكي بالمحاصيل
///
/// ميزة فريدة تستخدم الذكاء الاصطناعي والبيانات الضخمة للتنبؤ بـ:
/// - أفضل المحاصيل للزراعة حسب المنطقة والموسم
/// - توقعات الإنتاج والأرباح
/// - المخاطر المحتملة والحلول الوقائية
class SmartCropPredictionPage extends StatefulWidget {
  const SmartCropPredictionPage({super.key});

  @override
  State<SmartCropPredictionPage> createState() =>
      _SmartCropPredictionPageState();
}

class _SmartCropPredictionPageState extends State<SmartCropPredictionPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _chartAnimationController;
  late Animation<double> _fadeAnimation;

  bool _isAnalyzing = false;
  bool _analysisComplete = false;
  List<CropPrediction> _predictions = [];
  List<MarketTrend> _marketTrends = [];
  WeatherForecast? _weatherData;

  String _selectedRegion = 'صنعاء';
  String _selectedSeason = 'الربيع';
  double _farmSize = 5.0;
  String _soilType = 'طينية';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadInitialData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _chartAnimationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _chartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

  }

  Future<void> _loadInitialData() async {
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _marketTrends = _generateMarketTrends();
      _weatherData = _generateWeatherForecast();
    });

    _animationController.forward();
  }

  List<MarketTrend> _generateMarketTrends() {
    return [
      MarketTrend(
        cropName: 'طماطم',
        currentPrice: 450,
        predictedPrice: 500,
        direction: TrendDirection.up,
        changePercentage: 12.5,
      ),
      MarketTrend(
        cropName: 'خيار',
        currentPrice: 320,
        predictedPrice: 295,
        direction: TrendDirection.down,
        changePercentage: -8.2,
      ),
      MarketTrend(
        cropName: 'فلفل',
        currentPrice: 680,
        predictedPrice: 850,
        direction: TrendDirection.up,
        changePercentage: 25.3,
      ),
    ];
  }

  WeatherForecast _generateWeatherForecast() {
    return WeatherForecast(
      date: 'الأسبوع القادم',
      temperature: 28,
      humidity: 65,
      rainfall: 15,
      condition: 'مناسب للزراعة مع ري إضافي',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'التنبؤ الذكي بالمحاصيل',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showAdvancedAnalytics,
            tooltip: 'التحليلات المتقدمة',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showPredictionHistory,
            tooltip: 'سجل التنبؤات',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس التحليل الذكي
              _buildSmartAnalysisHeader(),

              const SizedBox(height: 20),

              // نموذج إدخال البيانات
              _buildInputForm(),

              const SizedBox(height: 20),

              // زر التحليل
              _buildAnalysisButton(),

              const SizedBox(height: 20),

              // نتائج التنبؤ
              if (_analysisComplete) _buildPredictionResults(),

              const SizedBox(height: 20),

              // اتجاهات السوق
              _buildMarketTrends(),

              const SizedBox(height: 20),

              // توقعات الطقس
              _buildWeatherForecast(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء رأس التحليل الذكي
  Widget _buildSmartAnalysisHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AssetsColors.primary,
            AssetsColors.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.psychology,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مختبر التنبؤ الذكي',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'تحليل متقدم بالذكاء الاصطناعي',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // مؤشرات الأداء
          Row(
            children: [
              Expanded(
                child: _buildPerformanceIndicator(
                  'دقة التنبؤ',
                  '94.2%',
                  Icons.precision_manufacturing,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildPerformanceIndicator(
                  'نجاح التوصيات',
                  '87.8%',
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildPerformanceIndicator(
                  'توفير التكاليف',
                  '23.5%',
                  Icons.savings,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر أداء
  Widget _buildPerformanceIndicator(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 10,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء نموذج إدخال البيانات
  Widget _buildInputForm() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'بيانات المزرعة',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AssetsColors.kGrey100,
            ),
          ),

          const SizedBox(height: 16),

          // المنطقة
          _buildDropdownField(
            'المنطقة',
            _selectedRegion,
            ['صنعاء', 'تعز', 'إب', 'الحديدة', 'عدن'],
            (value) => setState(() => _selectedRegion = value!),
          ),

          const SizedBox(height: 16),

          // الموسم
          _buildDropdownField(
            'الموسم',
            _selectedSeason,
            ['الربيع', 'الصيف', 'الخريف', 'الشتاء'],
            (value) => setState(() => _selectedSeason = value!),
          ),

          const SizedBox(height: 16),

          // مساحة المزرعة
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مساحة المزرعة (هكتار)',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kGrey70,
                ),
              ),
              const SizedBox(height: 8),
              Slider(
                value: _farmSize,
                min: 0.5,
                max: 50.0,
                divisions: 99,
                activeColor: AssetsColors.primary,
                onChanged: (value) => setState(() => _farmSize = value),
              ),
              Text(
                '${_farmSize.toStringAsFixed(1)} هكتار',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 12,
                  color: AssetsColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // نوع التربة
          _buildDropdownField('نوع التربة', _soilType, [
            'طينية',
            'رملية',
            'طميية',
            'صخرية',
          ], (value) => setState(() => _soilType = value!)),
        ],
      ),
    );
  }

  /// بناء حقل منسدل
  Widget _buildDropdownField(
    String label,
    String value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kGrey70,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                color: Colors.black,
              ),
              items:
                  items.map((item) {
                    return DropdownMenuItem(value: item, child: Text(item));
                  }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء زر التحليل
  Widget _buildAnalysisButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isAnalyzing ? null : _startAnalysis,
        icon:
            _isAnalyzing
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : const Icon(Icons.analytics),
        label: Text(
          _isAnalyzing ? 'جاري التحليل...' : 'بدء التحليل الذكي',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AssetsColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// بدء التحليل
  void _startAnalysis() {
    setState(() {
      _isAnalyzing = true;
      _analysisComplete = false;
    });

    // محاكاة التحليل
    Future.delayed(const Duration(seconds: 3), () {
      setState(() {
        _isAnalyzing = false;
        _analysisComplete = true;
        _predictions = _generatePredictions();
        _marketTrends = _generateMarketTrends();
        _weatherData = _generateWeatherForecast();
      });
    });
  }

  /// توليد التوقعات
  List<CropPrediction> _generatePredictions() {
    return [
      CropPrediction(
        cropName: 'طماطم',
        successRate: 85.0,
        expectedYield: 12.5,
        recommendation: 'ممتاز للزراعة',
        color: Colors.green,
      ),
      CropPrediction(
        cropName: 'خيار',
        successRate: 78.0,
        expectedYield: 8.2,
        recommendation: 'جيد للزراعة',
        color: Colors.blue,
      ),
      CropPrediction(
        cropName: 'فلفل',
        successRate: 72.0,
        expectedYield: 6.8,
        recommendation: 'مقبول للزراعة',
        color: Colors.orange,
      ),
    ];
  }

  /// بناء نتائج التوقع
  Widget _buildPredictionResults() {
    return Column(
      children:
          _predictions.map((prediction) {
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: prediction.color,
                  child: Text(
                    '${prediction.successRate.toInt()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                title: Text(prediction.cropName),
                subtitle: Text(prediction.recommendation),
                trailing: Text(
                  '${prediction.expectedYield} طن/هكتار',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            );
          }).toList(),
    );
  }

  /// بناء اتجاهات السوق
  Widget _buildMarketTrends() {
    return Column(
      children:
          _marketTrends.map((trend) {
            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ListTile(
                leading: Icon(
                  _getTrendIcon(trend.direction),
                  color: _getTrendColor(trend.direction),
                ),
                title: Text(trend.cropName),
                subtitle: Text(
                  'السعر الحالي: ${trend.currentPrice.toInt()} ريال',
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${trend.predictedPrice.toInt()} ريال',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${trend.changePercentage > 0 ? '+' : ''}${trend.changePercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: _getTrendColor(trend.direction),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
    );
  }

  /// بناء توقعات الطقس
  Widget _buildWeatherForecast() {
    if (_weatherData == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توقعات الطقس - ${_weatherData!.date}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildWeatherItem(
                  'الحرارة',
                  '${_weatherData!.temperature.toInt()}°م',
                  Icons.thermostat,
                ),
                _buildWeatherItem(
                  'الرطوبة',
                  '${_weatherData!.humidity.toInt()}%',
                  Icons.water_drop,
                ),
                _buildWeatherItem(
                  'الأمطار',
                  '${_weatherData!.rainfall.toInt()}مم',
                  Icons.cloud_queue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر الطقس
  Widget _buildWeatherItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.blue),
        const SizedBox(height: 4),
        Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  /// الحصول على أيقونة الاتجاه
  IconData _getTrendIcon(TrendDirection direction) {
    switch (direction) {
      case TrendDirection.up:
        return Icons.trending_up;
      case TrendDirection.down:
        return Icons.trending_down;
      case TrendDirection.stable:
        return Icons.trending_flat;
    }
  }

  /// الحصول على لون الاتجاه
  Color _getTrendColor(TrendDirection direction) {
    switch (direction) {
      case TrendDirection.up:
        return Colors.green;
      case TrendDirection.down:
        return Colors.red;
      case TrendDirection.stable:
        return Colors.grey;
    }
  }

  /// عرض التحليلات المتقدمة
  void _showAdvancedAnalytics() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('التحليلات المتقدمة'),
            content: const Text('ستتوفر قريباً...'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// عرض تاريخ التوقعات
  void _showPredictionHistory() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تاريخ التوقعات'),
            content: const Text('ستتوفر قريباً...'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}

import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';
import '../../../core/constants/headings.dart';
import '../../../core/constants/text_styles.dart';
import '../../../data/models/crops/disease_model.dart';
import '../../bloc/agricultural_crops/crops_cubit.dart';
import '../../widgets/shared/custom_loading_animation.dart';
import 'ai_diagnosis_page.dart';
import 'pest_distribution_map_page.dart';
import 'community_reports_page.dart';
import 'early_warning_system_page.dart';
import 'interactive_learning_page.dart';
import 'genetic_plant_analysis_page.dart';
import 'ar_plant_diagnosis_page.dart';
import 'reference_sources_page.dart';
import '../../../data/models/pests_diseases_data.dart';

/// صفحة الآفات والأمراض الزراعية
///
/// تعرض هذه الصفحة جميع الآفات والأمراض التي تصيب المحاصيل الزراعية
/// مع إمكانية البحث والتصفية حسب نوع المرض والمحصول
class PestsAndDiseases extends StatefulWidget {
  /// إنشاء صفحة الآفات والأمراض
  const PestsAndDiseases({super.key});

  @override
  State<PestsAndDiseases> createState() => _PestsAndDiseasesState();
}

class _PestsAndDiseasesState extends State<PestsAndDiseases>
    with TickerProviderStateMixin {
  /// متحكم البحث
  final TextEditingController _searchController = TextEditingController();

  /// متحكم التبويبات
  late TabController _tabController;

  /// نوع المرض المختار للتصفية
  int? _selectedDiseaseTypeId;

  /// المحصول المختار للتصفية
  int? _selectedPlantId;

  /// قائمة الأمراض المفلترة
  List<Disease> _filteredDiseases = [];


  /// قائمة الأمراض المفضلة
  final List<Disease> _favoriteDiseases = [];

  /// حالة التحميل
  bool _isLoading = false;

  /// إحصائيات البحث
  int _totalResults = 0;

  /// البيانات الجديدة للآفات والأمراض
  List<Map<String, dynamic>> _allPestsData = [];
  List<Map<String, dynamic>> _filteredPestsData = [];
  Map<String, int> _statistics = {};
  String _selectedFilter = 'الكل';
  String _selectedSeverity = 'الكل';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _searchController.addListener(_onSearchChanged);
    _loadPestsData();
  }

  /// تحميل بيانات الآفات والأمراض
  void _loadPestsData() {
    setState(() {
      _isLoading = true;
    });

    // تحميل البيانات من المصدر الجديد
    _allPestsData = PestsDiseasesData.getAllPestsAndDiseases();
    _filteredPestsData = _allPestsData;
    _statistics = PestsDiseasesData.getPestsStatistics();
    _totalResults = _allPestsData.length;

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// معالج تغيير البحث
  void _onSearchChanged() {
    _filterDiseases();
    _filterPests();
  }

  /// تصفية الآفات والأمراض الجديدة
  void _filterPests() {
    setState(() {
      _filteredPestsData =
          _allPestsData.where((pest) {
            // تصفية حسب النص المدخل
            final searchText = _searchController.text.toLowerCase();
            final matchesSearch =
                searchText.isEmpty ||
                pest['name'].toString().toLowerCase().contains(searchText) ||
                pest['scientificName'].toString().toLowerCase().contains(
                  searchText,
                ) ||
                pest['description'].toString().toLowerCase().contains(
                  searchText,
                );

            // تصفية حسب النوع
            final matchesFilter =
                _selectedFilter == 'الكل' ||
                pest['category'] == _selectedFilter;

            // تصفية حسب مستوى الخطورة
            final matchesSeverity =
                _selectedSeverity == 'الكل' ||
                pest['severity'] == _selectedSeverity;

            return matchesSearch && matchesFilter && matchesSeverity;
          }).toList();

      _totalResults = _filteredPestsData.length;
    });
  }

  /// تصفية الأمراض حسب البحث والفلاتر مع إحصائيات
  void _filterDiseases() {
    final cubit = context.read<CropsCubit>();
    final searchQuery = _searchController.text.toLowerCase();

    setState(() {
      _isLoading = true;
    });

    // محاكاة تأخير للتحميل
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          _filteredDiseases =
              cubit.diseases.where((disease) {
                // تصفية حسب النص
                final matchesSearch =
                    disease.name.toLowerCase().contains(searchQuery) ||
                    disease.details.toLowerCase().contains(searchQuery);

                // تصفية حسب نوع المرض
                final matchesDiseaseType =
                    _selectedDiseaseTypeId == null ||
                    disease.diseaseTypeId == _selectedDiseaseTypeId;

                // تصفية حسب المحصول
                final matchesPlant =
                    _selectedPlantId == null ||
                    disease.plantId == _selectedPlantId;

                return matchesSearch && matchesDiseaseType && matchesPlant;
              }).toList();

          _totalResults = _filteredDiseases.length;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.read<CropsCubit>(),
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: BlocConsumer<CropsCubit, CropsState>(
          listener: _handleStateChanges,
          builder: (context, state) {
            final cubit = context.read<CropsCubit>();

            // إذا كانت البيانات فارغة، نحمل البيانات
            if (cubit.diseases.isEmpty) {
              cubit.getDiseases();
              cubit.getDiseaseTypes();
              cubit.getPlants();
              return const Center(child: CustomLoadingAnimation());
            }

            if (state is GetDiseasesFailed) {
              return _buildErrorState(context);
            }

            return _buildBody(context);
          },
        ),
      ),
    );
  }

  /// بناء شريط التطبيق المحسن
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Headings.pestsAndDiseases,
            style: TextStyles.of(context).headlineMedium(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.kWhite,
            ),
          ),
          if (_totalResults > 0)
            Text(
              '$_totalResults نتيجة',
              style: TextStyle(
                fontSize: 12,
                fontFamily: AssetsFonts.cairo,
                color: Colors.white70,
              ),
            ),
        ],
      ),
      backgroundColor: AssetsColors.primary,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.library_books, color: Colors.white),
          onPressed: () => _navigateToReferenceSources(),
          tooltip: 'المصادر المرجعية',
        ),
        IconButton(
          icon: const Icon(Icons.info_outline, color: Colors.white),
          onPressed: () => _showInfoDialog(),
          tooltip: 'معلومات',
        ),
        // زر المفضلة
        Stack(
          children: [
            IconButton(
              icon: Icon(Icons.favorite, color: Colors.white),
              onPressed: () => _showFavorites(context),
              tooltip: 'المفضلة',
            ),
            if (_favoriteDiseases.isNotEmpty)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '${_favoriteDiseases.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),

        // زر الميزات المتقدمة
        IconButton(
          icon: Icon(Icons.auto_awesome, color: Colors.white),
          onPressed: () => _showAdvancedFeatures(),
          tooltip: 'الميزات المتقدمة',
        ),

        // زر المزيد
        PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: Colors.white),
          onSelected: _handleMenuSelection,
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  value: 'ai_diagnosis',
                  child: Row(
                    children: [
                      Icon(Icons.psychology, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'التشخيص بالذكاء الاصطناعي',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'pest_map',
                  child: Row(
                    children: [
                      Icon(Icons.map, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'خريطة انتشار الآفات',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'community',
                  child: Row(
                    children: [
                      Icon(Icons.people, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'تقارير المجتمع',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'early_warning',
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'نظام الإنذار المبكر',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'interactive_learning',
                  child: Row(
                    children: [
                      Icon(Icons.school, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'التعلم التفاعلي',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'genetic_analysis',
                  child: Row(
                    children: [
                      Icon(Icons.biotech, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'التحليل الجيني للنباتات',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'ar_diagnosis',
                  child: Row(
                    children: [
                      Icon(Icons.view_in_ar, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'التشخيص بالواقع المعزز',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'refresh',
                  child: Row(
                    children: [
                      Icon(Icons.refresh, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'تحديث البيانات',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'statistics',
                  child: Row(
                    children: [
                      Icon(Icons.analytics, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'الإحصائيات',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'help',
                  child: Row(
                    children: [
                      Icon(Icons.help_outline, color: AssetsColors.primary),
                      const SizedBox(width: 8),
                      Text(
                        'المساعدة',
                        style: TextStyle(fontFamily: AssetsFonts.cairo),
                      ),
                    ],
                  ),
                ),
              ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: TextStyle(
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
        ),
        tabs: [
          Tab(text: 'جميع الأمراض'),
          Tab(text: 'الآفات الحشرية'),
          Tab(text: 'الأمراض الفطرية'),
          Tab(text: 'دليل شامل'),
        ],
      ),
    );
  }

  /// معالجة تغييرات الحالة
  void _handleStateChanges(BuildContext context, CropsState state) {
    if (state is GetDiseasesSuccess) {
      _filterDiseases();
    }
  }

  /// معالجة اختيار القائمة
  void _handleMenuSelection(String value) {
    switch (value) {
      case 'ai_diagnosis':
        _navigateToAIDiagnosis();
        break;
      case 'pest_map':
        _navigateToPestMap();
        break;
      case 'community':
        _navigateToCommunity();
        break;
      case 'early_warning':
        _navigateToEarlyWarning();
        break;
      case 'interactive_learning':
        _navigateToInteractiveLearning();
        break;
      case 'genetic_analysis':
        _navigateToGeneticAnalysis();
        break;
      case 'ar_diagnosis':
        _navigateToARDiagnosis();
        break;
      case 'refresh':
        _refreshData();
        break;
      case 'statistics':
        _showStatistics();
        break;
      case 'help':
        _showHelp();
        break;
    }
  }

  /// تحديث البيانات
  void _refreshData() {
    final cubit = context.read<CropsCubit>();
    cubit.getDiseases();
    cubit.getDiseaseTypes();
    cubit.getPlants();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.refresh, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              'جاري تحديث البيانات...',
              style: TextStyle(fontFamily: AssetsFonts.cairo),
            ),
          ],
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض الإحصائيات
  void _showStatistics() {
    final cubit = context.read<CropsCubit>();
    final totalDiseases = cubit.diseases.length;
    final insectPests =
        cubit.diseases.where((d) => d.diseaseTypeId == 1).length;
    final fungalDiseases =
        cubit.diseases.where((d) => d.diseaseTypeId == 2).length;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(Icons.analytics, color: AssetsColors.primary),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات الأمراض',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatisticRow(
                  'إجمالي الأمراض',
                  totalDiseases,
                  Icons.bug_report,
                  Colors.blue,
                ),
                _buildStatisticRow(
                  'الآفات الحشرية',
                  insectPests,
                  Icons.pest_control,
                  Colors.orange,
                ),
                _buildStatisticRow(
                  'الأمراض الفطرية',
                  fungalDiseases,
                  Icons.coronavirus,
                  Colors.red,
                ),
                _buildStatisticRow(
                  'المفضلة',
                  _favoriteDiseases.length,
                  Icons.favorite,
                  Colors.pink,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إغلاق',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.primary,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatisticRow(
    String label,
    int count,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 16),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                color: Colors.white,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض المساعدة
  void _showHelp() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(Icons.help_outline, color: AssetsColors.primary),
                const SizedBox(width: 8),
                Text(
                  'كيفية الاستخدام',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHelpItem('🔍', 'استخدم شريط البحث للعثور على مرض معين'),
                _buildHelpItem(
                  '🏷️',
                  'استخدم الفلاتر لتصفية النتائج حسب النوع والمحصول',
                ),
                _buildHelpItem('❤️', 'اضغط على القلب لإضافة مرض للمفضلة'),
                _buildHelpItem('📖', 'اضغط على أي مرض لعرض التفاصيل الكاملة'),
                _buildHelpItem('🔄', 'اسحب للأسفل لتحديث البيانات'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'فهمت',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  /// بناء عنصر مساعدة
  Widget _buildHelpItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض المفضلة
  void _showFavorites(context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.95,
            minChildSize: 0.5,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // مقبض السحب
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: AssetsColors.kGrey70,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // العنوان
                    Row(
                      children: [
                        Icon(Icons.favorite, color: Colors.red, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          'الأمراض المفضلة',
                          style: TextStyles.of(context).headlineLarge(
                            fontSize: 20,
                            fontFamily: AssetsFonts.cairo,
                            fontWeight: FontWeight.bold,
                            color: AssetsColors.kGrey100,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '${_favoriteDiseases.length} مرض',
                          style: TextStyle(
                            fontFamily: AssetsFonts.cairo,
                            color: AssetsColors.kGrey70,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // قائمة المفضلة
                    Expanded(
                      child:
                          _favoriteDiseases.isEmpty
                              ? _buildEmptyFavorites()
                              : ListView.builder(
                                controller: scrollController,
                                itemCount: _favoriteDiseases.length,
                                itemBuilder: (context, index) {
                                  final disease = _favoriteDiseases[index];
                                  return _buildFavoriteDiseaseCard(disease);
                                },
                              ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// بناء حالة المفضلة الفارغة
  Widget _buildEmptyFavorites() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_border, size: 64, color: AssetsColors.kGrey70),
          const SizedBox(height: 16),
          Text(
            'لا توجد أمراض مفضلة',
            style: TextStyle(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.kGrey100,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على ❤️ في أي مرض لإضافته للمفضلة',
            style: TextStyle(
              fontSize: 14,
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kGrey70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة مرض مفضل
  Widget _buildFavoriteDiseaseCard(Disease disease) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AssetsColors.primary.withValues(alpha: 0.1),
          child: Icon(Icons.bug_report, color: AssetsColors.primary),
        ),
        title: Text(
          disease.name,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          disease.details.length > 50
              ? '${disease.details.substring(0, 50)}...'
              : disease.details,
          style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 12),
        ),
        trailing: IconButton(
          icon: Icon(Icons.favorite, color: Colors.red),
          onPressed: () => _toggleFavorite(disease),
        ),
        onTap: () {
          Navigator.pop(context);
          final cubit = context.read<CropsCubit>();
          final plant = cubit.plants.firstWhere(
            (p) => p.id == disease.plantId,
            orElse: () => cubit.plants.first,
          );
          final diseaseType = cubit.diseaseTypes.firstWhere(
            (dt) => dt.id == disease.diseaseTypeId,
            orElse: () => cubit.diseaseTypes.first,
          );
          _showDiseaseDetails(context, disease, plant.name, diseaseType.name);
        },
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AssetsColors.error),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: TextStyles.of(context).headlineMedium(
                fontSize: 18,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => context.read<CropsCubit>().getDiseases(),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        // شريط البحث والفلاتر
        _buildSearchAndFilters(context),

        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAllDiseasesTab(context),
              _buildInsectPestsTab(context),
              _buildFungalDiseasesTab(context),
              _buildComprehensiveGuideTab(context),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث والفلاتر
  Widget _buildSearchAndFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AssetsColors.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن مرض أو آفة...',
                hintStyle: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kGrey70,
                ),
                prefixIcon: Icon(Icons.search, color: AssetsColors.primary),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          icon: Icon(Icons.clear, color: AssetsColors.kGrey70),
                          onPressed: () {
                            _searchController.clear();
                            _onSearchChanged();
                          },
                        )
                        : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey100,
              ),
              onChanged: (value) => _onSearchChanged(),
            ),
          ),

          const SizedBox(height: 12),

          // فلاتر التصفية
          _buildFilters(context),
        ],
      ),
    );
  }

  /// بناء فلاتر التصفية
  Widget _buildFilters(BuildContext context) {
    final cubit = context.read<CropsCubit>();

    return Row(
      children: [
        // فلتر نوع المرض
        Expanded(child: _buildDiseaseTypeFilter(cubit)),

        const SizedBox(width: 12),

        // فلتر المحصول
        Expanded(child: _buildPlantFilter(cubit)),

        const SizedBox(width: 12),

        // زر إعادة تعيين الفلاتر
        IconButton(
          onPressed: _resetFilters,
          icon: Icon(Icons.clear_all, color: AssetsColors.primary),
          tooltip: 'إعادة تعيين الفلاتر',
        ),
      ],
    );
  }

  /// بناء فلتر نوع المرض
  Widget _buildDiseaseTypeFilter(CropsCubit cubit) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: AssetsColors.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int?>(
          value: _selectedDiseaseTypeId,
          hint: Text(
            'نوع المرض',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kGrey70,
              fontSize: 14,
            ),
          ),
          isExpanded: true,
          items: [
            DropdownMenuItem<int?>(
              value: null,
              child: Text(
                'جميع الأنواع',
                style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 14),
              ),
            ),
            ...cubit.diseaseTypes.map(
              (type) => DropdownMenuItem<int?>(
                value: type.id,
                child: Text(
                  type.name,
                  style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 14),
                ),
              ),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _selectedDiseaseTypeId = value;
            });
            _filterDiseases();
          },
        ),
      ),
    );
  }

  /// بناء فلتر المحصول
  Widget _buildPlantFilter(CropsCubit cubit) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: AssetsColors.primary.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int?>(
          value: _selectedPlantId,
          hint: Text(
            'المحصول',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kGrey70,
              fontSize: 14,
            ),
          ),
          isExpanded: true,
          items: [
            DropdownMenuItem<int?>(
              value: null,
              child: Text(
                'جميع المحاصيل',
                style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 14),
              ),
            ),
            ...cubit.plants.map(
              (plant) => DropdownMenuItem<int?>(
                value: plant.id,
                child: Text(
                  plant.name,
                  style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 14),
                ),
              ),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _selectedPlantId = value;
            });
            _filterDiseases();
          },
        ),
      ),
    );
  }

  /// إعادة تعيين الفلاتر
  void _resetFilters() {
    setState(() {
      _selectedDiseaseTypeId = null;
      _selectedPlantId = null;
      _searchController.clear();
    });
    _filterDiseases();
  }

  /// بناء تبويب جميع الأمراض
  Widget _buildAllDiseasesTab(BuildContext context) {
    return _buildDiseasesList(context, _filteredDiseases);
  }

  /// بناء تبويب الآفات الحشرية
  Widget _buildInsectPestsTab(BuildContext context) {
    final insectPests =
        _filteredDiseases.where((disease) {
          // تصفية الآفات الحشرية (نوع المرض = 1 مثلاً)
          return disease.diseaseTypeId == 1;
        }).toList();

    return _buildDiseasesList(context, insectPests);
  }

  /// بناء تبويب الأمراض الفطرية
  Widget _buildFungalDiseasesTab(BuildContext context) {
    final fungalDiseases =
        _filteredDiseases.where((disease) {
          // تصفية الأمراض الفطرية (نوع المرض = 2 مثلاً)
          return disease.diseaseTypeId == 2;
        }).toList();

    return _buildDiseasesList(context, fungalDiseases);
  }

  /// بناء قائمة الأمراض
  Widget _buildDiseasesList(BuildContext context, List<Disease> diseases) {
    if (diseases.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await context.read<CropsCubit>().getDiseases();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: diseases.length,
        itemBuilder: (context, index) {
          final disease = diseases[index];
          return _buildDiseaseCard(context, disease);
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: AssetsColors.kGrey70),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyles.of(context).headlineMedium(
                fontSize: 18,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير معايير البحث أو الفلاتر',
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المرض
  Widget _buildDiseaseCard(BuildContext context, Disease disease) {
    final cubit = context.read<CropsCubit>();

    // الحصول على اسم المحصول
    final plant = cubit.plants.firstWhere(
      (p) => p.id == disease.plantId,
      orElse: () => cubit.plants.first,
    );

    // الحصول على نوع المرض
    final diseaseType = cubit.diseaseTypes.firstWhere(
      (dt) => dt.id == disease.diseaseTypeId,
      orElse: () => cubit.diseaseTypes.first,
    );

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap:
            () => _showDiseaseDetails(
              context,
              disease,
              plant.name,
              diseaseType.name,
            ),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المرض
            if (disease.mainPhoto.isNotEmpty) _buildDiseaseImage(disease),

            // معلومات المرض
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم المرض
                  Text(
                    disease.name,
                    style: TextStyles.of(context).headlineMedium(
                      fontSize: 18,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.kGrey100,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // معلومات إضافية
                  Row(
                    children: [
                      _buildInfoChip(
                        plant.name,
                        Icons.eco,
                        AssetsColors.primary,
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        diseaseType.name,
                        Icons.bug_report,
                        AssetsColors.error,
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // وصف مختصر
                  Text(
                    disease.details.length > 100
                        ? '${disease.details.substring(0, 100)}...'
                        : disease.details,
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 14,
                      fontFamily: AssetsFonts.cairo,
                      color: AssetsColors.kGrey70,
                      height: 1.5,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      // زر عرض التفاصيل
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed:
                              () => _showDiseaseDetails(
                                context,
                                disease,
                                plant.name,
                                diseaseType.name,
                              ),
                          icon: Icon(
                            Icons.info_outline,
                            size: 16,
                            color: AssetsColors.primary,
                          ),
                          label: Text(
                            'التفاصيل والعلاج',
                            style: TextStyle(
                              fontFamily: AssetsFonts.cairo,
                              color: AssetsColors.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(color: AssetsColors.primary),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // زر المفضلة
                      GestureDetector(
                        onTap: () => _toggleFavorite(disease),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color:
                                _isFavorite(disease)
                                    ? Colors.red.withValues(alpha: 0.1)
                                    : Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color:
                                  _isFavorite(disease)
                                      ? Colors.red
                                      : AssetsColors.kGrey70,
                            ),
                          ),
                          child: Icon(
                            _isFavorite(disease)
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color:
                                _isFavorite(disease)
                                    ? Colors.red
                                    : AssetsColors.kGrey70,
                            size: 20,
                          ),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // زر المشاركة
                      GestureDetector(
                        onTap: () => _shareDisease(disease),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue),
                          ),
                          child: Icon(
                            Icons.share,
                            color: Colors.blue,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صورة المرض
  Widget _buildDiseaseImage(Disease disease) {
    try {
      final Uint8List imageBytes = base64Decode(disease.mainPhoto);
      return Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          image: DecorationImage(
            image: MemoryImage(imageBytes),
            fit: BoxFit.cover,
          ),
        ),
      );
    } catch (e) {
      return Container(
        height: 200,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          color: AssetsColors.kGrey30,
        ),
        child: Icon(
          Icons.image_not_supported,
          size: 64,
          color: AssetsColors.kGrey70,
        ),
      );
    }
  }

  /// بناء رقاقة المعلومات
  Widget _buildInfoChip(String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontFamily: AssetsFonts.cairo,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض تفاصيل المرض
  void _showDiseaseDetails(
    BuildContext context,
    Disease disease,
    String plantName,
    String diseaseTypeName,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.95,
            minChildSize: 0.5,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // مقبض السحب
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: AssetsColors.kGrey70,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // عنوان المرض
                    Text(
                      disease.name,
                      style: TextStyles.of(context).headlineLarge(
                        fontSize: 24,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.kGrey100,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // معلومات المرض
                    Row(
                      children: [
                        _buildInfoChip(
                          plantName,
                          Icons.eco,
                          AssetsColors.primary,
                        ),
                        const SizedBox(width: 8),
                        _buildInfoChip(
                          diseaseTypeName,
                          Icons.bug_report,
                          AssetsColors.error,
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // صورة المرض
                    if (disease.mainPhoto.isNotEmpty) ...[
                      Container(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: _buildDiseaseImage(disease),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // تفاصيل المرض
                    Text(
                      'التفاصيل والعلاج',
                      style: TextStyles.of(context).headlineMedium(
                        fontSize: 18,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.kGrey100,
                      ),
                    ),

                    const SizedBox(height: 12),

                    Expanded(
                      child: SingleChildScrollView(
                        controller: scrollController,
                        child: Text(
                          disease.details,
                          style: TextStyles.of(context).bodyMedium(
                            fontSize: 16,
                            fontFamily: AssetsFonts.cairo,
                            color: AssetsColors.kGrey70,
                            height: 1.8,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // زر الإغلاق
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AssetsColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'إغلاق',
                          style: TextStyle(
                            fontSize: 16,
                            fontFamily: AssetsFonts.cairo,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// التحقق من كون المرض مفضل
  bool _isFavorite(Disease disease) {
    return _favoriteDiseases.any((d) => d.id == disease.id);
  }

  /// تبديل حالة المفضلة
  void _toggleFavorite(Disease disease) {
    setState(() {
      if (_isFavorite(disease)) {
        _favoriteDiseases.removeWhere((d) => d.id == disease.id);
        _showFavoriteSnackBar('تم إزالة ${disease.name} من المفضلة', false);
      } else {
        _favoriteDiseases.add(disease);
        _showFavoriteSnackBar('تم إضافة ${disease.name} للمفضلة', true);
      }
    });
  }

  /// عرض رسالة المفضلة
  void _showFavoriteSnackBar(String message, bool isAdded) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isAdded ? Icons.favorite : Icons.favorite_border,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: isAdded ? Colors.red : Colors.grey,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        action:
            isAdded
                ? SnackBarAction(
                  label: 'عرض المفضلة',
                  textColor: Colors.white,
                  onPressed: () => _showFavorites(context),
                )
                : null,
      ),
    );
  }

  /// مشاركة المرض
  void _shareDisease(Disease disease) {
    // يمكن استخدام مكتبة share_plus هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.share, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              'تم نسخ معلومات المرض',
              style: TextStyle(fontFamily: AssetsFonts.cairo),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// عرض الميزات المتقدمة
  void _showAdvancedFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // رأس الصفحة
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AssetsColors.primary,
                        AssetsColors.primary.withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.auto_awesome, color: Colors.white, size: 28),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'الميزات المتقدمة',
                          style: TextStyle(
                            fontFamily: AssetsFonts.cairo,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close, color: Colors.white),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),

                // قائمة الميزات
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.all(20),
                    children: [
                      _buildFeatureCard(
                        'التشخيص بالذكاء الاصطناعي',
                        'استخدم الكاميرا والذكاء الاصطناعي لتشخيص الآفات والأمراض',
                        Icons.psychology,
                        Colors.blue,
                        () => _navigateToAIDiagnosis(),
                      ),
                      _buildFeatureCard(
                        'خريطة انتشار الآفات',
                        'اعرض انتشار الآفات والأمراض على الخريطة التفاعلية',
                        Icons.map,
                        Colors.green,
                        () => _navigateToPestMap(),
                      ),
                      _buildFeatureCard(
                        'تقارير المجتمع',
                        'شارك واستفد من تجارب المزارعين الآخرين',
                        Icons.people,
                        Colors.orange,
                        () => _navigateToCommunity(),
                      ),
                      _buildFeatureCard(
                        'نظام الإنذار المبكر',
                        'احصل على تنبيهات ذكية حول الآفات والأمراض المحتملة',
                        Icons.warning,
                        Colors.red,
                        () => _navigateToEarlyWarning(),
                      ),
                      _buildFeatureCard(
                        'التعلم التفاعلي',
                        'تعلم من خلال الدروس والاختبارات والألعاب التفاعلية',
                        Icons.school,
                        Colors.purple,
                        () => _navigateToInteractiveLearning(),
                      ),
                      _buildFeatureCard(
                        'التحليل الجيني للنباتات',
                        'تحليل الحمض النووي لتحديد المقاومة الجينية للآفات والأمراض',
                        Icons.biotech,
                        Colors.cyan,
                        () => _navigateToGeneticAnalysis(),
                      ),
                      _buildFeatureCard(
                        'التشخيص بالواقع المعزز',
                        'استخدم الكاميرا والواقع المعزز لتشخيص فوري ودقيق',
                        Icons.view_in_ar,
                        Colors.indigo,
                        () => _navigateToARDiagnosis(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// بناء بطاقة ميزة
  Widget _buildFeatureCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AssetsColors.kGrey100,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 13,
                          color: AssetsColors.kGrey70,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.arrow_forward_ios, color: color, size: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// الانتقال إلى التشخيص بالذكاء الاصطناعي
  void _navigateToAIDiagnosis() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AIDiagnosisPage()),
    );
  }

  /// الانتقال إلى خريطة انتشار الآفات
  void _navigateToPestMap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PestDistributionMapPage()),
    );
  }

  /// الانتقال إلى تقارير المجتمع
  void _navigateToCommunity() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CommunityReportsPage()),
    );
  }

  /// الانتقال إلى نظام الإنذار المبكر
  void _navigateToEarlyWarning() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const EarlyWarningSystemPage()),
    );
  }

  /// الانتقال إلى التعلم التفاعلي
  void _navigateToInteractiveLearning() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InteractiveLearningPage()),
    );
  }

  /// الانتقال إلى التحليل الجيني
  void _navigateToGeneticAnalysis() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const GeneticPlantAnalysisPage()),
    );
  }

  /// الانتقال إلى التشخيص بالواقع المعزز
  void _navigateToARDiagnosis() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ARPlantDiagnosisPage()),
    );
  }

  /// الانتقال إلى صفحة المصادر المرجعية
  void _navigateToReferenceSources() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReferenceSourcesPage()),
    );
  }

  /// إظهار معلومات حول التطبيق
  void _showInfoDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'حول دليل الآفات والأمراض',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Text(
              'هذا الدليل يحتوي على معلومات شاملة عن أهم الآفات والأمراض التي تصيب المحاصيل الزراعية.\n\n'
              'المصادر المستخدمة:\n'
              '• CABI - المعيار الذهبي في علوم النبات\n'
              '• EPPO - المنظمة الأوروبية لوقاية النباتات\n'
              '• UC IPM - جامعة كاليفورنيا\n'
              '• مصادر عربية موثوقة\n\n'
              'للحصول على معلومات أكثر تفصيلاً، راجع قسم "المصادر المرجعية".',
              style: TextStyle(fontFamily: AssetsFonts.cairo, fontSize: 14),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'موافق',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _navigateToReferenceSources();
                },
                child: Text(
                  'المصادر المرجعية',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.dufaultGreencolor,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  /// بناء تبويب الدليل الشامل
  Widget _buildComprehensiveGuideTab(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildStatisticsCards(),
          const SizedBox(height: 20),

          // فلاتر التصفية
          _buildFilterChips(),
          const SizedBox(height: 20),

          // قائمة الآفات والأمراض
          _buildPestsList(),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الآفات',
            _statistics['total']?.toString() ?? '0',
            Icons.bug_report,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: _buildStatCard(
            'الأمراض',
            _statistics['diseases']?.toString() ?? '0',
            Icons.local_hospital,
            Colors.red,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: _buildStatCard(
            'الآفات',
            _statistics['pests']?.toString() ?? '0',
            Icons.pest_control,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 30),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: AssetsFonts.cairo,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontFamily: AssetsFonts.cairo,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء رقائق التصفية
  Widget _buildFilterChips() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تصفية حسب النوع:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: AssetsFonts.cairo,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children:
              ['الكل', 'آفات', 'أمراض'].map((filter) {
                return FilterChip(
                  label: Text(filter),
                  selected: _selectedFilter == filter,
                  onSelected: (selected) {
                    setState(() {
                      _selectedFilter = filter;
                      _filterPests();
                    });
                  },
                );
              }).toList(),
        ),
        const SizedBox(height: 16),
        Text(
          'تصفية حسب الخطورة:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: AssetsFonts.cairo,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children:
              ['الكل', 'عالي جداً', 'عالي', 'متوسط', 'منخفض'].map((severity) {
                return FilterChip(
                  label: Text(severity),
                  selected: _selectedSeverity == severity,
                  onSelected: (selected) {
                    setState(() {
                      _selectedSeverity = severity;
                      _filterPests();
                    });
                  },
                );
              }).toList(),
        ),
      ],
    );
  }

  /// بناء قائمة الآفات والأمراض
  Widget _buildPestsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النتائج (${_filteredPestsData.length}):',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: AssetsFonts.cairo,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _filteredPestsData.length,
          itemBuilder: (context, index) {
            final pest = _filteredPestsData[index];
            return _buildPestCard(pest);
          },
        ),
      ],
    );
  }

  /// بناء بطاقة آفة أو مرض
  Widget _buildPestCard(Map<String, dynamic> pest) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان ومستوى الخطورة
            Row(
              children: [
                Expanded(
                  child: Text(
                    pest['name'],
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: AssetsFonts.cairo,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getSeverityColor(pest['severity']),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    pest['severity'],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // الاسم العلمي
            Text(
              pest['scientificName'],
              style: TextStyle(
                fontSize: 14,
                fontStyle: FontStyle.italic,
                color: Colors.grey[600],
                fontFamily: AssetsFonts.cairo,
              ),
            ),
            const SizedBox(height: 8),

            // الوصف
            Text(
              pest['description'],
              style: TextStyle(fontSize: 14, fontFamily: AssetsFonts.cairo),
            ),
            const SizedBox(height: 12),

            // المحاصيل المتأثرة
            Wrap(
              spacing: 4,
              children:
                  (pest['crops'] as List<String>).map((crop) {
                    return Chip(
                      label: Text(crop, style: const TextStyle(fontSize: 12)),
                      backgroundColor: AssetsColors.dufaultGreencolor
                          .withValues(alpha: 0.1),
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون مستوى الخطورة
  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'عالي جداً':
        return Colors.red.shade700;
      case 'عالي':
        return Colors.red;
      case 'متوسط':
        return Colors.orange;
      case 'منخفض':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

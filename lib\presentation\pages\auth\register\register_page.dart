
import '../../../../imports.dart';
import 'optimized_register_screen.dart';

// ignore: must_be_immutable
class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => RegisterCubit(
            RepositoryProvider.of<AuthRepository>(context),
            context.read<AuthCubit>(),
          ),
      child: const OptimizedRegisterScreen(),
    );
  }
}

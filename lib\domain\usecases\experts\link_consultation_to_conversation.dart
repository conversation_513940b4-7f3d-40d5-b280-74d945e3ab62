import '../../../core/utils/logging/logger_service.dart';
import '../../../data/models/experts/index.dart';
import '../../entities/marketing_products/conversation_entity.dart';
import '../../entities/marketing_products/message_entity.dart';
import '../../repositories/experts_repository_interface.dart';
import '../../repositories/marketing_products_repository_interface.dart';

/// حالة استخدام ربط الاستشارة بالمحادثة
///
/// تتولى هذه الحالة عملية ربط الاستشارات بنظام المحادثات الموجود
/// لتمكين التواصل المباشر بين المزارع والخبير
class LinkConsultationToConversation {
  // final ExpertsRepositoryInterface _expertsRepository; // سيتم استخدامه لاحقاً
  final MarketingProductsRepositoryInterface _conversationsRepository;

  /// إنشاء حالة الاستخدام
  LinkConsultationToConversation({
    required ExpertsRepositoryInterface expertsRepository, // سيتم استخدامه لاحقاً
    required MarketingProductsRepositoryInterface conversationsRepository,
  }) : // _expertsRepository = expertsRepository, // سيتم استخدامه لاحقاً
       _conversationsRepository = conversationsRepository;

  /// إنشاء محادثة للاستشارة
  ///
  /// [consultation] بيانات الاستشارة
  ///
  /// Returns معرف المحادثة الجديدة
  Future<String> createConversationForConsultation(
    ConsultationModel consultation,
  ) async {
    try {
      LoggerService.info('بدء إنشاء محادثة للاستشارة: ${consultation.title}');

      // التحقق من وجود محادثة موجودة
      final existingConversation = await _conversationsRepository
          .findExistingConversation(
            participantIds: [consultation.farmerId, consultation.expertId],
          );

      if (existingConversation != null) {
        LoggerService.info(
          'تم العثور على محادثة موجودة: ${existingConversation.id}',
        );
        return existingConversation.id;
      }

      // إنشاء محادثة جديدة
      final conversation = ConversationEntity.create(
        participantIds: [consultation.farmerId, consultation.expertId],
        participantNames: [consultation.farmerName, consultation.expertName],
        customTitle: 'استشارة: ${consultation.title}',
      );

      final conversationId = await _conversationsRepository.createConversation(
        conversation,
      );
      LoggerService.info('تم إنشاء المحادثة: $conversationId');

      // إرسال رسالة ترحيبية
      await _sendWelcomeMessage(conversationId, consultation);

      return conversationId;
    } catch (e) {
      LoggerService.error('خطأ في إنشاء محادثة للاستشارة', error: e);
      rethrow;
    }
  }

  /// البحث عن محادثة موجودة للاستشارة
  ///
  /// [farmerId] معرف المزارع
  /// [expertId] معرف الخبير
  ///
  /// Returns معرف المحادثة إذا وجدت، null إذا لم توجد
  Future<String?> findExistingConversation({
    required String farmerId,
    required String expertId,
  }) async {
    try {
      LoggerService.info('البحث عن محادثة موجودة بين $farmerId و $expertId');

      final conversation = await _conversationsRepository
          .findExistingConversation(participantIds: [farmerId, expertId]);

      if (conversation != null) {
        LoggerService.info('تم العثور على محادثة موجودة: ${conversation.id}');
        return conversation.id;
      }

      LoggerService.info('لم يتم العثور على محادثة موجودة');
      return null;
    } catch (e) {
      LoggerService.error('خطأ في البحث عن محادثة موجودة', error: e);
      return null;
    }
  }

  /// إرسال رسالة ترحيبية في المحادثة
  Future<void> _sendWelcomeMessage(
    String conversationId,
    ConsultationModel consultation,
  ) async {
    try {
      final welcomeMessage = '''
مرحباً ${consultation.expertName}،

تم إنشاء استشارة جديدة من المزارع ${consultation.farmerName}:

📋 العنوان: ${consultation.title}
📂 الفئة: ${consultation.category}
⚡ الأولوية: ${consultation.priorityText}

📝 الوصف:
${consultation.description}

يمكنك الآن مراجعة الاستشارة والرد عليها من خلال هذه المحادثة.
      ''';

      // إنشاء رسالة النظام
      final systemMessage = MessageEntity(
        id: '',
        conversationId: conversationId,
        senderId: 'system',
        senderName: 'النظام',
        receiverId: consultation.expertId,
        receiverName: consultation.expertName,
        messageType: MessageTypeEntity.text,
        content: welcomeMessage,
        sentAt: DateTime.now(),
        isRead: false,
        isDeleted: false,
        metadata: {
          'consultationId': consultation.id,
          'messageType': 'consultation_created',
        },
      );

      await _conversationsRepository.sendMessage(systemMessage);
      LoggerService.info('تم إرسال رسالة الترحيب في المحادثة');
    } catch (e) {
      LoggerService.error('خطأ في إرسال رسالة الترحيب', error: e);
      // لا نرمي الخطأ هنا لأن المحادثة تم إنشاؤها بنجاح
    }
  }

  /// إرسال رسالة تحديث حالة الاستشارة
  ///
  /// [conversationId] معرف المحادثة
  /// [consultation] بيانات الاستشارة
  /// [statusMessage] رسالة الحالة
  Future<void> sendStatusUpdateMessage({
    required String conversationId,
    required ConsultationModel consultation,
    required String statusMessage,
  }) async {
    try {
      LoggerService.info('إرسال رسالة تحديث حالة الاستشارة');

      final message = MessageEntity(
        id: '',
        conversationId: conversationId,
        senderId: 'system',
        senderName: 'النظام',
        receiverId: consultation.farmerId,
        receiverName: consultation.farmerName,
        messageType: MessageTypeEntity.text,
        content: statusMessage,
        sentAt: DateTime.now(),
        isRead: false,
        isDeleted: false,
        metadata: {
          'consultationId': consultation.id,
          'messageType': 'status_update',
        },
      );

      await _conversationsRepository.sendMessage(message);
      LoggerService.info('تم إرسال رسالة تحديث الحالة');
    } catch (e) {
      LoggerService.error('خطأ في إرسال رسالة تحديث الحالة', error: e);
    }
  }

  /// إرسال رسالة رد الخبير
  ///
  /// [conversationId] معرف المحادثة
  /// [consultation] بيانات الاستشارة
  /// [response] رد الخبير
  Future<void> sendExpertResponseMessage({
    required String conversationId,
    required ConsultationModel consultation,
    required String response,
  }) async {
    try {
      LoggerService.info('إرسال رسالة رد الخبير');

      final responseMessage = '''
رد الخبير ${consultation.expertName} على الاستشارة:

📋 ${consultation.title}

💬 الرد:
$response

تم الرد على الاستشارة بنجاح. يمكنك الآن تقييم الاستشارة إذا كنت راضياً عن الرد.
      ''';

      final message = MessageEntity(
        id: '',
        conversationId: conversationId,
        senderId: consultation.expertId,
        senderName: consultation.expertName,
        receiverId: consultation.farmerId,
        receiverName: consultation.farmerName,
        messageType: MessageTypeEntity.text,
        content: responseMessage,
        sentAt: DateTime.now(),
        isRead: false,
        isDeleted: false,
        metadata: {
          'consultationId': consultation.id,
          'messageType': 'expert_response',
          'response': response,
        },
      );

      await _conversationsRepository.sendMessage(message);
      LoggerService.info('تم إرسال رسالة رد الخبير');
    } catch (e) {
      LoggerService.error('خطأ في إرسال رسالة رد الخبير', error: e);
    }
  }

  /// إرسال رسالة إكمال الاستشارة
  ///
  /// [conversationId] معرف المحادثة
  /// [consultation] بيانات الاستشارة
  /// [rating] التقييم (اختياري)
  Future<void> sendCompletionMessage({
    required String conversationId,
    required ConsultationModel consultation,
    double? rating,
  }) async {
    try {
      LoggerService.info('إرسال رسالة إكمال الاستشارة');

      final completionMessage =
          rating != null
              ? '''
تم إكمال الاستشارة "${consultation.title}" بنجاح!

⭐ التقييم: ${rating.toStringAsFixed(1)}/5.0

شكراً لاستخدام خدمة الاستشارات الزراعية. نتمنى أن تكون الاستشارة مفيدة لك.
          '''
              : '''
تم إكمال الاستشارة "${consultation.title}" بنجاح!

شكراً لاستخدام خدمة الاستشارات الزراعية.
          ''';

      final message = MessageEntity(
        id: '',
        conversationId: conversationId,
        senderId: 'system',
        senderName: 'النظام',
        receiverId: consultation.farmerId,
        receiverName: consultation.farmerName,
        messageType: MessageTypeEntity.text,
        content: completionMessage,
        sentAt: DateTime.now(),
        isRead: false,
        isDeleted: false,
        metadata: {
          'consultationId': consultation.id,
          'messageType': 'consultation_completed',
          if (rating != null) 'rating': rating.toString(),
        },
      );

      await _conversationsRepository.sendMessage(message);
      LoggerService.info('تم إرسال رسالة إكمال الاستشارة');
    } catch (e) {
      LoggerService.error('خطأ في إرسال رسالة إكمال الاستشارة', error: e);
    }
  }
}

import 'package:flutter/material.dart';
import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// شريط التطبيق المخصص
///
/// ويدجت موحد لشريط التطبيق مع تصميم متسق عبر التطبيق
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان الشريط
  final String title;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص والأيقونات
  final Color? foregroundColor;

  /// الإجراءات في الشريط
  final List<Widget>? actions;

  /// هل يظهر زر الرجوع
  final bool showBackButton;

  /// دالة مخصصة للرجوع
  final VoidCallback? onBackPressed;

  /// أيقونة مخصصة للرجوع
  final Widget? leading;

  /// ارتفاع الشريط
  final double? elevation;

  /// هل الشريط مركزي
  final bool centerTitle;

  /// إنشاء شريط التطبيق المخصص
  const CustomAppBar({
    super.key,
    required this.title,
    this.backgroundColor,
    this.foregroundColor,
    this.actions,
    this.showBackButton = true,
    this.onBackPressed,
    this.leading,
    this.elevation,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyles.of(context).headlineSmall(
          color: foregroundColor ?? AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.textOnPrimary,
      elevation: elevation ?? 2,
      centerTitle: centerTitle,
      leading: leading ?? (showBackButton ? _buildBackButton(context) : null),
      actions: actions,
      automaticallyImplyLeading: showBackButton,
    );
  }

  /// بناء زر الرجوع
  Widget? _buildBackButton(BuildContext context) {
    if (!showBackButton) return null;

    return IconButton(
      onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      icon: Icon(
        Icons.arrow_back_ios,
        color: foregroundColor ?? AppColors.textOnPrimary,
      ),
      tooltip: 'رجوع',
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق بسيط مع عنوان فقط
class SimpleAppBar extends CustomAppBar {
  const SimpleAppBar({
    super.key,
    required super.title,
    super.backgroundColor,
    super.foregroundColor,
  });
}

/// شريط تطبيق مع بحث
class SearchAppBar extends CustomAppBar {
  /// دالة البحث
  final VoidCallback? onSearchPressed;

  /// نص البحث الحالي
  final String? searchQuery;

  /// دالة تغيير نص البحث
  final ValueChanged<String>? onSearchChanged;

  /// هل وضع البحث نشط
  final bool isSearchActive;

  const SearchAppBar({
    super.key,
    required super.title,
    this.onSearchPressed,
    this.searchQuery,
    this.onSearchChanged,
    this.isSearchActive = false,
    super.backgroundColor,
    super.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    if (isSearchActive) {
      return AppBar(
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: foregroundColor ?? AppColors.textOnPrimary,
        elevation: elevation ?? 2,
        leading: IconButton(
          onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios,
            color: foregroundColor ?? AppColors.textOnPrimary,
          ),
        ),
        title: TextField(
          onChanged: onSearchChanged,
          style: TextStyle(
            color: foregroundColor ?? AppColors.textOnPrimary,
          ),
          decoration: InputDecoration(
            hintText: 'ابحث...',
            hintStyle: TextStyle(
              color: (foregroundColor ?? AppColors.textOnPrimary).withValues(alpha: 0.7),
            ),
            border: InputBorder.none,
          ),
          autofocus: true,
        ),
        actions: [
          if (searchQuery?.isNotEmpty == true)
            IconButton(
              onPressed: () => onSearchChanged?.call(''),
              icon: Icon(
                Icons.clear,
                color: foregroundColor ?? AppColors.textOnPrimary,
              ),
            ),
        ],
      );
    }

    return AppBar(
      title: Text(
        title,
        style: TextStyles.of(context).headlineSmall(
          color: foregroundColor ?? AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.textOnPrimary,
      elevation: elevation ?? 2,
      centerTitle: centerTitle,
      leading: leading ?? (showBackButton ? _buildBackButton(context) : null),
      actions: [
        IconButton(
          onPressed: onSearchPressed,
          icon: Icon(
            Icons.search,
            color: foregroundColor ?? AppColors.textOnPrimary,
          ),
          tooltip: 'بحث',
        ),
        ...?actions,
      ],
    );
  }
}

/// شريط تطبيق مع قائمة منسدلة
class MenuAppBar extends CustomAppBar {
  /// عناصر القائمة
  final List<PopupMenuEntry> menuItems;

  /// دالة اختيار عنصر من القائمة
  final ValueChanged<dynamic>? onMenuSelected;

  const MenuAppBar({
    super.key,
    required super.title,
    required this.menuItems,
    this.onMenuSelected,
    super.backgroundColor,
    super.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyles.of(context).headlineSmall(
          color: foregroundColor ?? AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: foregroundColor ?? AppColors.textOnPrimary,
      elevation: elevation ?? 2,
      centerTitle: centerTitle,
      leading: leading ?? (showBackButton ? _buildBackButton(context) : null),
      actions: [
        PopupMenuButton(
          onSelected: onMenuSelected,
          itemBuilder: (context) => menuItems,
          icon: Icon(
            Icons.more_vert,
            color: foregroundColor ?? AppColors.textOnPrimary,
          ),
        ),
        ...?actions,
      ],
    );
  }
}


import 'package:animate_do/animate_do.dart';

import '../../../../core/utils/validators/form_validators.dart';

import '../../../../imports.dart';
import '../../../widgets/auth/login/fixed_size_text_field.dart';
import '../../../widgets/auth/login/remember_me_checkbox.dart';
import '../../../widgets/auth/shared/index.dart';
import '../../../widgets/shared/dialogs/exit_dialog.dart';
import '../../../widgets/shared/error_message.dart';
import '../../../widgets/shared/progress_indicator_overlay.dart';

// المكونات المحسنة

// الصفحات

// المكونات المشتركة

// الثوابت والخدمات

/// صفحة تسجيل الدخول المحسنة
///
/// تستخدم هذه الصفحة المكونات المحسنة مثل OptimizedButton و OptimizedTextField
/// لتحسين تجربة المستخدم وتوحيد مظهر التطبيق.
class OptimizedLoginScreen extends StatefulWidget {
  const OptimizedLoginScreen({super.key});

  @override
  State<OptimizedLoginScreen> createState() => _OptimizedLoginScreenState();
}

class _OptimizedLoginScreenState extends State<OptimizedLoginScreen> {
  /// متحكم حقل البريد الإلكتروني
  final _emailController = TextEditingController();

  /// متحكم حقل كلمة المرور
  final _passwordController = TextEditingController();

  /// مفتاح النموذج للتحقق من صحة الإدخال
  final _formKey = GlobalKey<FormState>();

  /// حالة عرض رسائل الخطأ
  bool _showErrorMessages = false;

  /// حالة تذكر المستخدم
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Form(
          key: _formKey,
          autovalidateMode: _showErrorMessages
              ? AutovalidateMode.always
              : AutovalidateMode.disabled,
          child: SingleChildScrollView(
            child: BlocConsumer<LoginCubit, LoginState>(
              listener: (context, state) {
                if (state is LoginSuccess) {
                  _handleLoginSuccess(context, state);
                } else if (state is LoginFailure) {
                  _showErrorMessage(context, state.error);
                  // تفعيل عرض رسائل الخطأ عند حدوث خطأ
                  setState(() {
                    _showErrorMessages = true;
                  });
                }
              },
              builder: (context, state) {
                return ProgressIndicatorOverlay(
                  isVisible: state is LoginLoading,
                  message: AppConstants.infoProcessing,
                  child: Column(
                    children: <Widget>[
                      _buildHeader(),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: Dimensions.paddingL),
                        child: Column(
                          children: <Widget>[
                            _buildLoginForm(state),
                            SizedBox(height: Dimensions.spacingM),
                            _buildForgotPasswordLink(context),
                            SizedBox(height: Dimensions.spacingL),
                            _buildRegisterLink(context),
                            SizedBox(height: Dimensions.spacingL),
                            _buildDivider(),
                            SizedBox(height: Dimensions.spacingL),
                            _buildSocialLoginOptions(context),
                            SizedBox(height: Dimensions.spacingXXL),
                          ],
                        ),
                      )
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  /// التحقق قبل الرجوع
  Future<bool> _onWillPop() async {
    // التحقق مما إذا كان المستخدم قد رأى صفحة الترحيب من قبل
    if (isOnBoarding) {
      return await handleAppExit(context);
    }

    // السماح بالرجوع إذا لم يكن المستخدم قد رأى صفحة الترحيب من قبل
    return true;
  }

  /// بناء رأس الصفحة مع الصور والعنوان
  Widget _buildHeader() {
    return AuthHeader(title: "تسجيل الدخول");
  }

  /// بناء نموذج تسجيل الدخول (البريد الإلكتروني وكلمة المرور وزر تسجيل الدخول)
  Widget _buildLoginForm(state) {
    return FadeInUp(
      duration: const Duration(milliseconds: 1800),
      child: Column(
        children: <Widget>[
          // حقل البريد الإلكتروني
          FixedSizeTextField(
            controller: _emailController,
            labelText: 'البريد الإلكتروني',
            hintText: 'أدخل بريدك الإلكتروني',
            keyboardType: TextInputType.emailAddress,
            prefixIcon: Icons.email,
            validator: FormValidators.validateEmail,
            height: 56.0,
          ),

          SizedBox(height: Dimensions.spacingM),

          // حقل كلمة المرور
          FixedSizeTextField(
            controller: _passwordController,
            labelText: 'كلمة المرور',
            hintText: 'أدخل كلمة المرور',
            isPassword: true,
            prefixIcon: Icons.lock,
            validator: FormValidators.validatePassword,
            height: 56.0,
          ),

          SizedBox(height: Dimensions.spacingM),

          // خيار تذكرني
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RememberMeCheckbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
              ),
            ],
          ),

          SizedBox(height: Dimensions.spacingL),

          // عرض رسالة خطأ إذا كانت موجودة
          if (state is LoginFailure) ...[
            ErrorMessage(
              message: state.error,
              padding: const EdgeInsets.only(bottom: 16.0),
            ),
          ],

          // زر تسجيل الدخول
          OptimizedButton(
            text: 'تسجيل الدخول',
            type: OptimizedButtonType.primary,
            size: OptimizedButtonSize.large,
            isFullWidth: true,
            isLoading: state is LoginLoading,
            onPressed: _handleLoginButtonPressed,
          ),
        ],
      ),
    );
  }

  /// بناء رابط التسجيل
  Widget _buildRegisterLink(BuildContext context) {
    return FadeInUp(
      duration: const Duration(milliseconds: 1900),
      child: RichTextLink.registerTextArabic(onTap: () {
        Navigator.pushNamed(context, RouteConstants.register);
      }),
    );
  }

  /// بناء رابط نسيت كلمة المرور
  Widget _buildForgotPasswordLink(BuildContext context) {
    return FadeInUp(
      duration: const Duration(milliseconds: 1900),
      child: Align(
        alignment: Alignment.centerLeft,
        child: TextButton(
          onPressed: () => _handleForgotPassword(context),
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: const Size(0, 30),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            'نسيت كلمة المرور؟',
            style: TextStyle(
              color: Colors.blue,
              fontSize: 14,
              fontFamily: AssetsFonts.messiri,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء الفاصل
  Widget _buildDivider() {
    return FadeInUp(
      duration: const Duration(milliseconds: 1900),
      child: const TextWithDivider(),
    );
  }

  /// بناء خيارات تسجيل الدخول الاجتماعية
  Widget _buildSocialLoginOptions(BuildContext context) {
    return SocialLoginButtons(
      onGoogleTap: () => _handleGoogleLogin(context),
      onFacebookTap: () => _handleFacebookLogin(context),
      onPhoneTap: () => _handlePhoneLogin(context),
    );
  }

  /// معالجة النقر على زر تسجيل الدخول

  void _handleGoogleLogin(BuildContext context) {
    context.read<LoginCubit>().loginWithGoogle();
  }

  /// معالجة تسجيل الدخول باستخدام Facebook
  void _handleFacebookLogin(BuildContext context) {
    // تنفيذ تسجيل الدخول باستخدام Facebook
  }

  /// معالجة تسجيل الدخول باستخدام رقم الهاتف
  void _handlePhoneLogin(BuildContext context) {
    // الانتقال إلى صفحة إدخال رقم الهاتف
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PhoneInputPage(),
      ),
    );
  }

  /// معالجة نسيت كلمة المرور
  void _handleForgotPassword(BuildContext context) {
    // عرض مربع حوار لإدخال البريد الإلكتروني
    showDialog(
      context: context,
      builder: (context) {
        final emailController = TextEditingController();
        final formKey = GlobalKey<FormState>();

        return AlertDialog(
          title: Text(
            'إعادة تعيين كلمة المرور',
            style: TextStyle(fontFamily: AssetsFonts.messiri),
            textAlign: TextAlign.center,
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور',
                  style: TextStyle(fontFamily: AssetsFonts.messiri),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                FixedSizeTextField(
                  controller: emailController,
                  labelText: 'البريد الإلكتروني',
                  hintText: 'أدخل بريدك الإلكتروني',
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: Icons.email,
                  validator: FormValidators.validateEmail,
                  height: 56.0,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(fontFamily: AssetsFonts.messiri),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  Navigator.pop(context);
                  // استدعاء دالة إعادة تعيين كلمة المرور
                  context
                      .read<LoginCubit>()
                      .resetPassword(emailController.text);
                }
              },
              child: Text(
                'إرسال',
                style: TextStyle(fontFamily: AssetsFonts.messiri),
              ),
            ),
          ],
        );
      },
    );
  }

  /// معالجة النقر على زر تسجيل الدخول
  void _handleLoginButtonPressed() {
    // تفعيل عرض رسائل الخطأ
    setState(() {
      _showErrorMessages = true;
    });

    if (_formKey.currentState!.validate()) {
      try {
        // نقل قيم الحقول إلى LoginCubit
        final loginCubit = context.read<LoginCubit>();
        loginCubit.emailController.text = _emailController.text;
        loginCubit.passwordController.text = _passwordController.text;

        // تم تعديل LoginCubit لتخطي التحقق من الصحة
        // لأننا قمنا بالتحقق هنا بالفعل

        // حفظ حالة تذكرني في التخزين المحلي إذا كانت مفعلة
        if (_rememberMe) {
          SharedPrefs.setString('remembered_email', _emailController.text);
        } else {
          SharedPrefs.remove('remembered_email');
        }

        LoggerService.debug(
          'محاولة تسجيل الدخول بالبريد الإلكتروني: ${_emailController.text}',
          tag: 'OptimizedLoginScreen',
        );

        // استدعاء دالة تسجيل الدخول
        loginCubit.loginWithEmailAndPassword();

        LoggerService.debug(
          'تم استدعاء loginWithEmailAndPassword بنجاح',
          tag: 'OptimizedLoginScreen',
        );
      } catch (e) {
        LoggerService.error(
          'خطأ في استدعاء loginWithEmailAndPassword',
          error: e,
          tag: 'OptimizedLoginScreen',
        );

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } else {
      // عرض رسالة للمستخدم بأن هناك أخطاء في النموذج
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppConstants.errorValidation),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// معالجة نجاح تسجيل الدخول
  void _handleLoginSuccess(BuildContext context, LoginSuccess state) {
    // حفظ المتغيرات المطلوبة قبل العملية غير المتزامنة
    final userId = state.user.id;
    final userPhone = state.user.phone.isNotEmpty ? state.user.phone : null;
    final authRepo = context.read<LoginCubit>().authRepository;

    LoggerService.debug(
      'معالجة نجاح تسجيل الدخول: معرف المستخدم = $userId، رقم الهاتف = $userPhone',
      tag: 'OptimizedLoginScreen',
    );

    // تنفيذ التوجيه مباشرة بدون تأخير
    _navigateAfterLogin(context, userId, userPhone, authRepo);
  }

  /// التوجيه بعد تسجيل الدخول
  Future<void> _navigateAfterLogin(
    BuildContext context,
    String userId,
    String? userPhone,
    AuthRepository authRepo,
  ) async {
    try {
      LoggerService.debug(
        'التحقق من اكتمال ملف المستخدم...',
        tag: 'OptimizedLoginScreen',
      );

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // التحقق مما إذا كان المستخدم قد أكمل ملفه الشخصي
      final hasCompletedProfile =
          await authRepo.hasUserCompletedProfile(userId);

      LoggerService.debug(
        'نتيجة التحقق من اكتمال ملف المستخدم: $hasCompletedProfile',
        tag: 'OptimizedLoginScreen',
      );

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.pop(context);
      }

      // عرض رسالة نجاح للمستخدم
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppConstants.successLogin),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 1),
          ),
        );
      }

      // التحقق من أن الـ State لا يزال مرتبطًا بالشجرة
      if (context.mounted) {
        if (hasCompletedProfile) {
          // إذا كان المستخدم قد أكمل ملفه الشخصي، توجيهه مباشرة إلى الصفحة الرئيسية
          LoggerService.debug(
            'توجيه المستخدم إلى الصفحة الرئيسية',
            tag: 'OptimizedLoginScreen',
          );

          // استخدام AppRouter بدلاً من Navigator مباشرة
          AppRouter.navigateToHome(context);
        } else {
          // إذا لم يكن المستخدم قد أكمل ملفه الشخصي، توجيهه إلى صفحة إكمال البروفايل
          LoggerService.debug(
            'توجيه المستخدم إلى صفحة إكمال البروفايل',
            tag: 'OptimizedLoginScreen',
          );

          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => RegisterProfilePage(
                // تمرير رقم الهاتف فقط إذا كان موجودًا
                phoneNumber: userPhone,
              ),
            ),
            (route) => false,
          );
        }
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في التحقق من اكتمال ملف المستخدم',
        error: e,
        tag: 'OptimizedLoginScreen',
      );

      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (context.mounted) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      }

      if (context.mounted) {
        _showErrorMessage(context, 'حدث خطأ أثناء التحقق من ملف المستخدم');
      }
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    // استرجاع البريد الإلكتروني المحفوظ إذا كان موجودًا
    final rememberedEmail = SharedPrefs.getString('remembered_email');
    if (rememberedEmail != null && rememberedEmail.isNotEmpty) {
      _emailController.text = rememberedEmail;
      _rememberMe = true;
    }
  }
}

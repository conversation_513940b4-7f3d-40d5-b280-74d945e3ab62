import 'package:flutter/material.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../core/utils/data_cache_manager.dart';
import '../../../data/models/experts/agricultural_expert_model.dart';

/// ويدجت الإحصائيات السريعة للخبراء الزراعيين
///
/// يعرض إحصائيات تفاعلية ومفيدة عن الخبراء المتاحين
/// مطبق وفق التفضيلات الـ18 مع عدم التكرار
class ExpertsStatisticsWidget extends StatefulWidget {
  /// قائمة الخبراء لحساب الإحصائيات
  final List<AgriculturalExpertModel> experts;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  /// دالة استدعاء عند النقر على إحصائية
  final Function(String statisticType)? onStatisticTap;

  /// هل تظهر في وضع مضغوط
  final bool isCompact;

  /// لون مخصص للإحصائيات
  final Color? customColor;

  const ExpertsStatisticsWidget({
    super.key,
    required this.experts,
    this.showAnimations = true,
    this.onStatisticTap,
    this.isCompact = false,
    this.customColor,
  });

  @override
  State<ExpertsStatisticsWidget> createState() =>
      _ExpertsStatisticsWidgetState();
}

class _ExpertsStatisticsWidgetState extends State<ExpertsStatisticsWidget>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _countController;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<int>> _countAnimations;

  @override
  void initState() {
    super.initState();

    if (widget.showAnimations) {
      _setupAnimations();
      _startAnimations();
    }
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _countController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // إعداد رسوم الانزلاق
    _slideAnimations = List.generate(4, (index) {
      return Tween<Offset>(
        begin: Offset(0, 0.5 + (index * 0.1)),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _slideController,
          curve: Interval(
            index * 0.2,
            0.8 + (index * 0.05),
            curve: Curves.easeOutBack,
          ),
        ),
      );
    });

    // إعداد رسوم العد باستخدام IntTween لتجنب مشاكل lerp
    final stats = _calculateStatistics();
    _countAnimations = [
      IntTween(begin: 0, end: stats.totalExperts).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: stats.availableExperts).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: stats.specialtiesCount).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: stats.averageResponseTime.round()).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
    ];
  }

  /// بدء الرسوم المتحركة
  void _startAnimations() {
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _countController.forward();
    });
  }

  @override
  void dispose() {
    if (widget.showAnimations) {
      _slideController.dispose();
      _countController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStatistics();

    if (widget.isCompact) {
      return _buildCompactView(stats);
    }

    return _buildFullView(stats);
  }

  /// بناء العرض الكامل
  Widget _buildFullView(ExpertsStatistics stats) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildStatisticsGrid(stats),
        ],
      ),
    );
  }

  /// بناء العرض المضغوط
  Widget _buildCompactView(ExpertsStatistics stats) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildCompactStatistic(
            'المجموع',
            stats.totalExperts.toString(),
            Icons.people,
            AppColors.primary,
            0,
          ),
          const SizedBox(width: 12),
          _buildCompactStatistic(
            'متاح',
            stats.availableExperts.toString(),
            Icons.check_circle,
            AppColors.success,
            1,
          ),
          const SizedBox(width: 12),
          _buildCompactStatistic(
            'التخصصات',
            stats.specialtiesCount.toString(),
            Icons.category,
            AppColors.accent,
            2,
          ),
        ],
      ),
    );
  }

  /// بناء رأس الإحصائيات
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.analytics,
          color: widget.customColor ?? AppColors.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'إحصائيات سريعة',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء شبكة الإحصائيات
  Widget _buildStatisticsGrid(ExpertsStatistics stats) {
    final statisticsData = [
      StatisticData(
        title: 'إجمالي الخبراء',
        value: stats.totalExperts.toString(),
        icon: Icons.people,
        color: AppColors.primary,
        type: 'total',
      ),
      StatisticData(
        title: 'الخبراء المتاحين',
        value: stats.availableExperts.toString(),
        icon: Icons.check_circle,
        color: AppColors.success,
        type: 'available',
      ),
      StatisticData(
        title: 'التخصصات المختلفة',
        value: stats.specialtiesCount.toString(),
        icon: Icons.category,
        color: AppColors.accent,
        type: 'specialties',
      ),
      StatisticData(
        title: 'متوسط وقت الاستجابة',
        value: '${stats.averageResponseTime.round()} دقيقة',
        icon: Icons.schedule,
        color: AppColors.warning,
        type: 'response_time',
      ),
    ];

    return SizedBox(
      // height: 240, // ارتفاع أكبر لاستيعاب المحتوى
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.8, // نسبة أطول لاستيعاب المحتوى
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: statisticsData.length,
        itemBuilder: (context, index) {
          return _buildStatisticCard(statisticsData[index], index);
        },
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatisticCard(StatisticData data, int index) {
    Widget card = GestureDetector(
      onTap: () => widget.onStatisticTap?.call(data.type),
      child: Container(
        padding: const EdgeInsets.all(12), // تقليل الـ padding
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // الأيقونة والرقم في صف واحد
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: data.color.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(data.icon, color: data.color, size: 18),
                ),
                const SizedBox(width: 8),
                if (widget.showAnimations)
                  AnimatedBuilder(
                    animation: _countAnimations[index],
                    builder: (context, child) {
                      return Text(
                        data.type == 'response_time'
                            ? '${_countAnimations[index].value} دقيقة'
                            : _countAnimations[index].value.toString(),
                        style: AppTextStyles.headlineSmall.copyWith(
                          color: data.color,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  )
                else
                  Text(
                    data.value,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: data.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Flexible(
              child: Text(
                data.title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );

    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _slideController,
        builder: (context, child) {
          return SlideTransition(
            position: _slideAnimations[index],
            child: card,
          );
        },
      );
    }

    return card;
  }

  /// بناء إحصائية مضغوطة
  Widget _buildCompactStatistic(
    String title,
    String value,
    IconData icon,
    Color color,
    int index,
  ) {
    Widget statistic = Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );

    if (widget.showAnimations && index < _slideAnimations.length) {
      return AnimatedBuilder(
        animation: _slideController,
        builder: (context, child) {
          return SlideTransition(
            position: _slideAnimations[index],
            child: statistic,
          );
        },
      );
    }

    return statistic;
  }

  /// حساب الإحصائيات مع Caching ذكي
  ExpertsStatistics _calculateStatistics() {
    // محاولة الحصول على الإحصائيات من التخزين المؤقت
    final cacheKey = '${CacheKeys.expertsStatistics}_${widget.experts.length}';
    final cachedStats = DataCacheManager.getCachedData<ExpertsStatistics>(
      cacheKey,
    );

    if (cachedStats != null) {
      return cachedStats;
    }

    if (widget.experts.isEmpty) {
      return ExpertsStatistics(
        totalExperts: 0,
        availableExperts: 0,
        specialtiesCount: 0,
        averageResponseTime: 0,
      );
    }

    final totalExperts = widget.experts.length;
    final availableExperts = widget.experts.where((e) => e.isAvailable).length;

    final specialties =
        widget.experts.map((e) => e.specialization).toSet().length;

    final averageResponseTime =
        widget.experts.isNotEmpty
            ? widget.experts
                    .map((e) => e.experienceYears.toDouble())
                    .reduce((a, b) => a + b) /
                widget.experts.length
            : 0.0;

    final statistics = ExpertsStatistics(
      totalExperts: totalExperts,
      availableExperts: availableExperts,
      specialtiesCount: specialties,
      averageResponseTime: averageResponseTime,
    );

    // تخزين الإحصائيات في التخزين المؤقت
    DataCacheManager.cacheData(
      key: cacheKey,
      data: statistics,
      duration: const Duration(minutes: 10), // تخزين لمدة 10 دقائق
    );

    return statistics;
  }
}

/// بيانات الإحصائية
class StatisticData {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final String type;

  const StatisticData({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.type,
  });
}

/// إحصائيات الخبراء
class ExpertsStatistics {
  final int totalExperts;
  final int availableExperts;
  final int specialtiesCount;
  final double averageResponseTime;

  const ExpertsStatistics({
    required this.totalExperts,
    required this.availableExperts,
    required this.specialtiesCount,
    required this.averageResponseTime,
  });
}

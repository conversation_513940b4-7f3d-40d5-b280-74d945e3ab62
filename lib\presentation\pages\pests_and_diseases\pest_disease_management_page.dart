import 'package:flutter/material.dart';
import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';

/// صفحة إدارة الآفات والأمراض الزراعية
///
/// تعرض الواجهات الخمس الرئيسية لإدارة الآفات والأمراض
/// وفقاً للمنهج العلمي المتكامل
class PestDiseaseManagementPage extends StatefulWidget {
  const PestDiseaseManagementPage({super.key});

  @override
  State<PestDiseaseManagementPage> createState() => _PestDiseaseManagementPageState();
}

class _PestDiseaseManagementPageState extends State<PestDiseaseManagementPage>
    with TickerProviderStateMixin {

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));
  }

  void _loadData() async {
    await Future.delayed(const Duration(milliseconds: 500));
    setState(() {
      _isLoading = false;
    });
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'إدارة الآفات والأمراض الزراعية',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(),
                      const SizedBox(height: 24),
                      _buildInterfacesList(),
                      const SizedBox(height: 24),
                      _buildExampleSection(),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  /// بناء الرأس التعريفي
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AssetsColors.primary, AssetsColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AssetsColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.agriculture,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نظام إدارة متكامل',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'خمس واجهات رئيسية للإدارة العلمية',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Text(
            'إدارة الآفات والأمراض الزراعية هي عملية معقدة تتطلب فهماً شاملاً لدورة حياة الآفات والأمراض، والعوامل البيئية التي تؤثر عليها، وخيارات المكافحة المتاحة.',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 13,
              color: Colors.white.withValues(alpha: 0.9),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الواجهات
  Widget _buildInterfacesList() {
    final interfaces = [
      {
        'number': '1',
        'title': 'الوقاية',
        'subtitle': 'منع حدوث الإصابة بالآفات والأمراض من البداية',
        'description': 'اختيار الأصناف المقاومة • إعداد التربة المناسب • الدورة الزراعية • الصرف الصحي الجيد • الري المناسب • التسميد المتوازن • فحص الشتلات والتقاوى',
        'icon': Icons.shield_outlined,
        'color': Colors.green,
        'route': 'prevention',
      },
      {
        'number': '2',
        'title': 'الكشف والرصد',
        'subtitle': 'الكشف المبكر عن وجود الآفات والأمراض في الحقل',
        'description': 'المسح الدوري • استخدام المصائد • تسجيل الملاحظات • الاستعانة بالخبراء • استخدام التقنيات الحديثة',
        'icon': Icons.search_outlined,
        'color': Colors.blue,
        'route': 'detection',
      },
      {
        'number': '3',
        'title': 'تحديد مستوى الضرر الاقتصادي',
        'subtitle': 'تحديد ما إذا كانت الإصابة تستدعي التدخل أم لا',
        'description': 'تحديد العتبة الاقتصادية • تقييم الخسائر المحتملة • مقارنة تكلفة المكافحة بالخسائر المحتملة',
        'icon': Icons.assessment_outlined,
        'color': Colors.orange,
        'route': 'economic',
      },
      {
        'number': '4',
        'title': 'المكافحة',
        'subtitle': 'تقليل أعداد الآفات والأمراض إلى مستويات غير ضارة',
        'description': 'اختيار الطريقة المناسبة • المكافحة المتكاملة • المكافحة البيولوجية • المكافحة الزراعية • المكافحة الفيزيائية • المكافحة الكيميائية • الالتزام بالإرشادات',
        'icon': Icons.bug_report_outlined,
        'color': Colors.red,
        'route': 'control',
      },
      {
        'number': '5',
        'title': 'التقييم والمتابعة',
        'subtitle': 'تقييم فعالية طرق المكافحة وتحديد الإجراءات الإضافية',
        'description': 'مراقبة أعداد الآفات والأمراض • تسجيل النتائج • تحليل البيانات لتحديد الاتجاهات وتطوير استراتيجيات مكافحة أفضل في المستقبل',
        'icon': Icons.analytics_outlined,
        'color': Colors.purple,
        'route': 'evaluation',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'واجهات إدارة الآفات والأمراض',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kGrey100,
          ),
        ),

        const SizedBox(height: 16),

        ...interfaces.map((interface) => _buildInterfaceCard(interface)),
      ],
    );
  }

  /// بناء بطاقة واجهة
  Widget _buildInterfaceCard(Map<String, dynamic> interface) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToInterface(interface['route']),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: (interface['color'] as Color).withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 10,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // رقم الواجهة
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: interface['color'],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          interface['number'],
                          style: TextStyle(
                            fontFamily: AssetsFonts.cairo,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // أيقونة الواجهة
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: (interface['color'] as Color).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        interface['icon'],
                        color: interface['color'],
                        size: 28,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // عنوان الواجهة
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            interface['title'],
                            style: TextStyle(
                              fontFamily: AssetsFonts.cairo,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AssetsColors.kGrey100,
                            ),
                          ),
                          Text(
                            interface['subtitle'],
                            style: TextStyle(
                              fontFamily: AssetsFonts.cairo,
                              fontSize: 12,
                              color: AssetsColors.kGrey70,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // سهم الانتقال
                    Icon(
                      Icons.arrow_forward_ios,
                      color: interface['color'],
                      size: 16,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // وصف الواجهة
                Text(
                  interface['description'],
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 13,
                    color: AssetsColors.kGrey70,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم المثال التطبيقي
  Widget _buildExampleSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Colors.amber,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'مثال تطبيقي',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kGrey100,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Text(
            'اللفحة المتأخرة في الطماطم',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'مرض فطري خطير يصيب الطماطم ويسبب خسائر كبيرة في المحصول، خاصة في الظروف الرطبة والباردة. يتطلب تطبيق الواجهات الخمس للإدارة المتكاملة.',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 13,
              color: AssetsColors.kGrey70,
              height: 1.4,
            ),
          ),

          const SizedBox(height: 16),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showExampleDetails(),
              icon: Icon(Icons.visibility),
              label: Text(
                'عرض المثال التفصيلي',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الانتقال إلى واجهة معينة
  void _navigateToInterface(String route) {
    // هنا يمكن إضافة التنقل إلى الصفحات المختلفة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم تطوير هذه الواجهة قريباً',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض تفاصيل المثال
  void _showExampleDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'مثال تطبيقي: اللفحة المتأخرة في الطماطم',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'هذا مثال شامل لكيفية تطبيق الواجهات الخمس لإدارة اللفحة المتأخرة في الطماطم:',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  height: 1.5,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              _buildExampleStep('1. الوقاية', 'زراعة أصناف مقاومة، تحسين التصريف، الدورة الزراعية'),
              _buildExampleStep('2. الكشف والرصد', 'فحص دوري للأوراق، مراقبة الرطوبة، استخدام المصائد'),
              _buildExampleStep('3. العتبة الاقتصادية', 'التدخل عند إصابة 5% من الأوراق'),
              _buildExampleStep('4. المكافحة', 'مكافحة متكاملة: بيولوجية + زراعية + كيميائية محدودة'),
              _buildExampleStep('5. التقييم والمتابعة', 'مراقبة فعالية المكافحة وتسجيل النتائج'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء خطوة في المثال
  Widget _buildExampleStep(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.primary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:io';

import 'package:agriculture/core/constants/app_constants.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/constants_url.dart';
import 'package:agriculture/core/utils/media/image_compression_service.dart';
import 'package:agriculture/core/utils/media/video_compression_service.dart';
import 'package:agriculture/core/utils/media/video_quality.dart';
import 'package:agriculture/presentation/bloc/community_forum/posts_cubit.dart';
import 'package:agriculture/presentation/bloc/community_forum/posts_state.dart';
import 'package:agriculture/presentation/widgets/shared/circular_progress.dart';
import 'package:agriculture/presentation/widgets/shared/user_profile_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

import '../../bloc/auth/core/auth_cubit.dart';

/// صفحة إنشاء منشور جديد
class NewPostScreen extends StatefulWidget {
  const NewPostScreen({super.key});

  @override
  State<NewPostScreen> createState() => _NewPostScreenState();
}

class _NewPostScreenState extends State<NewPostScreen> {
  /// متحكم نص المنشور
  final TextEditingController _textController = TextEditingController();

  /// متحكم نص الوسوم (هاشتاج)
  final TextEditingController _hashtagController = TextEditingController();

  /// قائمة ملفات الصور المختارة
  final List<File> _selectedImages = [];

  /// ملف الفيديو المختار
  File? _selectedVideo;

  /// قائمة الوسوم (هاشتاج)
  final List<String> _hashtags = [];

  /// ما إذا كان في وضع المعاينة
  bool _isPreviewMode = false;

  /// ملتقط الصور
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void dispose() {
    _textController.dispose();
    _hashtagController.dispose();
    super.dispose();
  }

  /// اختيار صورة من معرض الصور
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );
      if (image != null) {
        // عرض مؤشر التحميل
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('جاري معالجة الصورة...')));
        }

        // ضغط الصورة قبل إضافتها
        final File originalFile = File(image.path);
        final File? compressedFile =
            await ImageCompressionService.compressImage(
              file: originalFile,
              quality: AppConstants.defaultImageQuality,
              maxWidth: AppConstants.defaultImageMaxWidth,
              maxHeight: AppConstants.defaultImageMaxHeight,
            );

        if (mounted) {
          setState(() {
            _selectedImages.add(compressedFile ?? originalFile);
            // إذا تم اختيار صورة، نلغي اختيار الفيديو
            _selectedVideo = null;
          });

          // إشعار بنجاح العملية
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('تمت إضافة الصورة بنجاح')));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في اختيار الصورة: $e')));
      }
    }
  }

  /// اختيار فيديو من معرض الصور
  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
      );
      if (video != null) {
        // عرض مؤشر التحميل
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('جاري معالجة الفيديو... قد يستغرق ذلك بضع ثوان'),
            ),
          );
        }

        // تم تعطيل ضغط الفيديو مؤقتًا بسبب مشاكل التوافق
        final File originalFile = File(video.path);
        final File? compressedFile =
            await VideoCompressionService.compressVideo(
              file: originalFile,
              quality: VideoQuality.mediumQuality,
            );

        if (mounted) {
          setState(() {
            _selectedVideo = compressedFile ?? originalFile;
            // إذا تم اختيار فيديو، نلغي اختيار الصور
            _selectedImages.clear();
          });

          // إشعار بنجاح العملية
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تمت إضافة الفيديو بنجاح')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في اختيار الفيديو: $e')));
      }
    }
  }

  /// إزالة صورة من قائمة الصور المختارة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// إزالة الفيديو المختار
  void _removeVideo() {
    setState(() {
      _selectedVideo = null;
    });
  }

  /// إضافة وسم (هاشتاج)
  void _addHashtag() {
    final hashtag = _hashtagController.text.trim();
    if (hashtag.isNotEmpty) {
      // إزالة العلامة # إذا كانت موجودة
      final cleanHashtag =
          hashtag.startsWith('#') ? hashtag.substring(1) : hashtag;

      if (cleanHashtag.isNotEmpty && !_hashtags.contains(cleanHashtag)) {
        setState(() {
          _hashtags.add(cleanHashtag);
          _hashtagController.clear();
        });
      }
    }
  }

  /// إزالة وسم (هاشتاج)
  void _removeHashtag(String hashtag) {
    setState(() {
      _hashtags.remove(hashtag);
    });
  }

  /// تبديل وضع المعاينة
  void _togglePreviewMode() {
    setState(() {
      _isPreviewMode = !_isPreviewMode;
    });
  }

  /// نشر المنشور
  void _publishPost() {
    // التحقق من وجود محتوى للنشر
    if (_textController.text.isEmpty &&
        _selectedImages.isEmpty &&
        _selectedVideo == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة نص أو صورة أو فيديو للمنشور')),
      );
      return;
    }

    // الحصول على بيانات المستخدم الحالي
    final userAccount = context.read<AuthCubit>().currentUser;
    final userName = userAccount?.name ?? 'مستخدم غير معروف';
    final userImage = userAccount?.image ?? '';

    // إنشاء المنشور
    final postsCubit = context.read<PostsCubit>();
    postsCubit.createPost(
      userId: uid,
      userName: userName,
      userImage: userImage,
      text: _textController.text.isNotEmpty ? _textController.text : null,
      imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
      videoFile: _selectedVideo,
      hashtags: _hashtags.isNotEmpty ? _hashtags : null,
    );
  }

  /// بناء وضع التحرير
  Widget _buildEditMode() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المستخدم
          BlocBuilder<AuthCubit, AuthState>(
            builder: (context, state) {
              final userAccount = context.read<AuthCubit>().currentUser;
              final userName = userAccount?.name ?? 'مستخدم غير معروف';

              return Row(
                children: [
                  // صورة المستخدم
                  UserProfileAvatar(
                    radius: 25.0,
                    backgroundColor: AssetsColors.primary.withValues(alpha: 0.1),
                    iconColor: AssetsColors.primary,
                    showBorder: true,
                    borderColor: AssetsColors.primary,
                  ),
                  const SizedBox(width: 15.0),
                  // اسم المستخدم
                  Expanded(
                    child: Text(
                      userName,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 16.0),

          // حقل إدخال النص
          Container(
            height: 150,
            margin: const EdgeInsets.only(bottom: 16.0),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: TextFormField(
              controller: _textController,
              maxLines: null,
              decoration: InputDecoration(
                hintText: 'ماذا يدور في ذهنك؟',
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(12.0),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),

          // إضافة وسوم (هاشتاج)
          Container(
            margin: const EdgeInsets.only(bottom: 16.0),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _hashtagController,
                    textInputAction: TextInputAction.done,
                    decoration: InputDecoration(
                      hintText: 'أضف وسماً (مثل: زراعة)',
                      prefixIcon: Icon(Icons.tag, color: AssetsColors.primary),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12.0,
                        vertical: 14.0,
                      ),
                    ),
                    onSubmitted: (_) => _addHashtag(),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.add_circle, color: AssetsColors.primary),
                  onPressed: _addHashtag,
                ),
              ],
            ),
          ),

          // عرض الوسوم المضافة
          if (_hashtags.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(bottom: 16.0),
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.1)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الوسوم',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AssetsColors.primary,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _hashtags
                            .map(
                              (tag) => Chip(
                                label: Text('#$tag'),
                                deleteIcon: const Icon(Icons.close, size: 16),
                                onDeleted: () => _removeHashtag(tag),
                                backgroundColor: AssetsColors.primary
                                    .withValues(alpha: 0.1),
                                labelStyle: TextStyle(
                                  color: AssetsColors.primary,
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 0,
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ],
              ),
            ),

          // عرض الصور المختارة
          if (_selectedImages.isNotEmpty)
            Container(
              height: 120.0,
              margin: const EdgeInsets.only(bottom: 16.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.0),
                color: Colors.grey.withValues(alpha: 0.05),
              ),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Stack(
                      children: [
                        Container(
                          width: 100.0,
                          height: 100.0,
                          margin: const EdgeInsets.only(right: 8.0),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.0),
                            image: DecorationImage(
                              image: FileImage(_selectedImages[index]),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => _removeImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(4.0),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16.0,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

          // عرض الفيديو المختار
          if (_selectedVideo != null)
            Container(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Stack(
                children: [
                  Container(
                    height: 180.0,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      color: Colors.black.withValues(alpha: 0.8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.play_circle_fill,
                        color: Colors.white,
                        size: 48.0,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: _removeVideo,
                      child: Container(
                        padding: const EdgeInsets.all(4.0),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 16.0,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // أزرار إضافة الوسائط
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16.0),
            padding: const EdgeInsets.symmetric(
              vertical: 8.0,
              horizontal: 12.0,
            ),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.0),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: _pickImage,
                    icon: Icon(Icons.image, color: AssetsColors.primary),
                    label: Text(
                      'إضافة صورة',
                      style: TextStyle(
                        color: AssetsColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: TextButton.icon(
                    onPressed: _pickVideo,
                    icon: Icon(Icons.videocam, color: AssetsColors.primary),
                    label: Text(
                      'إضافة فيديو',
                      style: TextStyle(
                        color: AssetsColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // مسافة إضافية في الأسفل لتجنب تداخل الكيبورد
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  /// بناء وضع المعاينة
  Widget _buildPreviewMode() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.all(16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستخدم
            BlocBuilder<AuthCubit, AuthState>(
              builder: (context, state) {
                final userAccount = context.read<AuthCubit>().currentUser;
                final userName = userAccount?.name ?? 'مستخدم غير معروف';

                return Row(
                  children: [
                    // صورة المستخدم
                    UserProfileAvatar(
                      radius: 25.0,
                      backgroundColor: AssetsColors.primary.withValues(alpha: 0.1),
                      iconColor: AssetsColors.primary,
                      showBorder: true,
                      borderColor: AssetsColors.primary,
                    ),
                    const SizedBox(width: 15.0),
                    // اسم المستخدم
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            userName,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyLarge?.copyWith(
                              fontSize: 16.0,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'الآن',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 16.0),

            // نص المنشور
            if (_textController.text.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text(
                  _textController.text,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(fontSize: 16.0),
                ),
              ),

            // عرض الوسوم
            if (_hashtags.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      _hashtags
                          .map(
                            (tag) => Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AssetsColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '#$tag',
                                style: TextStyle(
                                  color: AssetsColors.primary,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ),

            // عرض الصور المختارة
            if (_selectedImages.isNotEmpty)
              Container(
                height: 250.0,
                margin: const EdgeInsets.only(bottom: 16.0),
                child: PageView.builder(
                  itemCount: _selectedImages.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.0),
                        image: DecorationImage(
                          image: FileImage(_selectedImages[index]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
              ),

            // عرض الفيديو المختار
            if (_selectedVideo != null)
              Container(
                height: 250.0,
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: 16.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  color: Colors.black,
                ),
                child: const Center(
                  child: Icon(
                    Icons.play_circle_fill,
                    color: Colors.white,
                    size: 64.0,
                  ),
                ),
              ),

            // معلومات التفاعل
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text('0', style: TextStyle(color: Colors.grey[600])),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.comment_outlined,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text('0', style: TextStyle(color: Colors.grey[600])),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.visibility_outlined,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text('0', style: TextStyle(color: Colors.grey[600])),
                ],
              ),
            ),

            // خط فاصل
            Divider(color: Colors.grey[300]),

            // أزرار التفاعل
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton.icon(
                  onPressed: () {},
                  icon: Icon(Icons.favorite_border, color: Colors.grey[600]),
                  label: Text(
                    'إعجاب',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                TextButton.icon(
                  onPressed: () {},
                  icon: Icon(Icons.comment_outlined, color: Colors.grey[600]),
                  label: Text(
                    'تعليق',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                TextButton.icon(
                  onPressed: () {},
                  icon: Icon(Icons.share_outlined, color: Colors.grey[600]),
                  label: Text(
                    'مشاركة',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
              ],
            ),

            // مسافة إضافية في الأسفل لتجنب تداخل الكيبورد
            const SizedBox(height: 50),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PostsCubit, PostsState>(
      listenWhen:
          (previous, current) =>
              current is PostCreated || current is PostCreationError,
      listener: (context, state) {
        if (state is PostCreated) {
          // إعادة تحميل المنشورات والعودة إلى الصفحة السابقة
          context.read<PostsCubit>().resetState();
          Navigator.pop(context);
        } else if (state is PostCreationError) {
          // عرض رسالة الخطأ
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AssetsColors.dufaultGreencolor,
          title: Text(_isPreviewMode ? 'معاينة المنشور' : 'إنشاء منشور جديد'),
          actions: [
            // زر المعاينة
            IconButton(
              icon: Icon(_isPreviewMode ? Icons.edit : Icons.preview),
              tooltip: _isPreviewMode ? 'تحرير' : 'معاينة',
              onPressed: _togglePreviewMode,
            ),

            // زر النشر
            BlocBuilder<PostsCubit, PostsState>(
              builder: (context, state) {
                if (state is PostCreating) {
                  return const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: CircularProgress(),
                  );
                }
                return Container(
                  margin: const EdgeInsets.only(left: 8.0, right: 8.0),
                  height: 40,
                  width: 80,
                  child: ElevatedButton(
                    onPressed: _publishPost,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AssetsColors.primary,
                      foregroundColor: Colors.white,
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                    ),
                    child: const Text(
                      'نشر',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        body: BlocListener<PostsCubit, PostsState>(
          listener: (context, state) {
            if (state is PostCreationError) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(state.message)));
            }
          },
          child: _isPreviewMode ? _buildPreviewMode() : _buildEditMode(),
        ),
      ),
    );
  }
}

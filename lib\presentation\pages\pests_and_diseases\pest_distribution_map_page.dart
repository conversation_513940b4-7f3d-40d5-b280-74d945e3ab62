
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

import '../../../data/repositories/real_pest_distribution_data.dart';
import '../../../imports.dart';

/// صفحة خريطة انتشار الآفات والأمراض
///
/// تعرض خريطة تفاعلية لانتشار الآفات والأمراض في اليمن
/// مع إمكانية الإبلاغ عن حالات جديدة
class PestDistributionMapPage extends StatefulWidget {
  const PestDistributionMapPage({super.key});

  @override
  State<PestDistributionMapPage> createState() => _PestDistributionMapPageState();
}

class _PestDistributionMapPageState extends State<PestDistributionMapPage>
    with TickerProviderStateMixin {
  
  late AnimationController _animationController;
  
  final Set<Marker> _markers = {};
  final Set<Circle> _circles = {};
  
  List<PestReport> _pestReports = [];
  PestReport? _selectedReport;
  String _selectedPestType = 'all';
  bool _showHeatmap = false;
  bool _isLoading = true;

  // إحداثيات اليمن
  static const LatLng _yemenCenter = LatLng(15.3694, 44.1910);

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadPestReports();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      setState(() {
      });
    } catch (e) {
      LoggerService.debug('خطأ في الحصول على الموقع: $e');
    }
  }

  Future<void> _loadPestReports() async {
    // تحميل البيانات الحقيقية
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _pestReports = _loadRealPestReports();
      _updateMapMarkers();
      _isLoading = false;
    });

    _animationController.forward();
  }

  /// تحميل البيانات الحقيقية للآفات
  List<PestReport> _loadRealPestReports() {
    final realData = RealPestDistributionRepository.getPestDistributionData();
    final reports = <PestReport>[];

    for (final data in realData) {
      reports.add(PestReport(
        id: data['id'] as String,
        pestName: data['pestName'] as String,
        location: LatLng(
          data['latitude'] as double,
          data['longitude'] as double,
        ),
        severity: _mapSeverityFromString(data['severity'] as String),
        reportDate: DateTime.parse(data['reportDate'] as String),
        reporterName: data['reportedBy'] as String,
        description: data['notes'] as String,
        affectedArea: (data['affectedArea'] as int).toDouble(),
        cropType: data['crop'] as String,
        affectedCrop: data['crop'] as String,
        treatmentStatus: data['status'] as String,
      ));
    }

    return reports;
  }

  /// تحويل مستوى الخطورة من نص إلى enum
  PestSeverity _mapSeverityFromString(String severity) {
    switch (severity) {
      case 'منخفض':
        return PestSeverity.low;
      case 'متوسط':
        return PestSeverity.medium;
      case 'عالي':
        return PestSeverity.high;
      case 'عالي جداً':
        return PestSeverity.critical;
      default:
        return PestSeverity.medium;
    }
  }


  void _updateMapMarkers() {
    _markers.clear();
    _circles.clear();
    
    for (final report in _pestReports) {
      if (_selectedPestType != 'all' && report.cropType != _selectedPestType) {
        continue;
      }
      
      // إضافة العلامة
      _markers.add(
        Marker(
          markerId: MarkerId(report.id),
          position: report.location,
          icon: _getPestIcon(report.severity),
          infoWindow: InfoWindow(
            title: report.pestName,
            snippet: '${report.cropType} - ${_getSeverityText(report.severity)}',
            onTap: () => _showReportDetails(report),
          ),
          onTap: () => _selectReport(report),
        ),
      );
      
      // إضافة دائرة التأثير
      _circles.add(
        Circle(
          circleId: CircleId('circle_${report.id}'),
          center: report.location,
          radius: report.affectedArea * 1000, // تحويل إلى متر
          fillColor: _getSeverityColor(report.severity).withOpacity(0.2),
          strokeColor: _getSeverityColor(report.severity),
          strokeWidth: 2,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'خريطة انتشار الآفات',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showHeatmap ? Icons.layers_clear : Icons.layers),
            onPressed: _toggleHeatmap,
            tooltip: _showHeatmap ? 'إخفاء الخريطة الحرارية' : 'عرض الخريطة الحرارية',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.add_location, color: AssetsColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'إبلاغ عن آفة',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'statistics',
                child: Row(
                  children: [
                    Icon(Icons.analytics, color: AssetsColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'الإحصائيات',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'alerts',
                child: Row(
                  children: [
                    Icon(Icons.notifications, color: AssetsColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'التنبيهات',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingScreen()
          : Stack(
              children: [
                // الخريطة
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _yemenCenter,
                    zoom: 6.5,
                  ),
                  markers: _markers,
                  circles: _showHeatmap ? _circles : {},
                  onMapCreated: (GoogleMapController controller) {
                  },
                  onTap: (LatLng position) {
                    setState(() {
                      _selectedReport = null;
                    });
                  },
                  mapType: MapType.hybrid,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: true,
                ),
                
                // شريط الفلاتر العلوي
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: _buildFilterBar(),
                ),
                
                // لوحة التفاصيل السفلية
                if (_selectedReport != null)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: _buildReportDetailsPanel(),
                  ),
                
                // إحصائيات سريعة
                Positioned(
                  top: 80,
                  right: 16,
                  child: _buildQuickStats(),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showReportDialog,
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        icon: Icon(Icons.add_location),
        label: Text(
          'إبلاغ عن آفة',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// بناء شاشة التحميل
  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AssetsColors.primary),
          ),
          const SizedBox(height: 20),
          Text(
            'جاري تحميل بيانات الآفات...',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الفلاتر
  Widget _buildFilterBar() {
    return SizedBox(
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', 'all'),
          _buildFilterChip('طماطم', 'طماطم'),
          _buildFilterChip('خيار', 'خيار'),
          _buildFilterChip('بطاطس', 'بطاطس'),
          _buildFilterChip('قطن', 'قطن'),
          _buildFilterChip('مانجو', 'مانجو'),
        ],
      ),
    );
  }

  /// بناء رقاقة فلتر
  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedPestType == value;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedPestType = value;
            _updateMapMarkers();
          });
        },
        backgroundColor: Colors.white,
        selectedColor: AssetsColors.primary,
        checkmarkColor: Colors.white,
        elevation: 2,
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    final totalReports = _pestReports.length;
    final criticalReports = _pestReports.where((r) => r.severity == PestSeverity.critical).length;
    final todayReports = _pestReports.where((r) => 
        DateTime.now().difference(r.reportDate).inDays == 0).length;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildStatItem('إجمالي', totalReports.toString(), Colors.blue),
          const SizedBox(height: 8),
          _buildStatItem('حرجة', criticalReports.toString(), Colors.red),
          const SizedBox(height: 8),
          _buildStatItem('اليوم', todayReports.toString(), Colors.green),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String label, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          '$label: $value',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء لوحة تفاصيل التقرير
  Widget _buildReportDetailsPanel() {
    final report = _selectedReport!;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس اللوحة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getSeverityColor(report.severity).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.bug_report,
                    color: _getSeverityColor(report.severity),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        report.pestName,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${report.cropType} - ${_getSeverityText(report.severity)}',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _selectedReport = null;
                    });
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // تفاصيل التقرير
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow('الوصف', report.description),
                    _buildDetailRow('المساحة المتأثرة', '${report.affectedArea} هكتار'),
                    _buildDetailRow('المبلغ', report.reporterName),
                    _buildDetailRow('تاريخ الإبلاغ', _formatDate(report.reportDate)),
                  ],
                ),
              ),
            ),
            
            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showTreatmentSuggestions(report),
                    icon: Icon(Icons.healing),
                    label: Text(
                      'العلاج المقترح',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AssetsColors.primary,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _shareReport(report),
                    icon: Icon(Icons.share),
                    label: Text(
                      'مشاركة',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف تفصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار تقرير
  void _selectReport(PestReport report) {
    setState(() {
      _selectedReport = report;
    });
  }

  /// عرض تفاصيل التقرير
  void _showReportDetails(PestReport report) {
    _selectReport(report);
  }

  /// تبديل الخريطة الحرارية
  void _toggleHeatmap() {
    setState(() {
      _showHeatmap = !_showHeatmap;
    });
  }

  /// معالجة اختيار القائمة
  void _handleMenuSelection(String value) {
    switch (value) {
      case 'report':
        _showReportDialog();
        break;
      case 'statistics':
        _showStatistics();
        break;
      case 'alerts':
        _showAlerts();
        break;
    }
  }

  /// عرض حوار الإبلاغ
  void _showReportDialog() {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'ميزة الإبلاغ عن الآفات قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض الإحصائيات
  void _showStatistics() {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'صفحة الإحصائيات قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض التنبيهات
  void _showAlerts() {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'نظام التنبيهات قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض اقتراحات العلاج
  void _showTreatmentSuggestions(PestReport report) {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'اقتراحات العلاج لـ ${report.pestName} قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// مشاركة التقرير
  void _shareReport(PestReport report) {
    // سيتم تنفيذها لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم نسخ رابط التقرير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// الحصول على أيقونة الآفة
  BitmapDescriptor _getPestIcon(PestSeverity severity) {
    // في التطبيق الحقيقي، ستكون أيقونات مخصصة
    return BitmapDescriptor.defaultMarkerWithHue(
      _getSeverityHue(severity),
    );
  }

  /// الحصول على لون الخطورة
  Color _getSeverityColor(PestSeverity severity) {
    switch (severity) {
      case PestSeverity.low:
        return Colors.green;
      case PestSeverity.medium:
        return Colors.orange;
      case PestSeverity.high:
        return Colors.red;
      case PestSeverity.critical:
        return Colors.red.shade900;
    }
  }

  /// الحصول على درجة اللون للخريطة
  double _getSeverityHue(PestSeverity severity) {
    switch (severity) {
      case PestSeverity.low:
        return BitmapDescriptor.hueGreen;
      case PestSeverity.medium:
        return BitmapDescriptor.hueOrange;
      case PestSeverity.high:
        return BitmapDescriptor.hueRed;
      case PestSeverity.critical:
        return BitmapDescriptor.hueViolet;
    }
  }

  /// الحصول على نص الخطورة
  String _getSeverityText(PestSeverity severity) {
    switch (severity) {
      case PestSeverity.low:
        return 'منخفض';
      case PestSeverity.medium:
        return 'متوسط';
      case PestSeverity.high:
        return 'عالي';
      case PestSeverity.critical:
        return 'حرج';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// نموذج تقرير الآفة
class PestReport {
  final String id;
  final String pestName;
  final LatLng location;
  final PestSeverity severity;
  final DateTime reportDate;
  final String reporterName;
  final String description;
  final double affectedArea;
  final String cropType;
  final String? affectedCrop;
  final String? treatmentStatus;

  PestReport({
    required this.id,
    required this.pestName,
    required this.location,
    required this.severity,
    required this.reportDate,
    required this.reporterName,
    required this.description,
    required this.affectedArea,
    required this.cropType,
    this.affectedCrop,
    this.treatmentStatus,
  });
}

/// مستويات خطورة الآفات
enum PestSeverity {
  low,      // منخفض
  medium,   // متوسط
  high,     // عالي
  critical, // حرج
}

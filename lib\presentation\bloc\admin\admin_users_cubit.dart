import 'dart:io';

import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/data/models/education/index.dart';
import 'package:agriculture/domain/repositories/education_repository_interface.dart';
import 'package:agriculture/domain/repositories/storage_repository_interface.dart';
import 'package:agriculture/domain/usecases/admin/get_users_list.dart';
import 'package:agriculture/domain/usecases/admin/update_user_role.dart';
import 'package:agriculture/presentation/bloc/admin/admin_users_state.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';

import '../../../data/models/user_account.dart';

/// كيوبت إدارة المستخدمين والدورات والدروس
///
/// يدير هذا الكيوبت عمليات إدارة المستخدمين والدورات والدروس
class AdminUsersCubit extends Cubit<AdminUsersState> {
  /// مرجع Firestore
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// حالة استخدام الحصول على قائمة المستخدمين
  // final GetUsersList? _getUsersList; // سيتم استخدامه لاحقاً

  /// حالة استخدام تحديث دور المستخدم
  final UpdateUserRole? _updateUserRole;

  /// مستودع التعليم (للدورات فقط)
  final EducationRepositoryInterface? _educationRepository;

  /// مستودع التخزين
  final StorageRepositoryInterface _storageRepository;

  /// مولد UUID
  final Uuid _uuid = const Uuid();

  /// إنشاء كيوبت إدارة المستخدمين
  AdminUsersCubit({
    GetUsersList? getUsersList,
    UpdateUserRole? updateUserRole,
    EducationRepositoryInterface? educationRepository,
    required StorageRepositoryInterface storageRepository,
  }) : // _getUsersList = getUsersList, // سيتم استخدامه لاحقاً
       _updateUserRole = updateUserRole,
       _educationRepository = educationRepository,
       _storageRepository = storageRepository,
       super(AdminUsersInitial());

  /// تحميل قائمة المستخدمين
  Future<void> loadUsers() async {
    emit(AdminUsersLoading());

    try {
      final usersSnapshot = await _firestore.collection('users').get();

      final users =
          usersSnapshot.docs.map((doc) {
            final data = doc.data();
            return UserAccount(
              id: doc.id,
              name: data['name'] ?? '',
              email: data['email'] ?? '',
              image: data['image'] ?? '',
              phone: data['phone'] ?? '',
              isVerified: data['isVerified'] ?? false,
              address: data['address'] ?? '',
              bio: data['bio'] ?? '',
              createdAt:
                  data['createdAt'] != null
                      ? data['createdAt'].toString()
                      : DateTime.now().toString(),
              lastUpdated:
                  data['lastUpdated'] != null
                      ? data['lastUpdated'].toString()
                      : DateTime.now().toString(),
              theme: data['theme'] ?? 'system',
              language: data['language'] ?? 'ar',
              specialty: data['specialty'],
              role: data['role'] ?? 'farmer',
            );
          }).toList();

      // ترتيب المستخدمين حسب تاريخ الإنشاء (الأحدث أولاً)
      users.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      emit(AdminUsersLoaded(users));

      LoggerService.info(
        'تم تحميل ${users.length} مستخدم',
        tag: 'AdminUsersCubit',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تحميل المستخدمين',
        error: e,
        tag: 'AdminUsersCubit',
      );

      emit(AdminUsersError('حدث خطأ أثناء تحميل المستخدمين: ${e.toString()}'));
    }
  }

  /// تحديث دور المستخدم
  ///
  /// المعلمات:
  /// - [userId]: معرف المستخدم
  /// - [newRole]: الدور الجديد
  Future<void> updateUserRole(String userId, String newRole) async {
    emit(AdminUsersUpdating());

    try {
      if (_updateUserRole != null) {
        // استخدام حالة استخدام تحديث دور المستخدم
        await _updateUserRole(userId, newRole);

        emit(AdminUsersUpdated(userId, newRole));

        LoggerService.info(
          'تم تحديث دور المستخدم $userId إلى $newRole',
          tag: 'AdminUsersCubit',
        );
      } else {
        // استخدام Firestore مباشرة إذا لم تكن حالة الاستخدام متاحة
        await _firestore.collection('users').doc(userId).update({
          'role': newRole,
          'lastUpdated': FieldValue.serverTimestamp(),
        });

        emit(AdminUsersUpdated(userId, newRole));

        LoggerService.info(
          'تم تحديث دور المستخدم $userId إلى $newRole',
          tag: 'AdminUsersCubit',
        );
      }

      // إعادة تحميل قائمة المستخدمين بعد التحديث
      loadUsers();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث دور المستخدم',
        error: e,
        tag: 'AdminUsersCubit',
      );

      emit(
        AdminUsersError('حدث خطأ أثناء تحديث دور المستخدم: ${e.toString()}'),
      );
    }
  }

  /// تحميل قائمة الدورات
  Future<void> loadCourses({String? categoryId}) async {
    emit(AdminCoursesLoading());
    try {
      if (_educationRepository != null) {
        final courses = await _educationRepository.getCourses(
          categoryId: categoryId,
        );
        emit(AdminCoursesLoaded(courses));
        LoggerService.info(
          'تم تحميل ${courses.length} دورة',
          tag: 'AdminUsersCubit',
        );
      } else {
        emit(AdminCoursesError('مستودع التعليم غير متاح'));
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في تحميل الدورات',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminCoursesError('حدث خطأ أثناء تحميل الدورات: ${e.toString()}'));
    }
  }

  /// إضافة دورة جديدة
  Future<void> addCourse({
    required String title,
    required String description,
    required String level,
    required int durationMinutes,
    required String categoryId,
    required String instructorName,
    File? imageFile,
  }) async {
    emit(AdminCourseAdding());
    try {
      if (_educationRepository == null) {
        emit(AdminCoursesError('مستودع التعليم غير متاح'));
        return;
      }
      String imageUrl = '';

      if (imageFile != null) {
        final courseId = _uuid.v4();
        final imagePath = 'courses/$courseId/cover/${_uuid.v4()}.jpg';
        final uploadedUrl = await _storageRepository.uploadFile(
          imageFile,
          imagePath,
        );
        imageUrl = uploadedUrl ?? '';
      }

      final course = CourseModel(
        id: '',
        title: title,
        description: description,
        imageUrl: imageUrl,
        level: level,
        durationMinutes: durationMinutes,
        lessonsCount: 0,
        lessonIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        categoryId: categoryId,
        instructorName: instructorName,
        viewsCount: 0,
      );

      final courseId = await _educationRepository.addCourse(course);
      emit(AdminCourseAdded(courseId));
      LoggerService.info(
        'تم إضافة الدورة بنجاح: $courseId',
        tag: 'AdminUsersCubit',
      );
      loadCourses();
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة الدورة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminCoursesError('حدث خطأ أثناء إضافة الدورة: ${e.toString()}'));
    }
  }

  /// تحديث دورة
  Future<void> updateCourse({
    required String courseId,
    required String title,
    required String description,
    required String level,
    required int durationMinutes,
    required String categoryId,
    required String instructorName,
    File? imageFile,
    String? currentImageUrl,
  }) async {
    emit(AdminCourseUpdating());
    try {
      if (_educationRepository == null) {
        emit(AdminCoursesError('مستودع التعليم غير متاح'));
        return;
      }
      String imageUrl = currentImageUrl ?? '';

      if (imageFile != null) {
        final imagePath = 'courses/$courseId/cover/${_uuid.v4()}.jpg';
        final uploadedUrl = await _storageRepository.uploadFile(
          imageFile,
          imagePath,
        );
        imageUrl = uploadedUrl ?? currentImageUrl ?? '';
      }

      final course = CourseModel(
        id: courseId,
        title: title,
        description: description,
        imageUrl: imageUrl,
        level: level,
        durationMinutes: durationMinutes,
        lessonsCount: 0,
        lessonIds: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        categoryId: categoryId,
        instructorName: instructorName,
        viewsCount: 0,
      );

      await _educationRepository.updateCourse(courseId, course);
      emit(AdminCourseUpdated());
      LoggerService.info(
        'تم تحديث الدورة بنجاح: $courseId',
        tag: 'AdminUsersCubit',
      );
      loadCourses();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث الدورة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminCoursesError('حدث خطأ أثناء تحديث الدورة: ${e.toString()}'));
    }
  }

  /// حذف دورة
  Future<void> deleteCourse(String courseId) async {
    emit(AdminCourseDeleting());
    try {
      await _educationRepository?.deleteCourse(courseId);
      emit(AdminCourseDeleted());
      LoggerService.info(
        'تم حذف الدورة بنجاح: $courseId',
        tag: 'AdminUsersCubit',
      );
      loadCourses();
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف الدورة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminCoursesError('حدث خطأ أثناء حذف الدورة: ${e.toString()}'));
    }
  }

  /// تحميل دروس دورة
  Future<void> loadCourseLessons(String courseId) async {
    emit(AdminLessonsLoading());
    try {
      final lessons = await _educationRepository!.getCourseLessons(courseId);
      emit(AdminLessonsLoaded(lessons));
      LoggerService.info(
        'تم تحميل ${lessons.length} درس للدورة $courseId',
        tag: 'AdminUsersCubit',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تحميل الدروس',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminLessonsError('حدث خطأ أثناء تحميل الدروس: ${e.toString()}'));
    }
  }

  /// إضافة درس جديد
  Future<void> addLesson({
    required String courseId,
    required String title,
    required String description,
    required int duration,
    required int order,
    String? additionalContent,
    File? videoFile,
  }) async {
    emit(AdminLessonAdding());
    try {
      // طباعة معلومات مفصلة للتصحيح
      LoggerService.info('🔥 بدء إضافة درس جديد:', tag: 'AdminUsersCubit');
      LoggerService.info('📚 معرف الدورة: "$courseId"', tag: 'AdminUsersCubit');
      LoggerService.info('📝 عنوان الدرس: "$title"', tag: 'AdminUsersCubit');
      LoggerService.info(
        '📄 وصف الدرس: "$description"',
        tag: 'AdminUsersCubit',
      );
      LoggerService.info(
        '⏱️ مدة الدرس: $duration دقيقة',
        tag: 'AdminUsersCubit',
      );
      LoggerService.info('🔢 ترتيب الدرس: $order', tag: 'AdminUsersCubit');

      // التحقق من أن courseId ليس فارغ
      if (courseId.isEmpty) {
        LoggerService.error(
          '❌ خطأ حرج: معرف الدورة فارغ!',
          tag: 'AdminUsersCubit',
        );
        throw Exception('معرف الدورة مطلوب ولا يمكن أن يكون فارغ');
      }

      // إنشاء معرف فريد للدرس
      final lessonId = _uuid.v4();
      LoggerService.info(
        '🆔 معرف الدرس المُنشأ: "$lessonId"',
        tag: 'AdminUsersCubit',
      );

      String videoUrl = '';

      // رفع الفيديو إذا تم اختياره
      if (videoFile != null) {
        final videoPath =
            'courses/$courseId/lessons/$lessonId/video/${_uuid.v4()}.mp4';
        final uploadedUrl = await _storageRepository.uploadFile(
          videoFile,
          videoPath,
        );
        videoUrl = uploadedUrl ?? '';
        LoggerService.info(
          'تم رفع فيديو الدرس: $videoUrl',
          tag: 'AdminUsersCubit',
        );
      }

      // إنشاء نموذج الدرس مع المعرف الصحيح
      final lesson = LessonModel(
        id: lessonId, // استخدام المعرف المُنشأ
        courseId: courseId, // ربط الدرس بالدورة
        title: title,
        description: description,
        videoUrl: videoUrl,
        duration: duration,
        order: order,
        additionalContent: additionalContent,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // التحقق من صحة البيانات قبل الحفظ
      LoggerService.info(
        '✅ تم إنشاء نموذج الدرس بنجاح:',
        tag: 'AdminUsersCubit',
      );
      LoggerService.info(
        '🆔 معرف الدرس: "${lesson.id}"',
        tag: 'AdminUsersCubit',
      );
      LoggerService.info(
        '📚 معرف الدورة في النموذج: "${lesson.courseId}"',
        tag: 'AdminUsersCubit',
      );
      LoggerService.info(
        '📝 عنوان الدرس في النموذج: "${lesson.title}"',
        tag: 'AdminUsersCubit',
      );

      // التحقق من أن courseId في النموذج ليس فارغ
      if (lesson.courseId.isEmpty) {
        LoggerService.error(
          '❌ خطأ حرج: معرف الدورة في النموذج فارغ!',
          tag: 'AdminUsersCubit',
        );
        throw Exception(
          'فشل في ربط الدرس بالدورة - معرف الدورة فارغ في النموذج',
        );
      }

      // حفظ الدرس في Firebase مع المعرف المحدد مسبق<|im_start|>
      await _educationRepository!.addLessonWithId(lessonId, lesson);

      LoggerService.info(
        'تم إضافة الدرس بنجاح: $lessonId',
        tag: 'AdminUsersCubit',
      );

      // التحقق من أن الدرس تم ربطه بالدورة بشكل صحيح
      await _verifyLessonCourseBinding(lessonId, courseId);

      // تحديث عدد الدروس في الدورة
      await _updateCourseStats(courseId);

      emit(AdminLessonAdded(lessonId));

      // إعادة تحميل دروس الدورة لعرض التحديث
      loadCourseLessons(courseId);
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة الدرس',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminLessonsError('حدث خطأ أثناء إضافة الدرس: ${e.toString()}'));
    }
  }

  /// تحديث درس
  Future<void> updateLesson({
    required String lessonId,
    required String courseId,
    required String title,
    required String description,
    required int duration,
    required int order,
    String? additionalContent,
    File? videoFile,
    String? currentVideoUrl,
  }) async {
    emit(AdminLessonUpdating());
    try {
      String videoUrl = currentVideoUrl ?? '';

      if (videoFile != null) {
        final videoPath =
            'courses/$courseId/lessons/$lessonId/video/${_uuid.v4()}.mp4';
        final uploadedUrl = await _storageRepository.uploadFile(
          videoFile,
          videoPath,
        );
        videoUrl = uploadedUrl ?? currentVideoUrl ?? '';
      }

      final lesson = LessonModel(
        id: lessonId,
        courseId: courseId,
        title: title,
        description: description,
        videoUrl: videoUrl,
        duration: duration,
        order: order,
        additionalContent: additionalContent,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _educationRepository!.updateLesson(lessonId, lesson);
      emit(AdminLessonUpdated());
      LoggerService.info(
        'تم تحديث الدرس بنجاح: $lessonId',
        tag: 'AdminUsersCubit',
      );
      loadCourseLessons(courseId);
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث الدرس',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminLessonsError('حدث خطأ أثناء تحديث الدرس: ${e.toString()}'));
    }
  }

  /// حذف درس
  Future<void> deleteLesson(String lessonId, String courseId) async {
    emit(AdminLessonDeleting());
    try {
      await _educationRepository!.deleteLesson(lessonId);

      // تحديث عدد الدروس في الدورة بعد الحذف
      await _updateCourseStats(courseId);

      emit(AdminLessonDeleted());
      LoggerService.info(
        'تم حذف الدرس بنجاح: $lessonId',
        tag: 'AdminUsersCubit',
      );
      loadCourseLessons(courseId);
    } catch (e) {
      LoggerService.error('خطأ في حذف الدرس', error: e, tag: 'AdminUsersCubit');
      emit(AdminLessonsError('حدث خطأ أثناء حذف الدرس: ${e.toString()}'));
    }
  }

  /// التحقق من ربط الدرس بالدورة بشكل صحيح
  Future<void> _verifyLessonCourseBinding(
    String lessonId,
    String courseId,
  ) async {
    try {
      LoggerService.info(
        'بدء التحقق من ربط الدرس بالدورة - درس: $lessonId - دورة: $courseId',
        tag: 'AdminUsersCubit',
      );

      // جلب الدرس من Firebase للتحقق من ربطه بالدورة
      final savedLesson = await _educationRepository!.getLesson(lessonId);

      if (savedLesson == null) {
        LoggerService.error(
          'خطأ: الدرس غير موجود في Firebase بعد الحفظ - المعرف: $lessonId',
          tag: 'AdminUsersCubit',
        );
        throw Exception('فشل في حفظ الدرس في قاعدة البيانات');
      }

      if (savedLesson.courseId != courseId) {
        LoggerService.error(
          'خطأ: الدرس غير مرتبط بالدورة الصحيحة - درس: $lessonId - دورة متوقعة: $courseId - دورة فعلية: ${savedLesson.courseId}',
          tag: 'AdminUsersCubit',
        );
        throw Exception('فشل في ربط الدرس بالدورة الصحيحة');
      }

      // التحقق من أن الدرس يظهر في قائمة دروس الدورة
      final courseLessons = await _educationRepository.getCourseLessons(
        courseId,
      );
      final lessonExists = courseLessons.any((lesson) => lesson.id == lessonId);

      if (!lessonExists) {
        LoggerService.error(
          'خطأ: الدرس لا يظهر في قائمة دروس الدورة - درس: $lessonId - دورة: $courseId',
          tag: 'AdminUsersCubit',
        );
        throw Exception('فشل في إضافة الدرس إلى قائمة دروس الدورة');
      }

      LoggerService.info(
        'تم التحقق بنجاح من ربط الدرس بالدورة - درس: $lessonId - دورة: $courseId',
        tag: 'AdminUsersCubit',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في التحقق من ربط الدرس بالدورة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      rethrow; // إعادة رمي الخطأ لأن هذا خطأ حرج
    }
  }

  /// تحديث إحصائيات الدورة (عدد الدروس)
  Future<void> _updateCourseStats(String courseId) async {
    try {
      // جلب جميع دروس الدورة
      final lessons = await _educationRepository!.getCourseLessons(courseId);
      final lessonsCount = lessons.length;

      // جلب تفاصيل الدورة الحالية
      final course = await _educationRepository.getCourseDetails(courseId);

      if (course != null) {
        // تحديث عدد الدروس في الدورة
        final updatedCourse = CourseModel(
          id: course.id,
          title: course.title,
          description: course.description,
          imageUrl: course.imageUrl,
          level: course.level,
          durationMinutes: course.durationMinutes,
          lessonsCount: lessonsCount, // تحديث عدد الدروس
          lessonIds: course.lessonIds,
          createdAt: course.createdAt,
          updatedAt: DateTime.now(),
          categoryId: course.categoryId,
          instructorName: course.instructorName,
          viewsCount: course.viewsCount,
        );

        // حفظ التحديث في Firebase
        await _educationRepository.updateCourse(courseId, updatedCourse);

        LoggerService.info(
          'تم تحديث إحصائيات الدورة: $courseId - عدد الدروس: $lessonsCount',
          tag: 'AdminUsersCubit',
        );
      }
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث إحصائيات الدورة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      // لا نرمي الخطأ هنا لأن العملية الأساسية (إضافة/حذف الدرس) نجحت
    }
  }

  // ==================== إدارة المقالات ====================

  /// تحميل قائمة المقالات من Firebase
  Future<void> loadArticles({String? categoryId}) async {
    emit(AdminArticlesLoading());
    try {
      LoggerService.info(
        'بدء تحميل المقالات من Firebase - الفئة: ${categoryId ?? "جميع الفئات"}',
        tag: 'AdminUsersCubit',
      );

      // جلب المقالات من Firebase
      QuerySnapshot articlesSnapshot;

      if (categoryId != null && categoryId.isNotEmpty) {
        // جلب المقالات حسب الفئة
        articlesSnapshot =
            await _firestore
                .collection('articles')
                .where('categoryId', isEqualTo: categoryId)
                .get();
      } else {
        // جلب جميع المقالات مع الترتيب
        articlesSnapshot =
            await _firestore
                .collection('articles')
                .orderBy('createdAt', descending: true)
                .get();
      }

      // تحويل البيانات إلى قائمة نماذج
      var articles =
          articlesSnapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return ArticleModel(
              id: doc.id,
              title: data['title'] ?? '',
              content: data['content'] ?? '',
              imageUrl: data['imageUrl'] ?? '',
              categoryId: data['categoryId'] ?? '',
              tags: List<String>.from(data['tags'] ?? []),
              isPublished: data['isPublished'] ?? false,
              isFeatured: data['isFeatured'] ?? false,
              publishDate: data['publishDate']?.toDate() ?? DateTime.now(),
              updatedAt: data['updatedAt']?.toDate() ?? DateTime.now(),
            );
          }).toList();

      // ترتيب المقالات حسب التاريخ (الأحدث أولاً) إذا كانت مفلترة
      if (categoryId != null && categoryId.isNotEmpty) {
        articles.sort((a, b) => b.publishDate.compareTo(a.publishDate));
      }

      emit(AdminArticlesLoaded(articles));
      LoggerService.info(
        'تم تحميل ${articles.length} مقالة بنجاح',
        tag: 'AdminUsersCubit',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تحميل المقالات',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminArticlesError('حدث خطأ أثناء تحميل المقالات: ${e.toString()}'));
    }
  }

  /// إضافة مقالة جديدة
  Future<void> addArticle({
    required String title,
    required String content,
    required String categoryId,
    required List<String> tags,
    required bool isPublished,
    required bool isFeatured,
    File? imageFile,
  }) async {
    emit(AdminArticleAdding());
    try {
      LoggerService.info(
        'بدء إضافة مقالة جديدة: "$title"',
        tag: 'AdminUsersCubit',
      );

      // التحقق من صحة البيانات
      if (title.trim().isEmpty) {
        throw Exception('عنوان المقالة مطلوب');
      }

      if (content.trim().isEmpty) {
        throw Exception('محتوى المقالة مطلوب');
      }

      if (categoryId.trim().isEmpty) {
        throw Exception('فئة المقالة مطلوبة');
      }

      // إنشاء معرف فريد للمقالة
      final articleId = _uuid.v4();

      String imageUrl = '';

      if (imageFile != null) {
        final imagePath = 'articles/$articleId/cover/${_uuid.v4()}.jpg';
        final uploadedUrl = await _storageRepository.uploadFile(
          imageFile,
          imagePath,
        );
        imageUrl = uploadedUrl ?? '';
        LoggerService.info(
          'تم رفع صورة المقالة: $imageUrl',
          tag: 'AdminUsersCubit',
        );
      }

      // إعداد بيانات المقالة
      final articleData = {
        'title': title.trim(),
        'content': content.trim(),
        'imageUrl': imageUrl,
        'categoryId': categoryId,
        'tags': tags,
        'isPublished': isPublished,
        'isFeatured': isFeatured,
        'publishDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'createdAt': FieldValue.serverTimestamp(),
      };

      // حفظ المقالة في Firebase
      await _firestore.collection('articles').doc(articleId).set(articleData);

      emit(AdminArticleAdded(articleId));
      LoggerService.info(
        'تم إضافة المقالة بنجاح: $articleId',
        tag: 'AdminUsersCubit',
      );
      loadArticles();
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة المقالة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminArticlesError('حدث خطأ أثناء إضافة المقالة: ${e.toString()}'));
    }
  }

  /// تحديث مقالة
  Future<void> updateArticle({
    required String articleId,
    required String title,
    required String content,
    required String categoryId,
    required List<String> tags,
    required bool isPublished,
    required bool isFeatured,
    File? imageFile,
    String? currentImageUrl,
  }) async {
    emit(AdminArticleUpdating());
    try {
      LoggerService.info(
        'بدء تحديث المقالة: $articleId',
        tag: 'AdminUsersCubit',
      );

      String imageUrl = currentImageUrl ?? '';

      if (imageFile != null) {
        final imagePath = 'articles/$articleId/cover/${_uuid.v4()}.jpg';
        final uploadedUrl = await _storageRepository.uploadFile(
          imageFile,
          imagePath,
        );
        imageUrl = uploadedUrl ?? currentImageUrl ?? '';
        LoggerService.info(
          'تم تحديث صورة المقالة: $imageUrl',
          tag: 'AdminUsersCubit',
        );
      }

      // إعداد بيانات التحديث
      final updateData = {
        'title': title.trim(),
        'content': content.trim(),
        'imageUrl': imageUrl,
        'categoryId': categoryId,
        'tags': tags,
        'isPublished': isPublished,
        'isFeatured': isFeatured,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // تحديث المقالة في Firebase
      await _firestore.collection('articles').doc(articleId).update(updateData);

      emit(AdminArticleUpdated());
      LoggerService.info(
        'تم تحديث المقالة بنجاح: $articleId',
        tag: 'AdminUsersCubit',
      );
      loadArticles();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث المقالة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminArticlesError('حدث خطأ أثناء تحديث المقالة: ${e.toString()}'));
    }
  }

  /// حذف مقالة
  Future<void> deleteArticle(String articleId) async {
    emit(AdminArticleDeleting());
    try {
      LoggerService.info('بدء حذف المقالة: $articleId', tag: 'AdminUsersCubit');

      // حذف المقالة من Firebase
      await _firestore.collection('articles').doc(articleId).delete();

      emit(AdminArticleDeleted());
      LoggerService.info(
        'تم حذف المقالة بنجاح: $articleId',
        tag: 'AdminUsersCubit',
      );
      loadArticles();
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف المقالة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminArticlesError('حدث خطأ أثناء حذف المقالة: ${e.toString()}'));
    }
  }

  /// تبديل حالة نشر المقالة
  Future<void> toggleArticlePublishStatus(
    String articleId,
    bool isPublished,
  ) async {
    try {
      LoggerService.info(
        'تحديث حالة نشر المقالة: $articleId إلى $isPublished',
        tag: 'AdminUsersCubit',
      );

      // تحديث حالة النشر في Firebase
      await _firestore.collection('articles').doc(articleId).update({
        'isPublished': isPublished,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      LoggerService.info(
        'تم تحديث حالة نشر المقالة: $articleId',
        tag: 'AdminUsersCubit',
      );
      loadArticles();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث حالة نشر المقالة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(
        AdminArticlesError('حدث خطأ أثناء تحديث حالة النشر: ${e.toString()}'),
      );
    }
  }

  /// تبديل حالة تمييز المقالة
  Future<void> toggleArticleFeaturedStatus(
    String articleId,
    bool isFeatured,
  ) async {
    try {
      LoggerService.info(
        'تحديث حالة تمييز المقالة: $articleId إلى $isFeatured',
        tag: 'AdminUsersCubit',
      );

      // تحديث حالة التمييز في Firebase
      await _firestore.collection('articles').doc(articleId).update({
        'isFeatured': isFeatured,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      LoggerService.info(
        'تم تحديث حالة تمييز المقالة: $articleId',
        tag: 'AdminUsersCubit',
      );
      loadArticles();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث حالة تمييز المقالة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(
        AdminArticlesError('حدث خطأ أثناء تحديث حالة التمييز: ${e.toString()}'),
      );
    }
  }

  // ==================== إدارة النصائح اليومية ====================

  /// تحميل قائمة النصائح اليومية من Firebase
  Future<void> loadTips({String? categoryId}) async {
    emit(AdminTipsLoading());
    try {
      LoggerService.info(
        'بدء تحميل النصائح اليومية من Firebase - الفئة: ${categoryId ?? "جميع الفئات"}',
        tag: 'AdminUsersCubit',
      );

      // جلب النصائح من Firebase
      QuerySnapshot tipsSnapshot;

      if (categoryId != null && categoryId.isNotEmpty) {
        // جلب النصائح حسب الفئة
        tipsSnapshot =
            await _firestore
                .collection('daily_tips')
                .where('categoryId', isEqualTo: categoryId)
                .get();
      } else {
        // جلب جميع النصائح مع الترتيب
        tipsSnapshot =
            await _firestore
                .collection('daily_tips')
                .orderBy('date', descending: true)
                .get();
      }

      // تحويل البيانات إلى قائمة نماذج
      var tips =
          tipsSnapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return DailyTipModel(
              id: doc.id,
              title: data['title'] ?? '',
              content: data['content'] ?? '',
              imageUrl: data['imageUrl'] ?? '',
              date: data['date']?.toDate() ?? DateTime.now(),
              categoryId: data['categoryId'] ?? '',
              tags: List<String>.from(data['tags'] ?? []),
              moreInfoUrl: data['moreInfoUrl'] ?? '',
            );
          }).toList();

      // ترتيب النصائح حسب التاريخ (الأحدث أولاً) إذا كانت مفلترة
      if (categoryId != null && categoryId.isNotEmpty) {
        tips.sort((a, b) => b.date.compareTo(a.date));
      }

      emit(AdminTipsLoaded(tips));
      LoggerService.info(
        'تم تحميل ${tips.length} نصيحة بنجاح',
        tag: 'AdminUsersCubit',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تحميل النصائح اليومية',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(
        AdminTipsError('حدث خطأ أثناء تحميل النصائح اليومية: ${e.toString()}'),
      );
    }
  }

  /// إضافة نصيحة يومية جديدة
  Future<void> addTip({
    required String title,
    required String content,
    required String categoryId,
    required List<String> tags,
    String? moreInfoUrl,
    File? imageFile,
  }) async {
    emit(AdminTipAdding());
    try {
      LoggerService.info(
        'بدء إضافة نصيحة يومية جديدة: "$title"',
        tag: 'AdminUsersCubit',
      );

      // التحقق من صحة البيانات
      if (title.trim().isEmpty) {
        throw Exception('عنوان النصيحة مطلوب');
      }

      if (content.trim().isEmpty) {
        throw Exception('محتوى النصيحة مطلوب');
      }

      if (categoryId.trim().isEmpty) {
        throw Exception('فئة النصيحة مطلوبة');
      }

      // إنشاء معرف فريد للنصيحة
      final tipId = _uuid.v4();

      String imageUrl = '';

      if (imageFile != null) {
        final imagePath = 'daily_tips/$tipId/cover/${_uuid.v4()}.jpg';
        final uploadedUrl = await _storageRepository.uploadFile(
          imageFile,
          imagePath,
        );
        imageUrl = uploadedUrl ?? '';
        LoggerService.info(
          'تم رفع صورة النصيحة: $imageUrl',
          tag: 'AdminUsersCubit',
        );
      }

      // إعداد بيانات النصيحة
      final tipData = {
        'title': title.trim(),
        'content': content.trim(),
        'imageUrl': imageUrl,
        'date': FieldValue.serverTimestamp(),
        'categoryId': categoryId,
        'tags': tags,
        'moreInfoUrl': moreInfoUrl ?? '',
        'createdAt': FieldValue.serverTimestamp(),
      };

      // حفظ النصيحة في Firebase
      await _firestore.collection('daily_tips').doc(tipId).set(tipData);

      emit(AdminTipAdded(tipId));
      LoggerService.info(
        'تم إضافة النصيحة بنجاح: $tipId',
        tag: 'AdminUsersCubit',
      );
      loadTips();
    } catch (e) {
      LoggerService.error(
        'خطأ في إضافة النصيحة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminTipsError('حدث خطأ أثناء إضافة النصيحة: ${e.toString()}'));
    }
  }

  /// تحديث نصيحة يومية
  Future<void> updateTip({
    required String tipId,
    required String title,
    required String content,
    required String categoryId,
    required List<String> tags,
    String? moreInfoUrl,
    File? imageFile,
    String? currentImageUrl,
  }) async {
    emit(AdminTipUpdating());
    try {
      LoggerService.info('بدء تحديث النصيحة: $tipId', tag: 'AdminUsersCubit');

      String imageUrl = currentImageUrl ?? '';

      if (imageFile != null) {
        final imagePath = 'daily_tips/$tipId/cover/${_uuid.v4()}.jpg';
        final uploadedUrl = await _storageRepository.uploadFile(
          imageFile,
          imagePath,
        );
        imageUrl = uploadedUrl ?? currentImageUrl ?? '';
        LoggerService.info(
          'تم تحديث صورة النصيحة: $imageUrl',
          tag: 'AdminUsersCubit',
        );
      }

      // إعداد بيانات التحديث
      final updateData = {
        'title': title.trim(),
        'content': content.trim(),
        'imageUrl': imageUrl,
        'categoryId': categoryId,
        'tags': tags,
        'moreInfoUrl': moreInfoUrl ?? '',
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // تحديث النصيحة في Firebase
      await _firestore.collection('daily_tips').doc(tipId).update(updateData);

      emit(AdminTipUpdated());
      LoggerService.info(
        'تم تحديث النصيحة بنجاح: $tipId',
        tag: 'AdminUsersCubit',
      );
      loadTips();
    } catch (e) {
      LoggerService.error(
        'خطأ في تحديث النصيحة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminTipsError('حدث خطأ أثناء تحديث النصيحة: ${e.toString()}'));
    }
  }

  /// حذف نصيحة يومية
  Future<void> deleteTip(String tipId) async {
    emit(AdminTipDeleting());
    try {
      LoggerService.info('بدء حذف النصيحة: $tipId', tag: 'AdminUsersCubit');

      // حذف النصيحة من Firebase
      await _firestore.collection('daily_tips').doc(tipId).delete();

      emit(AdminTipDeleted());
      LoggerService.info(
        'تم حذف النصيحة بنجاح: $tipId',
        tag: 'AdminUsersCubit',
      );
      loadTips();
    } catch (e) {
      LoggerService.error(
        'خطأ في حذف النصيحة',
        error: e,
        tag: 'AdminUsersCubit',
      );
      emit(AdminTipsError('حدث خطأ أثناء حذف النصيحة: ${e.toString()}'));
    }
  }
}

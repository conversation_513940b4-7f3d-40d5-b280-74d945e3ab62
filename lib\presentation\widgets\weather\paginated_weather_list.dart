

import '../../../core/constants/weather/index.dart';

import '../../../imports.dart';

/// ويدجت قائمة الطقس مع Pagination
///
/// يعرض بيانات الطقس بشكل مقسم لتحسين الأداء
class PaginatedWeatherList extends StatefulWidget {
  /// بيانات الطقس
  final List<Listweather> weatherData;

  /// عدد العناصر في كل صفحة
  final int itemsPerPage;

  /// بناء عنصر واحد
  final Widget Function(BuildContext context, Listweather item, int index)
  itemBuilder;

  /// دالة عند تحميل المزيد
  final VoidCallback? onLoadMore;

  /// هل يوجد المزيد من البيانات
  final bool hasMoreData;

  /// حالة التحميل
  final bool isLoading;

  const PaginatedWeatherList({
    super.key,
    required this.weatherData,
    required this.itemBuilder,
    this.itemsPerPage = 10,
    this.onLoadMore,
    this.hasMoreData = false,
    this.isLoading = false,
  });

  @override
  State<PaginatedWeatherList> createState() => _PaginatedWeatherListState();
}

class _PaginatedWeatherListState extends State<PaginatedWeatherList> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 0;
  List<Listweather> _displayedItems = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didUpdateWidget(PaginatedWeatherList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.weatherData != oldWidget.weatherData) {
      _loadInitialData();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// تحميل البيانات الأولية
  void _loadInitialData() {
    setState(() {
      _currentPage = 0;
      _displayedItems = widget.weatherData.take(widget.itemsPerPage).toList();
    });
  }

  /// معالج التمرير
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  /// تحميل المزيد من البيانات
  void _loadMoreData() {
    if (widget.isLoading) return;

    final nextPageStartIndex = (_currentPage + 1) * widget.itemsPerPage;

    if (nextPageStartIndex < widget.weatherData.length) {
      setState(() {
        _currentPage++;
        final nextPageItems =
            widget.weatherData
                .skip(nextPageStartIndex)
                .take(widget.itemsPerPage)
                .toList();
        _displayedItems.addAll(nextPageItems);
      });
    } else if (widget.hasMoreData && widget.onLoadMore != null) {
      widget.onLoadMore!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // القائمة الرئيسية
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            itemCount: _displayedItems.length + (widget.isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _displayedItems.length) {
                return _buildLoadingIndicator();
              }

              return widget.itemBuilder(context, _displayedItems[index], index);
            },
          ),
        ),

        // مؤشر التحميل السفلي
        if (widget.isLoading && _displayedItems.isNotEmpty)
          _buildBottomLoadingIndicator(),
      ],
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      child: Center(
        child: CircularProgressIndicator(
          color: WeatherColors.primaryBackground,
          strokeWidth: 2,
        ),
      ),
    );
  }

  /// بناء مؤشر التحميل السفلي
  Widget _buildBottomLoadingIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: WeatherDimensions.mediumPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: WeatherDimensions.mediumIconSize,
            height: WeatherDimensions.mediumIconSize,
            child: CircularProgressIndicator(
              color: WeatherColors.primaryBackground,
              strokeWidth: 2,
            ),
          ),
          SizedBox(width: WeatherDimensions.mediumMargin),
          Text(
            'جاري تحميل المزيد...',
            style: TextStyle(
              fontSize: WeatherDimensions.bodyFontSize,
              fontFamily: AssetsFonts.cairo,
              color: WeatherColors.lightText,
            ),
          ),
        ],
      ),
    );
  }
}

/// ويدجت Lazy Loading للصور
class LazyLoadingImage extends StatefulWidget {
  /// مسار الصورة
  final String imagePath;

  /// عرض الصورة
  final double? width;

  /// ارتفاع الصورة
  final double? height;

  /// ويدجت البديل أثناء التحميل
  final Widget? placeholder;

  /// ويدجت البديل عند الخطأ
  final Widget? errorWidget;

  const LazyLoadingImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<LazyLoadingImage> createState() => _LazyLoadingImageState();
}

class _LazyLoadingImageState extends State<LazyLoadingImage> {
  bool _isLoaded = false;
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child:
          _hasError
              ? _buildErrorWidget()
              : _isLoaded
              ? _buildImage()
              : _buildPlaceholder(),
    );
  }

  /// بناء الصورة
  Widget _buildImage() {
    return Image.asset(
      widget.imagePath,
      width: widget.width,
      height: widget.height,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _hasError = true;
            });
          }
        });
        return _buildErrorWidget();
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _isLoaded = true;
              });
            }
          });
          return child;
        }

        if (frame != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _isLoaded = true;
              });
            }
          });
          return child;
        }

        return _buildPlaceholder();
      },
    );
  }

  /// بناء البديل أثناء التحميل
  Widget _buildPlaceholder() {
    return widget.placeholder ??
        Container(
          decoration: BoxDecoration(
            color: WeatherColors.lightText.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: SizedBox(
              width: WeatherDimensions.mediumIconSize,
              height: WeatherDimensions.mediumIconSize,
              child: CircularProgressIndicator(
                color: WeatherColors.primaryBackground,
                strokeWidth: 2,
              ),
            ),
          ),
        );
  }

  /// بناء ويدجت الخطأ
  Widget _buildErrorWidget() {
    return widget.errorWidget ??
        Container(
          decoration: BoxDecoration(
            color: WeatherColors.errorColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.error_outline,
            color: WeatherColors.errorColor,
            size: WeatherDimensions.mediumIconSize,
          ),
        );
  }
}

/// ويدجت تحسين الذاكرة للقوائم الطويلة
class MemoryOptimizedListView extends StatefulWidget {
  /// عدد العناصر
  final int itemCount;

  /// بناء العنصر
  final Widget Function(BuildContext context, int index) itemBuilder;

  /// ارتفاع العنصر (للتحسين)
  final double? itemExtent;

  /// عدد العناصر المحفوظة في الذاكرة
  final int cacheExtent;

  const MemoryOptimizedListView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.itemExtent,
    this.cacheExtent = 10,
  });

  @override
  State<MemoryOptimizedListView> createState() =>
      _MemoryOptimizedListViewState();
}

class _MemoryOptimizedListViewState extends State<MemoryOptimizedListView> {
  final Map<int, Widget> _cachedWidgets = {};
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    _cachedWidgets.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: widget.itemCount,
      itemExtent: widget.itemExtent,
      cacheExtent: widget.cacheExtent.toDouble(),
      itemBuilder: (context, index) {
        // استخدام التخزين المؤقت للويدجتس
        if (_cachedWidgets.containsKey(index)) {
          return _cachedWidgets[index]!;
        }

        final widget = this.widget.itemBuilder(context, index);

        // حفظ الويدجت في التخزين المؤقت
        if (_cachedWidgets.length < this.widget.cacheExtent) {
          _cachedWidgets[index] = widget;
        }

        return widget;
      },
    );
  }
}

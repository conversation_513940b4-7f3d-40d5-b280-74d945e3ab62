
import 'package:agriculture/data/models/education/daily_tip_model.dart';

import 'package:agriculture/presentation/widgets/education/quick_tips_section.dart';

import 'package:cloud_firestore/cloud_firestore.dart';


import '../../../imports.dart';

/// ويدجت صفحة النصائح اليومية
///
/// يعرض هذا الويدجت جميع النصائح اليومية المحلية اليمنية
/// مع إمكانية البحث والتصفية حسب الفئة
class DailyTipsPageWidget extends StatefulWidget {
  /// إنشاء ويدجت صفحة النصائح اليومية
  const DailyTipsPageWidget({super.key});

  @override
  State<DailyTipsPageWidget> createState() => _DailyTipsPageWidgetState();
}

class _DailyTipsPageWidgetState extends State<DailyTipsPageWidget> {
  /// قائمة النصائح المعروضة (بعد التصفية)
  List<DailyTipModel> _displayedTips = [];

  /// قائمة جميع النصائح (محلية + Firebase)
  List<DailyTipModel> _allTips = [];

  /// قائمة النصائح المحلية
  List<DailyTipModel> _localTips = [];

  /// قائمة النصائح من Firebase
  List<DailyTipModel> _firebaseTips = [];

  /// الفئة المحددة للتصفية
  String _selectedCategory = EducationCategoriesConstants.allCourses;

  /// حالة التحميل
  bool _isLoading = true;

  /// قائمة الفئات المتاحة
  final List<String> _categories = EducationCategoriesConstants.allCategories;

  @override
  void initState() {
    super.initState();
    _loadTips();
    // تحميل آخر نصيحة من الأدمن لعرضها في بطاقة نصيحة اليوم
    context.read<EducationCubit>().loadDailyTip();
  }



  /// تحميل النصائح
  ///
  /// يقوم بتحميل النصائح المحلية اليمنية ونصائح Firebase ودمجها
  void _loadTips() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل النصائح المحلية
      _localTips = YemenDailyTipsData.yemenTips;

      // تحميل النصائح من Firebase
      await _loadFirebaseTips();

      // دمج النصائح
      _allTips = [..._localTips, ..._firebaseTips];

      // ترتيب النصائح حسب التاريخ (الأحدث أولاً)
      _allTips.sort((a, b) => b.date.compareTo(a.date));

      // تطبيق التصفية
      _filterTips();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      LoggerService.debug('خطأ في تحميل النصائح: $e');

      // في حالة الخطأ، استخدم النصائح المحلية فقط
      if (mounted) {
        setState(() {
          _allTips = _localTips;
          _filterTips();
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل النصائح من Firebase
  ///
  /// يقوم بجلب النصائح من Firebase وتحويلها إلى نماذج
  Future<void> _loadFirebaseTips() async {
    try {
      final firestore = FirebaseFirestore.instance;

      // جلب النصائح من Firebase
      final tipsSnapshot = await firestore
          .collection('daily_tips')
          .where('isActive', isEqualTo: true)
          .get();

      _firebaseTips = tipsSnapshot.docs.map((doc) {
        final data = doc.data();
        return DailyTipModel(
          id: doc.id,
          title: data['title'] ?? '',
          content: data['content'] ?? '',
          categoryId: data['categoryId'] ?? '',
          tags: List<String>.from(data['tags'] ?? []),
          date: data['createdAt']?.toDate() ?? DateTime.now(),
        );
      }).toList();

      LoggerService.debug('تم تحميل ${_firebaseTips.length} نصيحة من Firebase');
    } catch (e) {
      LoggerService.debug('خطأ في تحميل النصائح من Firebase: $e');
      _firebaseTips = [];
    }
  }

  /// تصفية النصائح
  ///
  /// يقوم بتصفية النصائح حسب الفئة المحددة فقط
  void _filterTips() {
    setState(() {
      _displayedTips = _allTips.where((tip) {
        // تصفية حسب الفئة
        return _selectedCategory == EducationCategoriesConstants.allCourses ||
            tip.categoryId == _selectedCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'النصائح اليومية',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading ? _buildLoadingState() : _buildContent(),
    );
  }

  /// بناء حالة التحميل
  ///
  /// يعرض مؤشر التحميل أثناء تحميل البيانات
  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  ///
  /// يعرض شريط البحث والتصفية وقائمة النصائح في تخطيط قابل للتمرير
  Widget _buildContent() {
    return CustomScrollView(
      slivers: [
        // نصيحة اليوم المميزة
        SliverToBoxAdapter(
          child: _buildTipOfTheDaySection(),
        ),

        // قسم النصائح السريعة
        SliverToBoxAdapter(
          child: _buildQuickTipsSection(),
        ),

        // قسم التبويبات للتصفية
        SliverToBoxAdapter(
          child: _buildCategoryTabsSection(),
        ),

        // إحصائيات سريعة
        SliverToBoxAdapter(
          child: _buildStatsSection(),
        ),

        // قائمة النصائح
        _displayedTips.isEmpty
            ? SliverToBoxAdapter(
                child: _buildEmptyState(),
              )
            : SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final tip = _displayedTips[index];
                    return EnhancedDailyTipCard(
                      tip: tip,
                      onTap: () => _showTipDetails(tip),
                    );
                  },
                  childCount: _displayedTips.length,
                ),
              ),
      ],
    );
  }

  /// بناء قسم نصيحة اليوم المميزة
  ///
  /// يعرض آخر نصيحة رفعها الأدمن من Firebase في تصميم مميز
  /// إذا لم توجد نصائح من الأدمن، يعرض النصيحة المحلية كاحتياطي
  Widget _buildTipOfTheDaySection() {
    return BlocBuilder<EducationCubit, EducationState>(
      builder: (context, state) {
        DailyTipModel tipOfTheDay;

        // تحديد النصيحة المراد عرضها حسب الحالة
        if (state is EducationDailyTipLoaded && state.dailyTip != null) {
          // استخدام آخر نصيحة من الأدمن
          tipOfTheDay = state.dailyTip!;
        } else {
          // استخدام النصيحة المحلية كاحتياطي
          tipOfTheDay = YemenDailyTipsData.getTipOfTheDay();
        }

    return Container(
      margin: const EdgeInsets.all(16),
      constraints: const BoxConstraints(
        maxHeight: 200, // تحديد ارتفاع أقصى لتجنب overflow
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF66BB6A), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showTipDetails(tipOfTheDay), // إضافة إمكانية النقر لعرض التفاصيل
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16), // تقليل المساحة من 20 إلى 16
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // رأس نصيحة اليوم
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.lightbulb,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'نصيحة اليوم',
                            style: TextStyles.of(context).bodySmall(
                              fontSize: 12,
                              fontFamily: AssetsFonts.cairo,
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            tipOfTheDay.title,
                            style: TextStyles.of(context).headlineMedium(
                              fontSize: 16,
                              fontFamily: AssetsFonts.cairo,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    // أيقونة للإشارة إلى إمكانية النقر
                    Icon(
                      Icons.touch_app,
                      color: Colors.white.withValues(alpha: 0.7),
                      size: 20,
                    ),
                  ],
                ),

                const SizedBox(height: 8), // تقليل المساحة من 12 إلى 8

                // محتوى النصيحة - مقطوع مع إمكانية النقر للمزيد
                Text(
                  tipOfTheDay.content,
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 13, // تقليل حجم الخط من 14 إلى 13
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.white.withValues(alpha: 0.95),
                    height: 1.3, // تقليل ارتفاع السطر من 1.4 إلى 1.3
                  ),
                  maxLines: 2, // تقليل عدد الأسطر من 3 إلى 2
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 6), // تقليل المساحة من 8 إلى 6

                // مؤشر "اضغط للمزيد" وفئة النصيحة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // فئة النصيحة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4), // تقليل المساحة
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16), // تقليل الانحناء
                      ),
                      child: Text(
                        EducationCategoriesConstants.getCategoryName(tipOfTheDay.categoryId),
                        style: TextStyles.of(context).bodySmall(
                          fontSize: 10, // تقليل حجم الخط من 11 إلى 10
                          fontFamily: AssetsFonts.cairo,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // مؤشر "اضغط للمزيد"
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3), // تقليل المساحة
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10), // تقليل الانحناء
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'اضغط للمزيد',
                            style: TextStyles.of(context).bodySmall(
                              fontSize: 9, // تقليل حجم الخط من 10 إلى 9
                              fontFamily: AssetsFonts.cairo,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 3), // تقليل المساحة من 4 إلى 3
                          Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white,
                            size: 10, // تقليل حجم الأيقونة من 12 إلى 10
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
      },
    );
  }

  /// بناء قسم النصائح السريعة
  ///
  /// يعرض قائمة أفقية من النصائح السريعة
  Widget _buildQuickTipsSection() {
    final quickTips = YemenDailyTipsData.getRecentTips(limit: 5);

    return QuickTipsSection(
      tips: quickTips,
      onTipTap: (tip) => _showTipDetails(tip),
      onViewMore: () {
        // التمرير لأسفل لعرض جميع النصائح
        // يمكن تحسين هذا لاحقاً بإضافة ScrollController
      },
    );
  }

  /// بناء قسم التبويبات للتصفية
  ///
  /// يحتوي على تبويبات أفقية لتصفية النصائح حسب الفئة
  Widget _buildCategoryTabsSection() {
    return CategoryTabsWidget(
      selectedCategory: _selectedCategory,
      onCategoryChanged: (category) {
        setState(() {
          _selectedCategory = category;
        });
        _filterTips();
      },
      categories: _categories,
    );
  }

  /// بناء قسم الإحصائيات
  ///
  /// يعرض عدد النصائح الإجمالي والمعروض
  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.grey[50],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'إجمالي النصائح: ${_allTips.length}',
            style: TextStyles.of(context).bodySmall(
              fontSize: 12,
              fontFamily: AssetsFonts.cairo,
              color: Colors.grey[600],
            ),
          ),
          Text(
            'المعروضة: ${_displayedTips.length}',
            style: TextStyles.of(context).bodySmall(
              fontSize: 12,
              fontFamily: AssetsFonts.cairo,
              color: const Color(0xFF4CAF50),
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }



  /// بناء حالة القائمة الفارغة
  ///
  /// يعرض رسالة عندما لا توجد نصائح للعرض
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lightbulb_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _selectedCategory != EducationCategoriesConstants.allCourses
                  ? 'لم يتم إضافة نصائح في فئة "${EducationCategoriesConstants.getCategoryName(_selectedCategory)}" بعد'
                  : 'لم يتم إضافة نصائح بعد',
              style: TextStyles.of(context).headlineMedium(
                fontSize: 16,
                fontFamily: AssetsFonts.cairo,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _selectedCategory != EducationCategoriesConstants.allCourses
                  ? 'جرب اختيار فئة أخرى أو تصفح جميع النصائح'
                  : 'سيتم إضافة نصائح جديدة من الأدمن قريباً',
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل النصيحة باستخدام الديالوج المحسن
  ///
  /// المعلمات:
  /// - [tip]: النصيحة المراد عرض تفاصيلها
  ///
  /// يقوم بعرض الديالوج المحسن مع ألوان متناسقة وتصميم جميل
  void _showTipDetails(DailyTipModel tip) {
    EnhancedTipDialog.show(context, tip);
  }
}

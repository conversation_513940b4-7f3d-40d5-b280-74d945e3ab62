import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:agriculture/core/constants/daily_tips_constants.dart';
import 'package:agriculture/core/constants/education_categories_constants.dart';
import 'package:agriculture/data/models/education/daily_tip_model.dart';
import 'package:flutter/material.dart';

/// ديالوج محسن لعرض تفاصيل النصيحة
///
/// يعرض هذا الديالوج النصيحة بتصميم جميل ومحسن
/// مع ألوان متناسقة وتخطيط منظم
class EnhancedTipDialog extends StatelessWidget {
  /// نموذج النصيحة المراد عرضها
  final DailyTipModel tip;

  /// إنشاء ديالوج محسن لعرض تفاصيل النصيحة
  ///
  /// المعلمات:
  /// - [tip]: نموذج النصيحة المراد عرضها
  const EnhancedTipDialog({
    super.key,
    required this.tip,
  });

  /// عرض الديالوج
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [tip]: النصيحة المراد عرضها
  ///
  /// يقوم بعرض الديالوج المحسن للنصيحة
  static Future<void> show(BuildContext context, DailyTipModel tip) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => EnhancedTipDialog(tip: tip),
    );
  }

  @override
  Widget build(BuildContext context) {
    final gradient = DailyTipsConstants.getCategoryGradient(tip.categoryId);
    final primaryColor = DailyTipsConstants.getCategoryColor(tip.categoryId);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(DailyTipsConstants.defaultSpacing),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 500,
          maxHeight: 600,
        ),
        decoration: BoxDecoration(
          color: DailyTipsConstants.cardBackgroundColor,
          borderRadius: BorderRadius.circular(DailyTipsConstants.cardBorderRadius),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس الديالوج مع تدرج لوني
            _buildDialogHeader(context, gradient, primaryColor),

            // محتوى الديالوج
            Flexible(
              child: _buildDialogContent(context, primaryColor),
            ),

            // تذييل الديالوج مع زر الإغلاق
            _buildDialogFooter(context, primaryColor),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الديالوج
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [gradient]: تدرج ألوان الفئة
  /// - [primaryColor]: اللون الأساسي للفئة
  ///
  /// يعرض رأس الديالوج مع الأيقونة والعنوان
  Widget _buildDialogHeader(BuildContext context, List<Color> gradient, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(DailyTipsConstants.defaultSpacing),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(DailyTipsConstants.cardBorderRadius),
          topRight: Radius.circular(DailyTipsConstants.cardBorderRadius),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الفئة
          Container(
            padding: const EdgeInsets.all(DailyTipsConstants.smallSpacing),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              DailyTipsConstants.getCategoryIcon(tip.categoryId),
              color: Colors.white,
              size: DailyTipsConstants.largeIconSize,
            ),
          ),

          const SizedBox(width: DailyTipsConstants.smallSpacing),

          // معلومات الرأس
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اسم الفئة
                Text(
                  EducationCategoriesConstants.getCategoryName(tip.categoryId),
                  style: TextStyles.of(context).bodySmall(
                    fontSize: 12,
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.white.withValues(alpha: 0.9),
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 4),

                // عنوان النصيحة
                Text(
                  tip.title,
                  style: TextStyles.of(context).headlineMedium(
                    fontSize: 18,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    height: 1.3,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // زر الإغلاق
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الديالوج
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [primaryColor]: اللون الأساسي للفئة
  ///
  /// يعرض محتوى النصيحة والعلامات
  Widget _buildDialogContent(BuildContext context, Color primaryColor) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(DailyTipsConstants.defaultSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // محتوى النصيحة
          Text(
            tip.content,
            style: TextStyles.of(context).bodyMedium(
              fontSize: 16,
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kGrey100,
              height: 1.6,
            ),
          ),

          // العلامات إذا كانت موجودة
          if (tip.tags.isNotEmpty) ...[
            const SizedBox(height: DailyTipsConstants.defaultSpacing),
            _buildTagsSection(context, primaryColor),
          ],

          // معلومات إضافية
          const SizedBox(height: DailyTipsConstants.defaultSpacing),
          _buildAdditionalInfo(context, primaryColor),
        ],
      ),
    );
  }

  /// بناء قسم العلامات
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [primaryColor]: اللون الأساسي للفئة
  ///
  /// يعرض العلامات المرتبطة بالنصيحة
  Widget _buildTagsSection(BuildContext context, Color primaryColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العلامات:',
          style: TextStyles.of(context).bodyMedium(
            fontSize: 14,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: primaryColor,
          ),
        ),

        const SizedBox(height: DailyTipsConstants.smallSpacing),

        Wrap(
          spacing: DailyTipsConstants.smallSpacing,
          runSpacing: DailyTipsConstants.smallSpacing,
          children: tip.tags.map((tag) => Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 6,
            ),
            decoration: BoxDecoration(
              color: primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              tag,
              style: TextStyles.of(context).bodySmall(
                fontSize: 12,
                fontFamily: AssetsFonts.cairo,
                color: primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  /// بناء المعلومات الإضافية
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [primaryColor]: اللون الأساسي للفئة
  ///
  /// يعرض التاريخ ومعلومات أخرى
  Widget _buildAdditionalInfo(BuildContext context, Color primaryColor) {
    return Container(
      padding: const EdgeInsets.all(DailyTipsConstants.smallSpacing),
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(DailyTipsConstants.smallCardBorderRadius),
        border: Border.all(
          color: primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            size: 16,
            color: primaryColor,
          ),

          const SizedBox(width: DailyTipsConstants.smallSpacing),

          Text(
            _formatDate(tip.date),
            style: TextStyles.of(context).bodySmall(
              fontSize: 12,
              fontFamily: AssetsFonts.cairo,
              color: primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الديالوج
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [primaryColor]: اللون الأساسي للفئة
  ///
  /// يعرض زر الإغلاق المحسن
  Widget _buildDialogFooter(BuildContext context, Color primaryColor) {
    return Padding(
      padding: const EdgeInsets.all(DailyTipsConstants.defaultSpacing),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.check, size: 18),
            label: Text(
              'فهمت',
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: DailyTipsConstants.defaultSpacing,
                vertical: DailyTipsConstants.smallSpacing,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(DailyTipsConstants.smallCardBorderRadius),
              ),
              elevation: 2,
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق التاريخ
  ///
  /// المعلمات:
  /// - [date]: التاريخ المراد تنسيقه
  ///
  /// يقوم بتنسيق التاريخ بصيغة مقروءة
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسابيع';
    }
  }
}


import '../imports.dart';

/// مساعد تحديد المسار الأولي
///
/// يوفر هذا الكلاس وظائف لتحديد المسار الأولي للتطبيق بناءً على حالة المستخدم.
class InitialRouteHelper {
  // منع إنشاء نسخة من الكلاس
  InitialRouteHelper._();

  /// تحديد المسار الأولي للتطبيق
  ///
  /// يقوم بتحديد المسار الأولي بناءً على حالة المستخدم:
  /// - إذا كان المستخدم مسجل الدخول، يتم التحقق من اكتمال بيانات البروفايل:
  ///   - إذا كان قد أكمل بيانات البروفايل، يتم توجيهه إلى الصفحة الرئيسية.
  ///   - إذا لم يكمل بيانات البروفايل، يتم توجيهه إلى صفحة إكمال البروفايل.
  /// - إذا لم يكن المستخدم مسجل الدخول ولم ير صفحة الترحيب من قبل، يتم توجيهه إلى صفحة الترحيب.
  /// - إذا لم يكن المستخدم مسجل الدخول ورأى صفحة الترحيب من قبل، يتم توجيهه إلى صفحة تسجيل الدخول.
  static Future<String> determineInitialRoute() async {
    // تسجيل بداية تحديد المسار الأولي
    LoggerService.debug(
      'تحديد المسار الأولي: isOnBoarding=$isOnBoarding, uid=$uid',
      tag: 'InitialRoute',
    );

    // التحقق من حالة المستخدم
    final currentUser = getCurrentUser();

    // تسجيل إضافي للتصحيح
    LoggerService.debug(
      'حالة المستخدم: currentUser=${currentUser != null ? "موجود" : "غير موجود"}, uid="$uid"',
      tag: 'InitialRoute',
    );

    // إذا كان المستخدم مسجل الدخول، نتحقق من اكتمال بيانات البروفايل
    // نتحقق من أن المستخدم موجود في Firebase وأن uid غير فارغ
    if (currentUser != null && uid.isNotEmpty) {
      // التحقق من اكتمال بيانات البروفايل
      try {
        // استخدام AuthRepository للتحقق من اكتمال بيانات البروفايل
        final authRepository = AuthRepository();
        final hasCompletedProfile =
            await authRepository.hasUserCompletedProfile(uid);

        LoggerService.debug(
          'نتيجة التحقق من اكتمال بيانات البروفايل: $hasCompletedProfile',
          tag: 'InitialRoute',
        );

        // إذا لم يكمل المستخدم بيانات البروفايل، توجيهه إلى صفحة إكمال البروفايل
        if (!hasCompletedProfile) {
          LoggerService.warning(
            'المستخدم لم يكمل بيانات البروفايل. سيتم توجيهه إلى صفحة إكمال البروفايل.',
            tag: 'InitialRoute',
          );

          return RouteConstants.registerProfile;
        }

        // إذا أكمل المستخدم بيانات البروفايل، توجيهه إلى الصفحة الرئيسية
        LoggerService.debug(
          'المستخدم مسجل الدخول وأكمل بيانات البروفايل، توجيهه إلى الصفحة الرئيسية',
          tag: 'InitialRoute',
        );
        return RouteConstants.home;
      } catch (e) {
        LoggerService.error(
          'خطأ أثناء التحقق من اكتمال بيانات البروفايل: $e',
          error: e,
          tag: 'InitialRoute',
        );

        // في حالة حدوث خطأ، نوجه المستخدم إلى صفحة إكمال البروفايل للتأكد
        return RouteConstants.registerProfile;
      }
    }

    // إذا كان هناك تناقض بين حالة Firebase وحالة uid المخزنة
    if (currentUser != null && uid.isEmpty) {
      // تحديث uid من Firebase
      uid = currentUser.uid;
      SharedPrefs.setString('uid', uid);
      SharedPrefs.setBool('isAuth', true);

      LoggerService.debug(
        'تم تحديث uid من Firebase: uid=$uid',
        tag: 'InitialRoute',
      );

      // التحقق من اكتمال بيانات البروفايل
      try {
        // استخدام AuthRepository للتحقق من اكتمال بيانات البروفايل
        final authRepository = AuthRepository();
        final hasCompletedProfile =
            await authRepository.hasUserCompletedProfile(uid);

        LoggerService.debug(
          'نتيجة التحقق من اكتمال بيانات البروفايل: $hasCompletedProfile',
          tag: 'InitialRoute',
        );

        // إذا لم يكمل المستخدم بيانات البروفايل، توجيهه إلى صفحة إكمال البروفايل
        if (!hasCompletedProfile) {
          LoggerService.warning(
            'المستخدم لم يكمل بيانات البروفايل. سيتم توجيهه إلى صفحة إكمال البروفايل.',
            tag: 'InitialRoute',
          );

          return RouteConstants.registerProfile;
        }
      } catch (e) {
        LoggerService.error(
          'خطأ أثناء التحقق من اكتمال بيانات البروفايل: $e',
          error: e,
          tag: 'InitialRoute',
        );

        // في حالة حدوث خطأ، نوجه المستخدم إلى صفحة إكمال البروفايل للتأكد
        return RouteConstants.registerProfile;
      }

      return RouteConstants.home;
    }

    // إذا كان uid غير فارغ ولكن المستخدم غير موجود في Firebase
    if (currentUser == null && uid.isNotEmpty) {
      // إعادة تعيين متغيرات الجلسة
      uid = '';
      SharedPrefs.remove('uid');
      SharedPrefs.setBool('isAuth', false);

      LoggerService.debug(
        'تم إعادة تعيين متغيرات الجلسة لأن المستخدم غير موجود في Firebase',
        tag: 'InitialRoute',
      );
    }

    // إذا لم يكن المستخدم مسجل الدخول ولم ير صفحة الترحيب من قبل، توجيهه إلى صفحة الترحيب
    if (!isOnBoarding) {
      LoggerService.debug(
        'المستخدم لم ير صفحة الترحيب من قبل، توجيهه إلى صفحة الترحيب',
        tag: 'InitialRoute',
      );
      return RouteConstants.onBoarding;
    }

    // إذا لم يكن المستخدم مسجل الدخول ورأى صفحة الترحيب من قبل، توجيهه إلى صفحة تسجيل الدخول
    LoggerService.debug(
      'المستخدم رأى صفحة الترحيب من قبل، توجيهه إلى صفحة تسجيل الدخول',
      tag: 'InitialRoute',
    );
    return RouteConstants.login;
  }

  /// تحديث حالة صفحة الترحيب
  ///
  /// يقوم بتعيين متغير isOnBoarding إلى true لتخطي صفحة الترحيب في المرات القادمة.
  static Future<void> markOnboardingAsCompleted() async {
    // تعيين متغير isOnBoarding إلى true
    isOnBoarding = true;

    // حفظ القيمة في التخزين المحلي
    await SharedPrefs.setBool(AppConstants.prefsKeyIsOnBoarding, true);

    // تسجيل تحديث حالة صفحة الترحيب
    LoggerService.debug(
      'تم تحديث حالة صفحة الترحيب: isOnBoarding=$isOnBoarding',
      tag: 'InitialRoute',
    );
  }
}

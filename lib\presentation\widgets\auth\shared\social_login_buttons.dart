import 'package:animate_do/animate_do.dart';

import 'package:flutter/services.dart';

import '../../../../imports.dart';

/// أزرار تسجيل الدخول الاجتماعية
///
/// يعرض هذا المكون أزرار تسجيل الدخول باستخدام الخدمات الاجتماعية مثل Google وFacebook ورقم الهاتف.
class SocialLoginButtons extends StatelessWidget {
  /// دالة تسجيل الدخول باستخدام Google
  final VoidCallback onGoogleTap;

  /// دالة تسجيل الدخول باستخدام Facebook
  final VoidCallback onFacebookTap;

  /// دالة تسجيل الدخول باستخدام رقم الهاتف
  final VoidCallback onPhoneTap;

  /// نص العنوان
  final String titleText;

  /// مدة التأثير
  final Duration animationDuration;

  /// منشئ أزرار تسجيل الدخول الاجتماعية
  const SocialLoginButtons({
    super.key,
    required this.onGoogleTap,
    required this.onFacebookTap,
    required this.onPhoneTap,
    this.titleText = 'أو سجل الدخول باستخدام',
    this.animationDuration = const Duration(milliseconds: 1900),
  });

  @override
  Widget build(BuildContext context) {
    return FadeInUp(
      duration: animationDuration,
      child: Column(
        children: [
          Text(
            titleText,
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              color: Colors.grey.shade700,
              fontSize: Dimensions.fontSizeM,
            ),
          ),
          SizedBox(height: Dimensions.spacingL),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // زر تسجيل الدخول بحساب Google
              _buildSocialButton(
                context: context,
                icon: AssetsLoginImage.google,
                onTap: onGoogleTap,
              ),
              SizedBox(width: Dimensions.spacingL),

              // زر تسجيل الدخول برقم الهاتف
              _buildPhoneSocialButton(
                context: context,
                onTap: onPhoneTap,
              ),
              SizedBox(width: Dimensions.spacingL),

              // زر تسجيل الدخول بحساب Facebook
              _buildSocialButton(
                context: context,
                icon: AssetsLoginImage.facebook,
                onTap: onFacebookTap,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// دالة لإنشاء زر التسجيل بالخدمات الخارجية
  Widget _buildSocialButton({
    required BuildContext context,
    required String icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(25),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.grey,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Image.asset(
            icon,
            width: 24,
            height: 24,
          ),
        ),
      ),
    );
  }

  /// دالة مخصصة لإنشاء زر التسجيل برقم الهاتف
  Widget _buildPhoneSocialButton({
    required BuildContext context,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        // تأثير صوتي عند النقر
        HapticFeedback.lightImpact();

        // تأخير بسيط قبل الانتقال لإظهار تأثير النقر
        Future.delayed(const Duration(milliseconds: 150), () {
          onTap();
        });
      },
      borderRadius: BorderRadius.circular(Dimensions.radiusS),
      splashColor: const Color.fromRGBO(0, 150, 0, 0.3),
      highlightColor: const Color.fromRGBO(0, 150, 0, 0.1),
      child: Ink(
        height: 52,
        width: 76,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(Dimensions.radiusS),
          color: const Color(0xFFF6F6F6),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            Icons.phone_android,
            size: Dimensions.iconSizeL,
            color: Colors.green.shade700,
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import '../../../../core/constants/colors.dart';
import '../../../../core/constants/text_styles.dart';
import '../../../../core/helpers/datetime_helper.dart';
import '../../../../domain/entities/marketing_products/message_entity.dart';
import '../../../../domain/entities/marketing_products/conversation_entity.dart';
import '../../../pages/marketing_products/constants/conversations_dimensions.dart';

/// فقاعة الرسالة
///
/// تعرض هذه الفقاعة رسالة واحدة في المحادثة
/// مع التمييز بين الرسائل المرسلة والمستقبلة
class MessageBubble extends StatelessWidget {
  /// الرسالة المراد عرضها
  final MessageEntity message;

  /// هل الرسالة من المستخدم الحالي
  final bool isMe;

  /// هل يجب إظهار الصورة الرمزية
  final bool showAvatar;

  /// دالة عند الضغط المطول
  final VoidCallback? onLongPress;

  /// إنشاء فقاعة الرسالة
  const MessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.showAvatar = true,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin:
          isMe
              ? ConversationsDimensions.sentMessageMargin
              : ConversationsDimensions.receivedMessageMargin,
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // صورة المرسل (للرسائل المستقبلة فقط)
          if (!isMe && showAvatar) _buildAvatar(),
          if (!isMe && !showAvatar)
            SizedBox(width: ConversationsDimensions.smallAvatarSize),

          // فقاعة الرسالة
          Flexible(
            child: GestureDetector(
              onLongPress: onLongPress,
              child: Container(
                padding: ConversationsDimensions.messageBubblePadding,
                decoration: BoxDecoration(
                  color: isMe ? AppColors.primary : AppColors.surface,
                  borderRadius: _getBorderRadius(),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadow.withValues(alpha: 0.1),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // محتوى الرسالة
                    _buildMessageContent(),

                    SizedBox(
                      height: ConversationsDimensions.messageStatusSpacing,
                    ),

                    // معلومات الحالة والوقت
                    _buildMessageInfo(),
                  ],
                ),
              ),
            ),
          ),

          // صورة المرسل (للرسائل المرسلة فقط)
          if (isMe && showAvatar) _buildAvatar(),
          if (isMe && !showAvatar)
            SizedBox(width: ConversationsDimensions.smallAvatarSize),
        ],
      ),
    );
  }

  /// بناء الصورة الرمزية
  Widget _buildAvatar() {
    return Container(
      width: ConversationsDimensions.smallAvatarSize,
      height: ConversationsDimensions.smallAvatarSize,
      margin: EdgeInsets.only(
        left: isMe ? 0 : ConversationsDimensions.smallSpacing,
        right: isMe ? ConversationsDimensions.smallSpacing : 0,
      ),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        shape: BoxShape.circle,
        border: Border.all(color: AppColors.border),
      ),
      child: Center(
        child: Text(
          _getInitials(),
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// بناء محتوى الرسالة
  Widget _buildMessageContent() {
    switch (message.messageType) {
      case MessageTypeEntity.text:
        return _buildTextMessage();
      case MessageTypeEntity.image:
        return _buildImageMessage();
      case MessageTypeEntity.productInquiry:
        return _buildProductInquiryMessage();
      case MessageTypeEntity.file:
        return _buildFileMessage();
      default:
        return _buildTextMessage();
    }
  }

  /// بناء رسالة نصية
  Widget _buildTextMessage() {
    return Text(
      message.content,
      style: AppTextStyles.bodyMedium.copyWith(
        color: isMe ? AppColors.onPrimary : AppColors.textPrimary,
      ),
    );
  }

  /// بناء رسالة صورة
  Widget _buildImageMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: ConversationsDimensions.imagePreviewSize,
          height: ConversationsDimensions.imagePreviewSize,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(
              ConversationsDimensions.imagePreviewRadius,
            ),
            border: Border.all(color: AppColors.border),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(
              ConversationsDimensions.imagePreviewRadius,
            ),
            child: const Icon(
              Icons.image,
              size: 48,
              color: AppColors.textSecondary,
            ),
          ),
        ),

        if (message.content.isNotEmpty) ...[
          SizedBox(height: ConversationsDimensions.imageTextSpacing),
          Text(
            message.content,
            style: AppTextStyles.bodyMedium.copyWith(
              color: isMe ? AppColors.onPrimary : AppColors.textPrimary,
            ),
          ),
        ],
      ],
    );
  }

  /// بناء رسالة استفسار عن منتج
  Widget _buildProductInquiryMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: (isMe ? AppColors.onPrimary : AppColors.primary).withValues(
          alpha: 0.1,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isMe
                  ? AppColors.onPrimary.withValues(alpha: 0.3)
                  : AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help_outline,
                size: 16,
                color: isMe ? AppColors.onPrimary : AppColors.primary,
              ),
              const SizedBox(width: 6),
              Text(
                'استفسار عن منتج',
                style: AppTextStyles.bodySmall.copyWith(
                  color: isMe ? AppColors.onPrimary : AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          if (message.relatedProductName != null) ...[
            const SizedBox(height: 6),
            Text(
              message.relatedProductName!,
              style: AppTextStyles.bodySmall.copyWith(
                color:
                    isMe
                        ? AppColors.onPrimary.withValues(alpha: 0.8)
                        : AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],

          const SizedBox(height: 8),
          Text(
            message.content,
            style: AppTextStyles.bodyMedium.copyWith(
              color: isMe ? AppColors.onPrimary : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة ملف
  Widget _buildFileMessage() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: (isMe ? AppColors.onPrimary : AppColors.success).withValues(
          alpha: 0.1,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              isMe
                  ? AppColors.onPrimary.withValues(alpha: 0.3)
                  : AppColors.success.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        message.content,
        style: AppTextStyles.bodyMedium.copyWith(
          color: isMe ? AppColors.onPrimary : AppColors.success,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// بناء معلومات الرسالة
  Widget _buildMessageInfo() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          _formatTime(message.sentAt),
          style: AppTextStyles.bodySmall.copyWith(
            color:
                isMe
                    ? AppColors.onPrimary.withValues(alpha: 0.7)
                    : AppColors.textSecondary,
            fontSize: 10,
          ),
        ),

        if (isMe) ...[
          SizedBox(width: ConversationsDimensions.statusTimeSpacing),
          Icon(
            message.isRead ? Icons.done_all : Icons.done,
            size: ConversationsDimensions.statusIconSize,
            color:
                message.isRead
                    ? AppColors.success
                    : AppColors.onPrimary.withValues(alpha: 0.7),
          ),
        ],
      ],
    );
  }

  /// الحصول على نصف قطر الزوايا
  BorderRadius _getBorderRadius() {
    const radius = ConversationsDimensions.messageBubbleRadius;

    if (isMe) {
      return BorderRadius.only(
        topLeft: const Radius.circular(radius),
        topRight: const Radius.circular(radius),
        bottomLeft: const Radius.circular(radius),
        bottomRight:
            showAvatar
                ? const Radius.circular(4)
                : const Radius.circular(radius),
      );
    } else {
      return BorderRadius.only(
        topLeft: const Radius.circular(radius),
        topRight: const Radius.circular(radius),
        bottomLeft:
            showAvatar
                ? const Radius.circular(4)
                : const Radius.circular(radius),
        bottomRight: const Radius.circular(radius),
      );
    }
  }

  /// الحصول على الأحرف الأولى
  String _getInitials() {
    final name = isMe ? 'أنا' : message.senderName;
    if (name.isEmpty) return '؟';

    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'
          .toUpperCase();
    }
  }

  /// تنسيق الوقت
  String _formatTime(DateTime time) {
    return DateTimeHelper.formatMessageTime(time);
  }
}

import 'dart:io';


import 'package:agriculture/presentation/bloc/admin/admin_users_cubit.dart';
import 'package:agriculture/presentation/bloc/admin/admin_users_state.dart';

import 'package:image_picker/image_picker.dart';

import '../../../imports.dart';

/// صفحة نموذج الدرس (إضافة/تعديل)
///
/// تسمح هذه الصفحة للمسؤول بإضافة درس جديد أو تعديل درس موجود
/// مع رفع الفيديو إلى Google Drive وحفظ البيانات في Firebase
class AdminLessonFormPage extends StatefulWidget {
  /// معرف الدورة
  final String courseId;

  /// عنوان الدورة
  final String courseTitle;

  /// معرف الدرس للتعديل (null للإضافة)
  final String? lessonId;

  /// إنشاء صفحة نموذج الدرس
  ///
  /// المعلمات:
  /// - [courseId]: معرف الدورة
  /// - [courseTitle]: عنوان الدورة
  /// - [lessonId]: معرف الدرس للتعديل (اختياري)
  const AdminLessonFormPage({
    super.key,
    required this.courseId,
    required this.courseTitle,
    this.lessonId,
  });

  @override
  State<AdminLessonFormPage> createState() => _AdminLessonFormPageState();
}

class _AdminLessonFormPageState extends State<AdminLessonFormPage> {
  /// مفتاح النموذج
  final _formKey = GlobalKey<FormState>();

  /// متحكمات النص
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _durationController = TextEditingController();
  final _orderController = TextEditingController();
  final _additionalContentController = TextEditingController();

  /// المتغيرات
  File? _selectedVideo;
  bool _isLoading = false;

  /// منتقي الفيديو
  final ImagePicker _picker = ImagePicker();

  /// هل هو وضع التعديل
  bool get isEditMode => widget.lessonId != null;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _durationController.dispose();
    _orderController.dispose();
    _additionalContentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocListener<AdminUsersCubit, AdminUsersState>(
        listener: _handleStateChanges,
        child: _buildBody(),
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isEditMode ? 'تعديل الدرس' : 'إضافة درس جديد',
            style: TextStyles.of(context).bodyMedium(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.kWhite,
            ),
          ),
          Text(
            widget.courseTitle,
            style: TextStyles.of(context).bodySmall(
              fontSize: 12,
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kWhite.withOpacity(0.8),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
      backgroundColor: AssetsColors.primary,
      foregroundColor: AssetsColors.kWhite,
      elevation: 0,
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('معلومات الدرس الأساسية'),
            const SizedBox(height: 16),
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildSectionTitle('فيديو الدرس'),
            const SizedBox(height: 16),
            _buildVideoSection(),
            const SizedBox(height: 24),
            _buildSectionTitle('محتوى إضافي (اختياري)'),
            const SizedBox(height: 16),
            _buildAdditionalContentSection(),
            const SizedBox(height: 32),
            _buildSubmitButton(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyles.of(context).bodyMedium(
        fontSize: 18,
        fontFamily: AssetsFonts.cairo,
        fontWeight: FontWeight.bold,
        color: AssetsColors.kGrey100,
      ),
    );
  }

  /// بناء قسم المعلومات الأساسية
  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        AuthField(
          controller: _titleController,
          labelText: 'عنوان الدرس *',
          prefixIcon: Icons.title,
          keyboardType: TextInputType.text,
          validator: AdminValidation.validateLessonTitle,
        ),
        const SizedBox(height: 16),
        AuthField(
          controller: _descriptionController,
          labelText: 'وصف الدرس *',
          prefixIcon: Icons.description,
          keyboardType: TextInputType.multiline,
          maxLines: 3,
          validator: AdminValidation.validateLessonDescription,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: AuthField(
                controller: _durationController,
                labelText: 'المدة (بالدقائق) *',
                prefixIcon: Icons.access_time,
                keyboardType: TextInputType.number,
                validator: AdminValidation.validateLessonDuration,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AuthField(
                controller: _orderController,
                labelText: 'ترتيب الدرس *',
                prefixIcon: Icons.format_list_numbered,
                keyboardType: TextInputType.number,
                validator: AdminValidation.validateLessonOrder,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم الفيديو
  Widget _buildVideoSection() {
    return GestureDetector(
      onTap: _pickVideo,
      child: Container(
        width: double.infinity,
        height: 120,
        decoration: BoxDecoration(
          border: Border.all(
            color: AssetsColors.primary.withOpacity(0.3),
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color: AssetsColors.kGrey20.withOpacity(0.3),
        ),
        child:
            _selectedVideo != null
                ? _buildVideoPreview()
                : _buildVideoPlaceholder(),
      ),
    );
  }

  /// بناء معاينة الفيديو
  Widget _buildVideoPreview() {
    return Stack(
      children: [
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.video_library, size: 40, color: Colors.green),
              const SizedBox(height: 8),
              Text(
                'تم اختيار الفيديو',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(Icons.edit, color: AssetsColors.kWhite, size: 16),
          ),
        ),
      ],
    );
  }

  /// بناء العنصر النائب للفيديو
  Widget _buildVideoPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.video_call, size: 40, color: AssetsColors.primary),
        const SizedBox(height: 8),
        Text(
          'اضغط لاختيار فيديو',
          style: TextStyles.of(context).bodyMedium(
            fontSize: 16,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'فيديو تعليمي للدرس (اختياري)',
          style: TextStyles.of(context).bodySmall(
            fontSize: 12,
            fontFamily: AssetsFonts.cairo,
            color: AssetsColors.kGrey70,
          ),
        ),
      ],
    );
  }

  /// بناء قسم المحتوى الإضافي
  Widget _buildAdditionalContentSection() {
    return AuthField(
      controller: _additionalContentController,
      labelText: 'محتوى إضافي (اختياري)',
      prefixIcon: Icons.note_add,
      keyboardType: TextInputType.multiline,
      maxLines: 4,
      validator: AdminValidation.validateAdditionalContent,
    );
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child:
          _isLoading
              ? const Center(child: CustomLoadingAnimation())
              : PrimaryButton(
                text: isEditMode ? 'تحديث الدرس' : 'إضافة الدرس',
                onTap: _submitForm,
              ),
    );
  }

  /// اختيار فيديو
  Future<void> _pickVideo() async {
    try {
      final XFile? video = await _picker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        setState(() {
          _selectedVideo = File(video.path);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء اختيار الفيديو: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// معالجة تغييرات الحالة
  void _handleStateChanges(BuildContext context, AdminUsersState state) {
    if (state is AdminLessonAdding || state is AdminLessonUpdating) {
      setState(() {
        _isLoading = true;
      });
    } else if (state is AdminLessonAdded) {
      setState(() {
        _isLoading = false;
      });
      _showSuccessMessage('تم إضافة الدرس بنجاح');
      Navigator.pop(context);
    } else if (state is AdminLessonUpdated) {
      setState(() {
        _isLoading = false;
      });
      _showSuccessMessage('تم تحديث الدرس بنجاح');
      Navigator.pop(context);
    } else if (state is AdminLessonsError) {
      setState(() {
        _isLoading = false;
      });
      _showErrorMessage(state.message);
    }
  }

  /// إرسال النموذج
  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      final duration = int.tryParse(_durationController.text) ?? 0;
      final order = int.tryParse(_orderController.text) ?? 1;

      // طباعة معلومات مفصلة للتصحيح
      LoggerService.debug('🔥 AdminLessonFormPage._submitForm() - بدء إرسال النموذج:');
      LoggerService.debug('📚 معرف الدورة: "${widget.courseId}"');
      LoggerService.debug('📝 عنوان الدرس: "${_titleController.text.trim()}"');
      LoggerService.debug('📄 وصف الدرس: "${_descriptionController.text.trim()}"');
      LoggerService.debug('⏱️ مدة الدرس: $duration');
      LoggerService.debug('🔢 ترتيب الدرس: $order');
      LoggerService.debug('🎬 فيديو محدد: ${_selectedVideo != null}');

      // التحقق من أن courseId ليس فارغ
      if (widget.courseId.isEmpty) {
        LoggerService.debug('❌ خطأ حرج: معرف الدورة فارغ في الصفحة!');
        _showErrorMessage('خطأ: معرف الدورة مفقود');
        return;
      }

      if (isEditMode) {
        LoggerService.debug('✏️ وضع التعديل - معرف الدرس: "${widget.lessonId}"');
        context.read<AdminUsersCubit>().updateLesson(
          lessonId: widget.lessonId!,
          courseId: widget.courseId,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          duration: duration,
          order: order,
          additionalContent:
              _additionalContentController.text.trim().isNotEmpty
                  ? _additionalContentController.text.trim()
                  : null,
          videoFile: _selectedVideo,
        );
      } else {
        LoggerService.debug('➕ وضع الإضافة - إرسال البيانات إلى AdminUsersCubit');
        context.read<AdminUsersCubit>().addLesson(
          courseId: widget.courseId,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          duration: duration,
          order: order,
          additionalContent:
              _additionalContentController.text.trim().isNotEmpty
                  ? _additionalContentController.text.trim()
                  : null,
          videoFile: _selectedVideo,
        );
      }
    } else {
      LoggerService.debug('❌ فشل في التحقق من صحة النموذج');
    }
  }

  /// عرض رسالة نجاح
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}

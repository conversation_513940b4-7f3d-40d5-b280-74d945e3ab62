import 'package:agriculture/imports.dart';

import 'package:flutter/services.dart';


/// نموذج إدخال رقم الهاتف
///
/// يعرض حقل إدخال رقم الهاتف مع قائمة رموز الدول
class PhoneInputForm extends StatelessWidget {
  const PhoneInputForm({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PhoneAuthCubit>();

    return BlocBuilder<PhoneAuthCubit, PhoneAuthState>(
      buildWhen: (previous, current) =>
          current is PhoneAuthPhoneValidationChanged ||
          current is PhoneAuthCountryCodeChanged,
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // قائمة رموز الدول
              Container(
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Theme.of(context).primaryColor.withAlpha(26),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: cubit.selectedCountryCode,
                    icon: Icon(Icons.arrow_drop_down,
                        color: Theme.of(context).primaryColor),
                    iconSize: 24,
                    elevation: 16,
                    dropdownColor: Colors.grey.shade100,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily: AssetsFonts.messiri,
                    ),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        cubit.changeCountryCode(newValue);
                      }
                    },
                    items: cubit.countryCodes
                        .map<DropdownMenuItem<String>>((String code) {
                      return DropdownMenuItem<String>(
                        value: code,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Text(code),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
              const SizedBox(width: 10),

              // حقل إدخال الرقم
              Expanded(
                child: TextFormField(
                  controller: cubit.phoneController,
                  keyboardType: TextInputType.phone,
                  validator: cubit.validateYemeniPhone,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: AssetsFonts.messiri,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(9),
                  ],
                  decoration: InputDecoration(
                    hintText: '7XXXXXXXX',
                    hintStyle: TextStyles.of(context).bodyMedium().copyWith(
                          color: Colors.grey.shade500,
                          fontFamily: AssetsFonts.messiri,
                        ),
                    errorText: cubit.phoneError,
                    errorStyle: TextStyle(
                      color: Colors.red,
                      fontFamily: AssetsFonts.sultan,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 15),
                    prefixIcon: Icon(
                      Icons.phone_android,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  onFieldSubmitted: (_) {
                    if (cubit.phoneFormKey.currentState!.validate()) {
                      cubit.sendVerificationCode();
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

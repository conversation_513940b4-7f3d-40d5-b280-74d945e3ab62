

import '../../../core/constants/weather/index.dart';
import '../../../imports.dart';

/// ويدجت حالة البيانات الفارغة للطقس
///
/// يعرض رسائل موحدة عندما لا توجد بيانات متاحة
/// مع إمكانية تخصيص الرسالة والإجراءات
class WeatherEmptyWidget extends StatelessWidget {
  /// رسالة البيانات الفارغة
  final String message;

  /// دالة إعادة التحميل
  final VoidCallback? onRefresh;

  /// نوع البيانات الفارغة
  final WeatherEmptyType emptyType;

  /// لون الخلفية
  final Color? backgroundColor;

  /// لون النص
  final Color? textColor;

  /// إنشاء ويدجت البيانات الفارغة
  const WeatherEmptyWidget({
    super.key,
    required this.message,
    this.onRefresh,
    this.emptyType = WeatherEmptyType.general,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: backgroundColor,
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(WeatherDimensions.extraLargePadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة البيانات الفارغة
              _buildEmptyIcon(),
              SizedBox(height: WeatherDimensions.largeMargin),

              // عنوان البيانات الفارغة
              _buildEmptyTitle(context),
              SizedBox(height: WeatherDimensions.mediumMargin),

              // رسالة البيانات الفارغة
              _buildEmptyMessage(context),
              SizedBox(height: WeatherDimensions.extraLargeMargin),

              // زر إعادة التحميل
              if (onRefresh != null) _buildRefreshButton(context),

              // اقتراحات إضافية
              if (emptyType != WeatherEmptyType.general) ...[
                SizedBox(height: WeatherDimensions.largeMargin),
                _buildSuggestions(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة البيانات الفارغة
  Widget _buildEmptyIcon() {
    IconData iconData;
    Color iconColor;

    switch (emptyType) {
      case WeatherEmptyType.noLocation:
        iconData = Icons.location_searching;
        iconColor = WeatherColors.warningColor;
        break;
      case WeatherEmptyType.noForecast:
        iconData = Icons.cloud_queue;
        iconColor = WeatherColors.infoColor;
        break;
      case WeatherEmptyType.noHourlyData:
        iconData = Icons.schedule;
        iconColor = WeatherColors.infoColor;
        break;
      case WeatherEmptyType.noWeeklyData:
        iconData = Icons.calendar_today;
        iconColor = WeatherColors.infoColor;
        break;
      case WeatherEmptyType.general:
        iconData = Icons.cloud_off;
        iconColor = WeatherColors.lightText;
        break;
    }

    return Icon(
      iconData,
      size: WeatherDimensions.weatherIconSize,
      color: iconColor,
    );
  }

  /// بناء عنوان البيانات الفارغة
  Widget _buildEmptyTitle(BuildContext context) {
    String title;

    switch (emptyType) {
      case WeatherEmptyType.noLocation:
        title = 'لم يتم تحديد الموقع';
        break;
      case WeatherEmptyType.noForecast:
        title = 'لا توجد توقعات متاحة';
        break;
      case WeatherEmptyType.noHourlyData:
        title = 'لا توجد بيانات بالساعة';
        break;
      case WeatherEmptyType.noWeeklyData:
        title = 'لا توجد بيانات أسبوعية';
        break;
      case WeatherEmptyType.general:
        title = 'لا توجد بيانات';
        break;
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: WeatherDimensions.largeTitleFontSize,
        fontFamily: AssetsFonts.cairo,
        fontWeight: FontWeight.bold,
        color: textColor ?? WeatherColors.primaryText,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء رسالة البيانات الفارغة
  Widget _buildEmptyMessage(BuildContext context) {
    return Text(
      message,
      style: TextStyle(
        fontSize: WeatherDimensions.bodyFontSize,
        fontFamily: AssetsFonts.cairo,
        color: (textColor ?? WeatherColors.primaryText).withValues(alpha: 0.7),
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء زر إعادة التحميل
  Widget _buildRefreshButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onRefresh,
      icon: Icon(Icons.refresh, size: WeatherDimensions.mediumIconSize),
      label: Text(
        WeatherStrings.refreshButton,
        style: TextStyle(
          fontSize: WeatherDimensions.bodyFontSize,
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: WeatherColors.primaryBackground,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: WeatherDimensions.extraLargePadding,
          vertical: WeatherDimensions.mediumPadding,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            WeatherDimensions.buttonBorderRadius,
          ),
        ),
        elevation: 2,
      ),
    );
  }

  /// بناء الاقتراحات
  Widget _buildSuggestions(BuildContext context) {
    String suggestions;

    switch (emptyType) {
      case WeatherEmptyType.noLocation:
        suggestions =
            '• تأكد من تشغيل خدمات الموقع\n• امنح التطبيق إذن الوصول للموقع\n• جرب البحث عن مدينة يدوياً';
        break;
      case WeatherEmptyType.noForecast:
        suggestions =
            '• تحقق من اتصالك بالإنترنت\n• جرب إعادة تحميل البيانات\n• تأكد من صحة الموقع';
        break;
      case WeatherEmptyType.noHourlyData:
        suggestions =
            '• قد تكون البيانات غير متوفرة لهذا الموقع\n• جرب موقعاً آخر\n• تحقق من اتصالك بالإنترنت';
        break;
      case WeatherEmptyType.noWeeklyData:
        suggestions =
            '• قد تكون التوقعات طويلة المدى غير متوفرة\n• جرب موقعاً آخر\n• تحقق من اتصالك بالإنترنت';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      decoration: BoxDecoration(
        color: WeatherColors.infoColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        border: Border.all(
          color: WeatherColors.infoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tips_and_updates,
                size: WeatherDimensions.mediumIconSize,
                color: WeatherColors.infoColor,
              ),
              SizedBox(width: WeatherDimensions.smallMargin),
              Text(
                'اقتراحات',
                style: TextStyle(
                  fontSize: WeatherDimensions.subtitleFontSize,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.w600,
                  color: WeatherColors.infoColor,
                ),
              ),
            ],
          ),
          SizedBox(height: WeatherDimensions.smallMargin),
          Text(
            suggestions,
            style: TextStyle(
              fontSize: WeatherDimensions.smallFontSize,
              fontFamily: AssetsFonts.cairo,
              color: (textColor ?? WeatherColors.primaryText).withValues(
                alpha: 0.7,
              ),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

/// أنواع البيانات الفارغة المختلفة
enum WeatherEmptyType {
  /// بيانات فارغة عامة
  general,

  /// لا يوجد موقع محدد
  noLocation,

  /// لا توجد توقعات
  noForecast,

  /// لا توجد بيانات بالساعة
  noHourlyData,

  /// لا توجد بيانات أسبوعية
  noWeeklyData,
}

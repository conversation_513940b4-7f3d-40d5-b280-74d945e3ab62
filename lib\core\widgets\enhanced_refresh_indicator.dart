import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// مؤشر تحديث محسن مع رسوم متحركة وتفاعل أفضل
///
/// يوفر تجربة Pull-to-refresh محسنة مع haptic feedback
/// وفق التفضيلات الـ18 مع عدم التكرار
class EnhancedRefreshIndicator extends StatefulWidget {
  /// الويدجت الفرعي
  final Widget child;

  /// دالة التحديث
  final Future<void> Function() onRefresh;

  /// المسافة المطلوبة لتفعيل التحديث
  final double displacement;

  /// لون المؤشر
  final Color? color;

  /// لون الخلفية
  final Color? backgroundColor;

  /// حجم المؤشر
  final double strokeWidth;

  /// تفعيل Haptic Feedback
  final bool enableHapticFeedback;

  /// رسالة التحديث
  final String? refreshMessage;

  /// نوع الرسوم المتحركة
  final RefreshAnimationType animationType;

  const EnhancedRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 40.0,
    this.color,
    this.backgroundColor,
    this.strokeWidth = 2.0,
    this.enableHapticFeedback = true,
    this.refreshMessage,
    this.animationType = RefreshAnimationType.material,
  });

  @override
  State<EnhancedRefreshIndicator> createState() => _EnhancedRefreshIndicatorState();
}

class _EnhancedRefreshIndicatorState extends State<EnhancedRefreshIndicator> {
  bool _isRefreshing = false;
  bool _hapticTriggered = false;

  /// معالج التحديث
  Future<void> _handleRefresh() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    // تفعيل Haptic Feedback
    if (widget.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }

    try {
      await widget.onRefresh();
    } finally {
      setState(() {
        _isRefreshing = false;
        _hapticTriggered = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _handleRefresh,
      displacement: widget.displacement,
      color: widget.color,
      backgroundColor: widget.backgroundColor,
      strokeWidth: widget.strokeWidth,
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          // تفعيل Haptic Feedback عند الوصول لنقطة التحديث
          if (widget.enableHapticFeedback &&
              !_hapticTriggered &&
              notification is ScrollUpdateNotification &&
              notification.metrics.extentBefore == 0 &&
              notification.metrics.pixels < -widget.displacement) {
            _hapticTriggered = true;
            HapticFeedback.lightImpact();
          }

          // إعادة تعيين Haptic عند العودة
          if (_hapticTriggered &&
              notification is ScrollUpdateNotification &&
              notification.metrics.pixels >= 0) {
            _hapticTriggered = false;
          }

          return false;
        },
        child: widget.child,
      ),
    );
  }
}

/// أنواع الرسوم المتحركة للتحديث
enum RefreshAnimationType {
  material,
  scale,
  rotation,
  bounce,
}

/// ويدجت Pull-to-refresh مخصص للقوائم
class CustomPullToRefresh extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final String? refreshText;
  final Color? primaryColor;
  final bool enableHaptic;

  const CustomPullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.refreshText,
    this.primaryColor,
    this.enableHaptic = true,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedRefreshIndicator(
      onRefresh: onRefresh,
      color: primaryColor ?? Theme.of(context).primaryColor,
      enableHapticFeedback: enableHaptic,
      animationType: RefreshAnimationType.scale,
      child: child,
    );
  }
}

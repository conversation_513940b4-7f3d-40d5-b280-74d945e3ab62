import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../core/utils/debouncer.dart';

/// شريط بحث ذكي محسن للخبراء الزراعيين
///
/// يوفر بحث متقدم مع اقتراحات ذكية وفلترة سريعة
/// مطبق وفق التفضيلات الـ18 مع عدم التكرار
class SmartSearchBarWidget extends StatefulWidget {
  /// النص الأولي للبحث
  final String initialValue;

  /// دالة استدعاء عند تغيير النص
  final Function(String)? onSearchChanged;

  /// دالة استدعاء عند إرسال البحث
  final Function(String)? onSearchSubmitted;

  /// دالة استدعاء عند النقر على الفلاتر
  final VoidCallback? onFiltersPressed;

  /// دالة استدعاء عند النقر على المسح
  final VoidCallback? onClearPressed;

  /// دالة استدعاء عند اختيار اقتراح
  final Function(String)? onSuggestionSelected;

  /// قائمة الاقتراحات المخصصة
  final List<String>? customSuggestions;

  /// هل تظهر الاقتراحات
  final bool showSuggestions;

  /// هل تظهر زر الفلاتر
  final bool showFiltersButton;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  /// نص التلميح
  final String? hintText;

  /// عدد الفلاتر المطبقة
  final int appliedFiltersCount;

  const SmartSearchBarWidget({
    super.key,
    this.initialValue = '',
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.onFiltersPressed,
    this.onClearPressed,
    this.onSuggestionSelected,
    this.customSuggestions,
    this.showSuggestions = true,
    this.showFiltersButton = true,
    this.showAnimations = true,
    this.hintText,
    this.appliedFiltersCount = 0,
  });

  @override
  State<SmartSearchBarWidget> createState() => _SmartSearchBarWidgetState();
}

class _SmartSearchBarWidgetState extends State<SmartSearchBarWidget>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  late SearchDebouncer _searchDebouncer;

  bool _showSuggestions = false;
  List<String> _filteredSuggestions = [];

  /// قائمة الاقتراحات الافتراضية
  static const List<String> _defaultSuggestions = [
    'خبير محاصيل',
    'خبير ري',
    'خبير آفات',
    'خبير تربة',
    'خبير بساتين',
    'خبير خضروات',
    'خبير نخيل',
    'خبير قطن',
    'خبير قمح',
    'خبير ذرة',
  ];

  @override
  void initState() {
    super.initState();

    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    _searchDebouncer = SearchDebouncer(
      delay: const Duration(milliseconds: 300),
      minLength: 1,
    );

    if (widget.showAnimations) {
      _setupAnimations();
    }

    _setupListeners();
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _colorAnimation = ColorTween(
      begin: AppColors.surface,
      end: Colors.white,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  /// إعداد المستمعين
  void _setupListeners() {
    _controller.addListener(_onTextChanged);

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _animationController.forward();
        if (widget.showSuggestions) {
          _updateSuggestions(_controller.text);
        }
      } else {
        _animationController.reverse();
        setState(() {
          _showSuggestions = false;
        });
      }
    });
  }

  /// معالج تغيير النص مع Debouncing
  void _onTextChanged() {
    final text = _controller.text;

    // استخدام Debouncing للبحث
    _searchDebouncer.search(text, (query) {
      widget.onSearchChanged?.call(query);
    });

    // تحديث الاقتراحات فوراً (بدون debouncing)
    if (widget.showSuggestions && _focusNode.hasFocus) {
      _updateSuggestions(text);
    }
  }

  /// تحديث الاقتراحات
  void _updateSuggestions(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredSuggestions = widget.customSuggestions ?? _defaultSuggestions;
        _showSuggestions = _filteredSuggestions.isNotEmpty;
      });
      return;
    }

    final suggestions = widget.customSuggestions ?? _defaultSuggestions;
    final filtered =
        suggestions
            .where(
              (suggestion) =>
                  suggestion.toLowerCase().contains(query.toLowerCase()),
            )
            .take(5)
            .toList();

    setState(() {
      _filteredSuggestions = filtered;
      _showSuggestions = filtered.isNotEmpty;
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _searchDebouncer.dispose();
    if (widget.showAnimations) {
      _animationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        if (_showSuggestions && widget.showSuggestions)
          _buildSuggestionsOverlay(),
      ],
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    Widget searchBar = Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color:
            widget.showAnimations ? _colorAnimation.value : AppColors.surface,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildSearchIcon(),
          Expanded(child: _buildTextField()),
          if (_controller.text.isNotEmpty) _buildClearButton(),
          if (widget.showFiltersButton) _buildFiltersButton(),
        ],
      ),
    );

    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: searchBar,
          );
        },
      );
    }

    return searchBar;
  }

  /// بناء أيقونة البحث
  Widget _buildSearchIcon() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Icon(Icons.search, color: AppColors.primary, size: 24),
    );
  }

  /// بناء حقل النص
  Widget _buildTextField() {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      style: AppTextStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
      decoration: InputDecoration(
        hintText: widget.hintText ?? 'ابحث عن خبير زراعي...',
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(vertical: 16),
      ),
      textInputAction: TextInputAction.search,
      onSubmitted: (value) {
        widget.onSearchSubmitted?.call(value);
        _focusNode.unfocus();
      },
    );
  }

  /// بناء زر المسح
  Widget _buildClearButton() {
    return GestureDetector(
      onTap: () {
        _controller.clear();
        widget.onClearPressed?.call();
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Icon(Icons.clear, color: AppColors.textSecondary, size: 20),
      ),
    );
  }

  /// بناء زر الفلاتر
  Widget _buildFiltersButton() {
    return GestureDetector(
      onTap: () {
        widget.onFiltersPressed?.call();
        HapticFeedback.lightImpact();
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              widget.appliedFiltersCount > 0
                  ? AppColors.primary
                  : AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Stack(
          children: [
            Icon(
              Icons.tune,
              color:
                  widget.appliedFiltersCount > 0
                      ? Colors.white
                      : AppColors.primary,
              size: 20,
            ),
            if (widget.appliedFiltersCount > 0)
              Positioned(
                right: -2,
                top: -2,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '${widget.appliedFiltersCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء تراكب الاقتراحات
  Widget _buildSuggestionsOverlay() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            _filteredSuggestions.map((suggestion) {
              return _buildSuggestionItem(suggestion);
            }).toList(),
      ),
    );
  }

  /// بناء عنصر اقتراح
  Widget _buildSuggestionItem(String suggestion) {
    return InkWell(
      onTap: () {
        _controller.text = suggestion;
        widget.onSuggestionSelected?.call(suggestion);
        widget.onSearchSubmitted?.call(suggestion);
        _focusNode.unfocus();
        HapticFeedback.selectionClick();
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(Icons.search, color: AppColors.textSecondary, size: 18),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                suggestion,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ),
            Icon(Icons.north_west, color: AppColors.textSecondary, size: 16),
          ],
        ),
      ),
    );
  }
}

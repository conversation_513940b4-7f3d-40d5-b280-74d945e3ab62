import '../../../data/models/experts/index.dart';
import '../../repositories/experts_repository_interface.dart';

/// حالة استخدام لإنشاء طلب استشارة جديد
///
/// توفر هذه الحالة وظيفة إنشاء طلب استشارة مع التحقق من صحة البيانات
class CreateConsultation {
  /// مستودع الخبراء
  final ExpertsRepositoryInterface _repository;

  /// إنشاء حالة استخدام إنشاء الاستشارة
  ///
  /// المعلمات:
  /// - [repository]: مستودع الخبراء
  CreateConsultation(this._repository);

  /// إنشاء طلب استشارة جديد
  ///
  /// المعلمات:
  /// - [farmerId]: معرف المزارع
  /// - [farmerName]: اسم المزارع
  /// - [expertId]: معرف الخبير
  /// - [expertName]: اسم الخبير
  /// - [title]: عنوان الاستشارة
  /// - [description]: وصف المشكلة أو السؤال
  /// - [category]: فئة الاستشارة
  /// - [priority]: أولوية الاستشارة (افتراضي: عادية)
  /// - [images]: قائمة روابط الصور المرفقة (اختياري)
  ///
  /// Returns معرف الاستشارة الجديدة
  /// Throws [ArgumentError] إذا كانت البيانات غير صحيحة
  /// Throws [Exception] إذا فشل في إنشاء الاستشارة
  Future<String> call({
    required String farmerId,
    required String farmerName,
    required String expertId,
    required String expertName,
    required String title,
    required String description,
    required String category,
    ConsultationPriority priority = ConsultationPriority.normal,
    List<String> images = const [],
  }) async {
    // التحقق من صحة البيانات المطلوبة
    _validateInput(
      farmerId: farmerId,
      farmerName: farmerName,
      expertId: expertId,
      expertName: expertName,
      title: title,
      description: description,
      category: category,
    );

    // التحقق من وجود الخبير وتوفره
    await _validateExpert(expertId);

    // إنشاء نموذج الاستشارة
    final consultation = ConsultationModel(
      id: '', // سيتم تعيينه في المستودع
      farmerId: farmerId,
      farmerName: farmerName,
      expertId: expertId,
      expertName: expertName,
      title: title.trim(),
      description: description.trim(),
      images: images,
      category: category,
      status: ConsultationStatus.pending,
      priority: priority,
      createdAt: DateTime.now(),
    );

    // إنشاء الاستشارة في المستودع
    try {
      final consultationId = await _repository.createConsultation(consultation);

      if (consultationId.isEmpty) {
        throw Exception('فشل في إنشاء الاستشارة');
      }

      return consultationId;
    } catch (e) {
      throw Exception('حدث خطأ أثناء إنشاء الاستشارة: ${e.toString()}');
    }
  }

  /// إنشاء استشارة سريعة بأقل البيانات المطلوبة
  ///
  /// المعلمات:
  /// - [farmerId]: معرف المزارع
  /// - [farmerName]: اسم المزارع
  /// - [expertId]: معرف الخبير
  /// - [title]: عنوان الاستشارة
  /// - [description]: وصف المشكلة
  ///
  /// Returns معرف الاستشارة الجديدة
  Future<String> createQuick({
    required String farmerId,
    required String farmerName,
    required String expertId,
    required String title,
    required String description,
  }) async {
    // الحصول على بيانات الخبير
    final expert = await _repository.getExpertById(expertId);
    if (expert == null) {
      throw ArgumentError('الخبير المحدد غير موجود');
    }

    return await call(
      farmerId: farmerId,
      farmerName: farmerName,
      expertId: expertId,
      expertName: expert.name,
      title: title,
      description: description,
      category: 'عام', // فئة افتراضية
      priority: ConsultationPriority.normal,
    );
  }

  /// إنشاء استشارة طارئة
  ///
  /// المعلمات مشابهة لـ call() لكن مع أولوية طارئة
  Future<String> createEmergency({
    required String farmerId,
    required String farmerName,
    required String expertId,
    required String expertName,
    required String title,
    required String description,
    required String category,
    List<String> images = const [],
  }) async {
    return await call(
      farmerId: farmerId,
      farmerName: farmerName,
      expertId: expertId,
      expertName: expertName,
      title: title,
      description: description,
      category: category,
      priority: ConsultationPriority.emergency,
      images: images,
    );
  }

  /// التحقق من صحة البيانات المدخلة
  void _validateInput({
    required String farmerId,
    required String farmerName,
    required String expertId,
    required String expertName,
    required String title,
    required String description,
    required String category,
  }) {
    // التحقق من معرف المزارع
    if (farmerId.trim().isEmpty) {
      throw ArgumentError('معرف المزارع مطلوب');
    }

    // التحقق من اسم المزارع
    if (farmerName.trim().isEmpty) {
      throw ArgumentError('اسم المزارع مطلوب');
    }

    // التحقق من معرف الخبير
    if (expertId.trim().isEmpty) {
      throw ArgumentError('معرف الخبير مطلوب');
    }

    // التحقق من اسم الخبير
    if (expertName.trim().isEmpty) {
      throw ArgumentError('اسم الخبير مطلوب');
    }

    // التحقق من عنوان الاستشارة
    if (title.trim().isEmpty) {
      throw ArgumentError('عنوان الاستشارة مطلوب');
    }

    if (title.trim().length < 5) {
      throw ArgumentError('عنوان الاستشارة يجب أن يكون 5 أحرف على الأقل');
    }

    if (title.trim().length > 100) {
      throw ArgumentError('عنوان الاستشارة يجب أن يكون أقل من 100 حرف');
    }

    // التحقق من وصف المشكلة
    if (description.trim().isEmpty) {
      throw ArgumentError('وصف المشكلة مطلوب');
    }

    if (description.trim().length < 10) {
      throw ArgumentError('وصف المشكلة يجب أن يكون 10 أحرف على الأقل');
    }

    if (description.trim().length > 1000) {
      throw ArgumentError('وصف المشكلة يجب أن يكون أقل من 1000 حرف');
    }

    // التحقق من فئة الاستشارة
    if (category.trim().isEmpty) {
      throw ArgumentError('فئة الاستشارة مطلوبة');
    }
  }

  /// التحقق من وجود الخبير وتوفره
  Future<void> _validateExpert(String expertId) async {
    final expert = await _repository.getExpertById(expertId);

    if (expert == null) {
      throw ArgumentError('الخبير المحدد غير موجود');
    }

    if (!expert.isAvailable) {
      throw ArgumentError('الخبير المحدد غير متاح حالياً');
    }
  }

  /// الحصول على قائمة الفئات المتاحة للاستشارات
  List<String> getAvailableCategories() {
    return [
      'محاصيل حقلية',
      'خضروات',
      'فواكه',
      'ري وتسميد',
      'آفات وأمراض',
      'زراعة عضوية',
      'تربة ومناخ',
      'تربية النحل',
      'أخرى',
    ];
  }

  /// الحصول على قائمة الأولويات المتاحة
  List<ConsultationPriority> getAvailablePriorities() {
    return ConsultationPriority.values;
  }

  /// تحويل أولوية الاستشارة إلى نص عربي
  String priorityToArabic(ConsultationPriority priority) {
    switch (priority) {
      case ConsultationPriority.normal:
        return 'عادية';
      case ConsultationPriority.urgent:
        return 'عاجلة';
      case ConsultationPriority.emergency:
        return 'طارئة';
    }
  }
}

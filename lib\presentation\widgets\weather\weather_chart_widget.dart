import 'dart:math' as math;


import '../../../core/constants/weather/index.dart';
import '../../../imports.dart';

/// ويدجت الرسوم البيانية للطقس
///
/// يعرض رسوم بيانية تفاعلية لتغيرات الطقس
class WeatherChartWidget extends StatelessWidget {
  /// نوع الرسم البياني
  final WeatherChartType chartType;

  /// بيانات التوقعات بالساعة
  final HourlyModel? hourlyData;

  /// بيانات التوقعات الأسبوعية
  final WeeklyModel? weeklyData;

  /// تفعيل التفاعل
  final bool enableInteraction;

  /// إنشاء ويدجت الرسم البياني
  const WeatherChartWidget({
    super.key,
    required this.chartType,
    this.hourlyData,
    this.weeklyData,
    this.enableInteraction = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: WeatherDimensions.chartHeight,
      margin: EdgeInsets.symmetric(
        horizontal: WeatherDimensions.mediumMargin,
        vertical: WeatherDimensions.smallMargin,
      ),
      decoration: BoxDecoration(
        color: WeatherColors.cardBackground,
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: WeatherColors.shadowColor,
            blurRadius: WeatherDimensions.shadowBlurRadius,
            spreadRadius: WeatherDimensions.shadowSpreadRadius,
            offset: Offset(
              WeatherDimensions.shadowOffsetX,
              WeatherDimensions.shadowOffsetY,
            ),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(WeatherDimensions.chartPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الرسم البياني
            _buildChartTitle(context),
            SizedBox(height: WeatherDimensions.mediumMargin),

            // الرسم البياني
            Expanded(child: _buildChart(context)),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان الرسم البياني
  Widget _buildChartTitle(BuildContext context) {
    String title;
    IconData icon;

    switch (chartType) {
      case WeatherChartType.temperature:
        title = 'تغيرات درجة الحرارة';
        icon = Icons.thermostat;
        break;
      case WeatherChartType.humidity:
        title = 'تغيرات الرطوبة';
        icon = Icons.water_drop;
        break;
      case WeatherChartType.pressure:
        title = 'تغيرات الضغط الجوي';
        icon = Icons.speed;
        break;
      case WeatherChartType.windSpeed:
        title = 'تغيرات سرعة الرياح';
        icon = Icons.air;
        break;
    }

    return Row(
      children: [
        Icon(
          icon,
          size: WeatherDimensions.mediumIconSize,
          color: WeatherColors.primaryBackground,
        ),
        SizedBox(width: WeatherDimensions.smallMargin),
        Text(
          title,
          style: TextStyle(
            fontSize: WeatherDimensions.subtitleFontSize,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: WeatherColors.darkText,
          ),
        ),
      ],
    );
  }

  /// بناء الرسم البياني
  Widget _buildChart(BuildContext context) {
    switch (chartType) {
      case WeatherChartType.temperature:
        return _buildTemperatureChart();
      case WeatherChartType.humidity:
        return _buildHumidityChart();
      case WeatherChartType.pressure:
        return _buildPressureChart();
      case WeatherChartType.windSpeed:
        return _buildWindSpeedChart();
    }
  }

  /// بناء رسم بياني تفاعلي لدرجة الحرارة
  Widget _buildTemperatureChart() {
    if (hourlyData?.listweather == null || hourlyData!.listweather!.isEmpty) {
      return _buildNoDataWidget();
    }

    return InteractiveWeatherChart(
      data: hourlyData!.listweather!.take(12).toList(),
      chartType: chartType,
      enableInteraction: enableInteraction,
    );
  }

  /// بناء رسم بياني للرطوبة
  Widget _buildHumidityChart() {
    if (hourlyData?.listweather == null || hourlyData!.listweather!.isEmpty) {
      return _buildNoDataWidget();
    }

    return CustomPaint(
      size: Size.infinite,
      painter: HumidityChartPainter(
        data: hourlyData!.listweather!.take(12).toList(),
        enableInteraction: enableInteraction,
      ),
    );
  }

  /// بناء رسم بياني للضغط الجوي
  Widget _buildPressureChart() {
    if (hourlyData?.listweather == null || hourlyData!.listweather!.isEmpty) {
      return _buildNoDataWidget();
    }

    return CustomPaint(
      size: Size.infinite,
      painter: PressureChartPainter(
        data: hourlyData!.listweather!.take(12).toList(),
        enableInteraction: enableInteraction,
      ),
    );
  }

  /// بناء رسم بياني لسرعة الرياح
  Widget _buildWindSpeedChart() {
    if (hourlyData?.listweather == null || hourlyData!.listweather!.isEmpty) {
      return _buildNoDataWidget();
    }

    return CustomPaint(
      size: Size.infinite,
      painter: WindSpeedChartPainter(
        data: hourlyData!.listweather!.take(12).toList(),
        enableInteraction: enableInteraction,
      ),
    );
  }

  /// بناء ويدجت عدم وجود بيانات
  Widget _buildNoDataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: WeatherDimensions.extraLargeIconSize,
            color: WeatherColors.lightText,
          ),
          SizedBox(height: WeatherDimensions.smallMargin),
          Text(
            WeatherStrings.noDataMessage,
            style: TextStyle(
              fontSize: WeatherDimensions.bodyFontSize,
              fontFamily: AssetsFonts.cairo,
              color: WeatherColors.lightText,
            ),
          ),
        ],
      ),
    );
  }
}

/// أنواع الرسوم البيانية
enum WeatherChartType {
  /// رسم بياني لدرجة الحرارة
  temperature,

  /// رسم بياني للرطوبة
  humidity,

  /// رسم بياني للضغط الجوي
  pressure,

  /// رسم بياني لسرعة الرياح
  windSpeed,
}

/// رسام الرسم البياني التفاعلي لدرجة الحرارة
class TemperatureChartPainter extends CustomPainter {
  final List<Listweather> data;
  final bool enableInteraction;
  final Offset? touchPoint;
  final Function(int index, double value)? onPointTap;

  TemperatureChartPainter({
    required this.data,
    this.enableInteraction = true,
    this.touchPoint,
    this.onPointTap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    // رسم الخلفية المتدرجة
    _drawBackground(canvas, size);

    // رسم خطوط الشبكة
    _drawGrid(canvas, size);

    // رسم الخط الرئيسي
    _drawMainLine(canvas, size);

    // رسم النقاط
    _drawPoints(canvas, size);

    // رسم النقطة المحددة (عند اللمس)
    if (touchPoint != null && enableInteraction) {
      _drawSelectedPoint(canvas, size);
    }

    // رسم التسميات
    _drawLabels(canvas, size);
  }

  void _drawBackground(Canvas canvas, Size size) {
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        WeatherColors.sunnyColor.withValues(alpha: 0.1),
        WeatherColors.sunnyColor.withValues(alpha: 0.05),
      ],
    );

    final paint =
        Paint()
          ..shader = gradient.createShader(
            Rect.fromLTWH(0, 0, size.width, size.height),
          );
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
  }

  void _drawGrid(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = WeatherColors.lightText.withValues(alpha: 0.2)
          ..strokeWidth = 0.5;

    // خطوط أفقية
    for (int i = 0; i <= 4; i++) {
      final y = (i / 4) * size.height;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // خطوط عمودية
    for (int i = 0; i <= 6; i++) {
      final x = (i / 6) * size.width;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  void _drawMainLine(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = WeatherColors.sunnyColor
          ..strokeWidth = WeatherDimensions.chartLineWidth
          ..style = PaintingStyle.stroke;

    final path = Path();
    final points = _calculatePoints(size);

    for (int i = 0; i < points.length; i++) {
      if (i == 0) {
        path.moveTo(points[i].dx, points[i].dy);
      } else {
        // استخدام منحنيات ناعمة
        final cp1 = Offset(
          points[i - 1].dx + (points[i].dx - points[i - 1].dx) * 0.5,
          points[i - 1].dy,
        );
        final cp2 = Offset(
          points[i - 1].dx + (points[i].dx - points[i - 1].dx) * 0.5,
          points[i].dy,
        );
        path.cubicTo(
          cp1.dx,
          cp1.dy,
          cp2.dx,
          cp2.dy,
          points[i].dx,
          points[i].dy,
        );
      }
    }

    canvas.drawPath(path, paint);
  }

  void _drawPoints(Canvas canvas, Size size) {
    final points = _calculatePoints(size);

    for (int i = 0; i < points.length; i++) {
      final point = points[i];

      // دائرة خارجية
      final outerPaint =
          Paint()
            ..color = WeatherColors.sunnyColor.withValues(alpha: 0.3)
            ..style = PaintingStyle.fill;
      canvas.drawCircle(point, 8, outerPaint);

      // دائرة داخلية
      final innerPaint =
          Paint()
            ..color = WeatherColors.sunnyColor
            ..style = PaintingStyle.fill;
      canvas.drawCircle(point, 4, innerPaint);

      // نقطة مركزية
      final centerPaint =
          Paint()
            ..color = Colors.white
            ..style = PaintingStyle.fill;
      canvas.drawCircle(point, 2, centerPaint);
    }
  }

  void _drawSelectedPoint(Canvas canvas, Size size) {
    if (touchPoint == null) return;

    final points = _calculatePoints(size);
    int nearestIndex = 0;
    double minDistance = double.infinity;

    // العثور على أقرب نقطة
    for (int i = 0; i < points.length; i++) {
      final distance = (points[i] - touchPoint!).distance;
      if (distance < minDistance) {
        minDistance = distance;
        nearestIndex = i;
      }
    }

    if (minDistance < 30) {
      // ضمن نطاق 30 بكسل
      final selectedPoint = points[nearestIndex];

      // دائرة التحديد
      final selectionPaint =
          Paint()
            ..color = WeatherColors.warningColor
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3;
      canvas.drawCircle(selectedPoint, 12, selectionPaint);

      // عرض القيمة
      final temp = data[nearestIndex].main?.temp?.round() ?? 0;
      _drawValueLabel(canvas, selectedPoint, '$temp°');

      // استدعاء callback
      if (onPointTap != null) {
        onPointTap!(nearestIndex, temp.toDouble());
      }
    }
  }

  void _drawLabels(Canvas canvas, Size size) {
    final textPainter = TextPainter(textDirection: TextDirection.rtl);

    // تسميات الوقت (أسفل)
    for (int i = 0; i < data.length; i += 2) {
      if (i < data.length) {
        final x = (i / (data.length - 1)) * size.width;
        final time = DateTime.fromMillisecondsSinceEpoch(
          (data[i].dt ?? 0) * 1000,
        );

        textPainter.text = TextSpan(
          text: '${time.hour}:00',
          style: TextStyle(
            color: WeatherColors.lightText,
            fontSize: WeatherDimensions.microFontSize,
            fontFamily: AssetsFonts.cairo,
          ),
        );

        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, size.height + 5),
        );
      }
    }
  }

  void _drawValueLabel(Canvas canvas, Offset point, String value) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: value,
        style: TextStyle(
          color: Colors.white,
          fontSize: WeatherDimensions.smallFontSize,
          fontWeight: FontWeight.bold,
          fontFamily: AssetsFonts.cairo,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();

    // خلفية التسمية
    final rect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(point.dx, point.dy - 25),
        width: textPainter.width + 12,
        height: textPainter.height + 8,
      ),
      Radius.circular(6),
    );

    final backgroundPaint =
        Paint()
          ..color = WeatherColors.warningColor
          ..style = PaintingStyle.fill;

    canvas.drawRRect(rect, backgroundPaint);

    // النص
    textPainter.paint(
      canvas,
      Offset(
        point.dx - textPainter.width / 2,
        point.dy - 25 - textPainter.height / 2,
      ),
    );
  }

  List<Offset> _calculatePoints(Size size) {
    final points = <Offset>[];

    // العثور على أدنى وأعلى قيمة للتطبيع
    double minTemp = double.infinity;
    double maxTemp = double.negativeInfinity;

    for (final item in data) {
      final temp = item.main?.temp?.toDouble() ?? 0.0;
      minTemp = math.min(minTemp, temp);
      maxTemp = math.max(maxTemp, temp);
    }

    final tempRange = maxTemp - minTemp;
    if (tempRange == 0) return points;

    for (int i = 0; i < data.length; i++) {
      final temp = data[i].main?.temp?.toDouble() ?? 0.0;
      final x = (i / (data.length - 1)) * size.width;
      final normalizedTemp = (temp - minTemp) / tempRange;
      final y =
          size.height -
          (normalizedTemp * size.height * 0.8) -
          (size.height * 0.1);

      points.add(Offset(x, y));
    }

    return points;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// ويدجت الرسم البياني التفاعلي الحقيقي
class InteractiveWeatherChart extends StatefulWidget {
  final List<Listweather> data;
  final WeatherChartType chartType;
  final bool enableInteraction;

  const InteractiveWeatherChart({
    super.key,
    required this.data,
    required this.chartType,
    this.enableInteraction = true,
  });

  @override
  State<InteractiveWeatherChart> createState() =>
      _InteractiveWeatherChartState();
}

class _InteractiveWeatherChartState extends State<InteractiveWeatherChart>
    with TickerProviderStateMixin {
  Offset? _touchPoint;
  int? _selectedIndex;
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _showTooltip = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: widget.enableInteraction ? _handleTapDown : null,
      onTapUp: widget.enableInteraction ? _handleTapUp : null,
      onPanUpdate: widget.enableInteraction ? _handlePanUpdate : null,
      onPanEnd: widget.enableInteraction ? _handlePanEnd : null,
      child: Stack(
        children: [
          // الرسم البياني الرئيسي
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(size: Size.infinite, painter: _getPainter());
            },
          ),

          // التفاصيل المنبثقة
          if (_showTooltip && _selectedIndex != null && _touchPoint != null)
            _buildTooltip(),
        ],
      ),
    );
  }

  /// معالج النقر
  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _touchPoint = details.localPosition;
      _showTooltip = true;
    });
  }

  void _handleTapUp(TapUpDetails details) {
    // الاحتفاظ بالتفاصيل لثانيتين
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showTooltip = false;
        });
      }
    });
  }

  /// معالج السحب
  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      _touchPoint = details.localPosition;
    });
  }

  void _handlePanEnd(DragEndDetails details) {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _showTooltip = false;
        });
      }
    });
  }

  /// الحصول على الرسام المناسب
  CustomPainter _getPainter() {
    switch (widget.chartType) {
      case WeatherChartType.temperature:
        return TemperatureChartPainter(
          data: widget.data,
          enableInteraction: widget.enableInteraction,
          touchPoint: _touchPoint,
          onPointTap: _onPointSelected,
        );
      case WeatherChartType.humidity:
        return HumidityChartPainter(
          data: widget.data,
          enableInteraction: widget.enableInteraction,
        );
      case WeatherChartType.pressure:
        return PressureChartPainter(
          data: widget.data,
          enableInteraction: widget.enableInteraction,
        );
      case WeatherChartType.windSpeed:
        return WindSpeedChartPainter(
          data: widget.data,
          enableInteraction: widget.enableInteraction,
        );
    }
  }

  /// معالج تحديد النقطة
  void _onPointSelected(int index, double value) {
    setState(() {
      _selectedIndex = index;
    });
  }

  /// بناء التفاصيل المنبثقة
  Widget _buildTooltip() {
    if (_selectedIndex == null || _selectedIndex! >= widget.data.length) {
      return const SizedBox.shrink();
    }

    final data = widget.data[_selectedIndex!];
    final time = DateTime.fromMillisecondsSinceEpoch((data.dt ?? 0) * 1000);

    String value = '';
    String unit = '';
    IconData icon = Icons.thermostat;

    switch (widget.chartType) {
      case WeatherChartType.temperature:
        value = '${data.main?.temp?.round() ?? 0}';
        unit = '°م';
        icon = Icons.thermostat;
        break;
      case WeatherChartType.humidity:
        value = '${data.main?.humidity ?? 0}';
        unit = '%';
        icon = Icons.water_drop;
        break;
      case WeatherChartType.pressure:
        value = '${data.main?.pressure ?? 0}';
        unit = ' هيكتوباسكال';
        icon = Icons.speed;
        break;
      case WeatherChartType.windSpeed:
        value = '${data.wind?.speed?.toStringAsFixed(1) ?? '0'}';
        unit = ' م/ث';
        icon = Icons.air;
        break;
    }

    return Positioned(
      left: _touchPoint!.dx - 75,
      top: _touchPoint!.dy - 100,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        color: WeatherColors.primaryBackground,
        child: Container(
          padding: EdgeInsets.all(WeatherDimensions.mediumPadding),
          constraints: const BoxConstraints(minWidth: 150),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // الوقت
              Text(
                '${time.hour}:00',
                style: TextStyle(
                  fontSize: WeatherDimensions.smallFontSize,
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.white.withValues(alpha: 0.8),
                ),
              ),
              SizedBox(height: WeatherDimensions.smallMargin),

              // القيمة والأيقونة
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    icon,
                    color: Colors.white,
                    size: WeatherDimensions.mediumIconSize,
                  ),
                  SizedBox(width: WeatherDimensions.smallMargin),
                  Text(
                    '$value$unit',
                    style: TextStyle(
                      fontSize: WeatherDimensions.titleFontSize,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),

              // معلومات إضافية
              if (widget.chartType == WeatherChartType.temperature &&
                  data.main?.feelsLike != null) ...[
                SizedBox(height: WeatherDimensions.smallMargin),
                Text(
                  'تشعر وكأنها ${data.main!.feelsLike!.round()}°م',
                  style: TextStyle(
                    fontSize: WeatherDimensions.microFontSize,
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// رسام الرسم البياني للرطوبة
class HumidityChartPainter extends CustomPainter {
  final List<Listweather> data;
  final bool enableInteraction;

  HumidityChartPainter({required this.data, this.enableInteraction = true});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint =
        Paint()
          ..color = WeatherColors.rainyColor
          ..strokeWidth = WeatherDimensions.chartLineWidth
          ..style = PaintingStyle.stroke;

    final path = Path();

    for (int i = 0; i < data.length; i++) {
      final humidity = data[i].main?.humidity?.toDouble() ?? 0.0;
      final x = (i / (data.length - 1)) * size.width;
      final y = size.height - (humidity / 100) * size.height;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// رسام الرسم البياني للضغط الجوي
class PressureChartPainter extends CustomPainter {
  final List<Listweather> data;
  final bool enableInteraction;

  PressureChartPainter({required this.data, this.enableInteraction = true});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint =
        Paint()
          ..color = WeatherColors.cloudyColor
          ..strokeWidth = WeatherDimensions.chartLineWidth
          ..style = PaintingStyle.stroke;

    final path = Path();

    for (int i = 0; i < data.length; i++) {
      final pressure = data[i].main?.pressure?.toDouble() ?? 1013.0;
      final x = (i / (data.length - 1)) * size.width;
      final y =
          size.height - ((pressure - 980) / 60) * size.height; // تطبيع القيم

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// رسام الرسم البياني لسرعة الرياح
class WindSpeedChartPainter extends CustomPainter {
  final List<Listweather> data;
  final bool enableInteraction;

  WindSpeedChartPainter({required this.data, this.enableInteraction = true});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint =
        Paint()
          ..color = WeatherColors.stormyColor
          ..strokeWidth = WeatherDimensions.chartLineWidth
          ..style = PaintingStyle.stroke;

    final path = Path();

    for (int i = 0; i < data.length; i++) {
      final windSpeed = data[i].wind?.speed?.toDouble() ?? 0.0;
      final x = (i / (data.length - 1)) * size.width;
      final y = size.height - (windSpeed / 20) * size.height; // تطبيع القيم

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

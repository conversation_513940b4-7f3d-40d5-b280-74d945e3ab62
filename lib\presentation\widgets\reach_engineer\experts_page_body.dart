import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';


import '../../../core/widgets/enhanced_refresh_indicator.dart';
import '../../../core/utils/logging/logger_service.dart';
import '../../../core/services/notification_service.dart';

import '../../bloc/experts/favorites/favorites_cubit.dart';
import '../../bloc/experts/favorites/favorites_state.dart';
import 'enhanced_expert_card_widget.dart';
import 'smart_search_widget.dart';
import 'rating_dialog_widget.dart';
import '../../../data/models/experts/index.dart';
import '../../../routing/navigation_service.dart';
import '../../../routing/route_constants.dart';

import '../../bloc/experts/index.dart';

import 'index.dart';

/// جسم صفحة الخبراء الزراعيين المبسط
///
/// يستخدم الويدجتس الجديدة المحسنة وفق التفضيلات الـ18
/// مع عدم التكرار ووضع الويدجتس في المكان الصحيح
class ExpertsPageBody extends StatefulWidget {
  const ExpertsPageBody({super.key});

  @override
  State<ExpertsPageBody> createState() => _ExpertsPageBodyState();
}

class _ExpertsPageBodyState extends State<ExpertsPageBody> {
  final ScrollController _scrollController = ScrollController();

  /// خدمة التنقل
  NavigationService get _navigationService => NavigationService();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _loadFavorites();
  }

  /// تحميل المفضلة
  void _loadFavorites() {
    context.read<FavoritesCubit>().loadFavorites();
  }

  /// تحميل البيانات الأولية
  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ExpertsPaginationCubit>().loadFirstPage();
    });
  }

  /// تحديث البيانات (Pull-to-refresh)
  Future<void> _refreshData() async {
    context.read<ExpertsPaginationCubit>().loadFirstPage();
    // انتظار قصير للسماح للبيانات بالتحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ExpertsPaginationCubit, ExpertsPaginationState>(
      builder: (context, state) {
        return EnhancedRefreshIndicator(
          onRefresh: _refreshData,
          color: Theme.of(context).primaryColor,
          animationType: RefreshAnimationType.scale,
          enableHapticFeedback: true,
          child: CustomScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              _buildSearchSection(),
              _buildStatisticsSection(state),
              _buildExpertsContent(state),
            ],
          ),
        );
      },
    );
  }

  /// بناء قسم البحث المحسن
  Widget _buildSearchSection() {
    return SliverToBoxAdapter(
      child: SmartSearchWidget(
        onSearchChanged: (query) {
          _performSearch(query);
        },
        onSuggestionSelected: (suggestion) {
          _performSearch(suggestion);
        },
        onFiltersPressed: () => _showFiltersDialog(),
        suggestions: _getSearchSuggestions(),
        appliedFiltersCount: _getAppliedFiltersCount(),
        enableVoiceSearch: true,
        hintText: 'ابحث عن خبير زراعي...',
      ),
    );
  }

  /// الحصول على اقتراحات البحث
  List<String> _getSearchSuggestions() {
    return [
      'خبير محاصيل',
      'خبير ري',
      'خبير تربة',
      'خبير آفات',
      'خبير بساتين',
      'مرشد زراعي',
      'استشاري زراعي',
    ];
  }

  /// الحصول على عدد الفلاتر المطبقة
  int _getAppliedFiltersCount() {
    // TODO: حساب عدد الفلاتر المطبقة فعلياً
    return 0;
  }

  /// بناء قسم الإحصائيات
  Widget _buildStatisticsSection(ExpertsPaginationState state) {
    if (state is ExpertsPaginationLoaded) {
      return SliverToBoxAdapter(
        child: ExpertsStatisticsWidget(
          experts: state.experts,
          onStatisticTap: (type) => _handleStatisticTap(type),
          isCompact: false,
        ),
      );
    }

    return SliverToBoxAdapter(
      child: LoadingStatesWidget(
        type: LoadingType.statistics,
        showAnimations: true,
      ),
    );
  }

  /// بناء محتوى الخبراء
  Widget _buildExpertsContent(ExpertsPaginationState state) {
    if (state is ExpertsPaginationInitial) {
      return _buildInitialState();
    } else if (state is ExpertsPaginationLoading) {
      return _buildLoadingState();
    } else if (state is ExpertsPaginationLoaded) {
      return _buildLoadedState(state);
    } else if (state is ExpertsPaginationError) {
      return _buildErrorState(state);
    }

    return _buildInitialState();
  }

  /// بناء الحالة الأولية
  Widget _buildInitialState() {
    return SliverFillRemaining(
      child: LoadingStatesWidget(
        type: LoadingType.general,
        customMessage: 'جاري تحميل قائمة الخبراء...',
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return SliverToBoxAdapter(
      child: LoadingStatesWidget(type: LoadingType.expertsList, itemCount: 5),
    );
  }

  /// بناء حالة البيانات المحملة
  Widget _buildLoadedState(ExpertsPaginationLoaded state) {
    if (state.experts.isEmpty) {
      return _buildEmptyState();
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        if (index < state.experts.length) {
          return _buildExpertCard(state.experts[index]);
        } else if (state.hasMore) {
          // مؤشر تحميل المزيد
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }
        return null;
      }, childCount: state.experts.length + (state.hasMore ? 1 : 0)),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(ExpertsPaginationError state) {
    return SliverFillRemaining(
      child: EmptyStatesWidget(
        type: EmptyStateType.networkError,
        customDescription: state.message,
        onRetry: () {
          context.read<ExpertsPaginationCubit>().loadFirstPage();
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return SliverFillRemaining(
      child: EmptyStatesWidget(
        type: EmptyStateType.noExperts,
        onButtonPressed: () {
          context.read<ExpertsPaginationCubit>().loadFirstPage();
        },
      ),
    );
  }

  /// بناء بطاقة خبير محسنة
  Widget _buildExpertCard(AgriculturalExpertModel expert) {
    return EnhancedExpertCardWidget(
      expert: expert,
      isFavorite: _isFavoriteExpert(expert),
      onTap: () => _navigateToExpertProfile(expert),
      onCallPressed: () => _callExpert(expert),
      onMessagePressed: () => _messageExpert(expert),
      onBookAppointmentPressed: () => _bookAppointment(expert),
      onRatingPressed: () => _showRatingDialog(expert),
      onFavoriteToggled: () => _toggleFavorite(expert),
      showAnimations: true,
    );
  }

  /// التحقق من كون الخبير في المفضلة
  bool _isFavoriteExpert(AgriculturalExpertModel expert) {
    final favoritesState = context.read<FavoritesCubit>().state;
    if (favoritesState is FavoritesLoaded) {
      return favoritesState.isFavorite(expert.id);
    }
    return false;
  }

  /// عرض حوار الفلاتر
  void _showFiltersDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => AdvancedFiltersWidget(
            onFiltersApplied: ({
              governorate,
              specialty,
              minRating,
              isAvailable,
              minExperience,
            }) {
              _applyFilters(
                governorate: governorate,
                specialty: specialty,
                minRating: minRating,
                isAvailable: isAvailable,
                minExperience: minExperience,
              );
            },
            onFiltersCleared: () {
              _clearFilters();
            },
          ),
    );
  }

  /// معالج النقر على الإحصائية
  void _handleStatisticTap(String type) {
    switch (type) {
      case 'total':
        // عرض جميع الخبراء
        context.read<ExpertsPaginationCubit>().loadFirstPage();
        break;
      case 'available':
        // فلترة الخبراء المتاحين
        _applyFilters(isAvailable: true);
        break;
      case 'specialties':
        // عرض حوار الفلاتر
        _showFiltersDialog();
        break;
      case 'response_time':
        // ترتيب حسب وقت الاستجابة
        _applySorting('response_time');
        break;
    }
  }

  /// التنقل لملف الخبير
  void _navigateToExpertProfile(AgriculturalExpertModel expert) {
    try {
      LoggerService.info('التنقل لملف الخبير: ${expert.name}');
      _navigationService.navigateTo(
        RouteConstants.expertProfile,
        arguments: expert,
      );
    } catch (e) {
      LoggerService.error('خطأ في التنقل لملف الخبير', error: e);
      NotificationService.showErrorNotification(
        context: context,
        message: 'فشل في فتح ملف الخبير',
      );
    }
  }

  /// الاتصال بالخبير
  void _callExpert(AgriculturalExpertModel expert) {
    _makePhoneCall(expert.phone, expert.name);
  }

  /// مراسلة الخبير
  void _messageExpert(AgriculturalExpertModel expert) {
    _openWhatsApp(expert.phone, expert.name);
  }


  /// تبديل المفضلة
  void _toggleFavorite(AgriculturalExpertModel expert) {
    final favoritesCubit = context.read<FavoritesCubit>();
    final isFavorite = _isFavoriteExpert(expert);

    favoritesCubit.toggleFavorite(expert);

    // إظهار رسالة تأكيد
    final message =
        isFavorite
            ? 'تم إزالة ${expert.name} من المفضلة'
            : 'تم إضافة ${expert.name} إلى المفضلة';

    NotificationService.showSuccessNotification(
      context: context,
      message: message,
    );
  }


  /// حجز موعد مع الخبير
  void _bookAppointment(AgriculturalExpertModel expert) {
    if (!expert.isAvailable) {
      NotificationService.showWarningNotification(
        context: context,
        message: 'الخبير غير متاح حالياً لحجز المواعيد',
      );
      return;
    }
    _showAppointmentDialog(expert);
  }

  /// عرض حوار التقييم المحسن
  void _showRatingDialog(AgriculturalExpertModel expert) {
    showDialog(
      context: context,
      builder:
          (context) => RatingDialogWidget(
            expert: expert,
            farmerId:
                'current_farmer_id', // TODO: الحصول على معرف المزارع الحالي
            farmerName: 'المزارع الحالي', // TODO: الحصول على اسم المزارع الحالي
            onRatingSubmitted: () {
              // تحديث البيانات بعد إرسال التقييم
              setState(() {
                // إعادة تحميل البيانات لإظهار التقييم الجديد
              });
            },
          ),
    );
  }

  /// عرض حوار حجز الموعد
  void _showAppointmentDialog(AgriculturalExpertModel expert) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('حجز موعد مع ${expert.name}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('اختر التاريخ والوقت المناسب للاستشارة'),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('اليوم - 2:00 مساءً'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _confirmAppointment(expert, 'اليوم - 2:00 مساءً');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('غداً - 10:00 صباحاً'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _confirmAppointment(expert, 'غداً - 10:00 صباحاً');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('بعد غد - 4:00 مساءً'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _confirmAppointment(expert, 'بعد غد - 4:00 مساءً');
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }


  /// تأكيد حجز الموعد
  void _confirmAppointment(AgriculturalExpertModel expert, String time) {
    // TODO: حجز الموعد في النظام
    NotificationService.showSuccessNotification(
      context: context,
      message: 'تم حجز موعدك مع ${expert.name} في $time',
    );

    // إشعار تذكير بالموعد
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        NotificationService.showAppointmentReminder(
          context: context,
          expertName: expert.name,
          appointmentTime: time,
        );
      }
    });
  }

  // ========== الوظائف المساعدة ==========

  /// تطبيق البحث
  void _performSearch(String query) {
    try {
      LoggerService.info('البحث في الخبراء: $query');
      context.read<ExpertsPaginationCubit>().search(query);
    } catch (e) {
      LoggerService.error('خطأ في البحث', error: e);
      _showErrorMessage('فشل في البحث');
    }
  }

  /// تطبيق الفلاتر
  void _applyFilters({
    String? governorate,
    String? specialty,
    double? minRating,
    bool? isAvailable,
    int? minExperience,
  }) {
    try {
      LoggerService.info('تطبيق الفلاتر');

      // تطبيق فلتر المحافظة
      if (governorate != null) {
        context.read<ExpertsPaginationCubit>().filterByGovernorate(governorate);
      }

      // تطبيق فلتر التوفر
      if (isAvailable != null) {
        context.read<ExpertsPaginationCubit>().filterByAvailability(
          isAvailable,
        );
      }

      // للفلاتر الأخرى، يمكن إضافة منطق إضافي لاحقاً
      if (specialty != null || minRating != null || minExperience != null) {
        _showInfoMessage('سيتم إضافة المزيد من الفلاتر قريباً');
      }
    } catch (e) {
      LoggerService.error('خطأ في تطبيق الفلاتر', error: e);
      _showErrorMessage('فشل في تطبيق الفلاتر');
    }
  }

  /// مسح الفلاتر
  void _clearFilters() {
    try {
      LoggerService.info('مسح جميع الفلاتر');
      context.read<ExpertsPaginationCubit>().clearFilters();
    } catch (e) {
      LoggerService.error('خطأ في مسح الفلاتر', error: e);
      _showErrorMessage('فشل في مسح الفلاتر');
    }
  }

  /// تطبيق الترتيب
  void _applySorting(String sortType) {
    try {
      LoggerService.info('تطبيق الترتيب: $sortType');
      // يمكن إضافة منطق الترتيب هنا
      _showInfoMessage('سيتم تطبيق الترتيب قريباً');
    } catch (e) {
      LoggerService.error('خطأ في الترتيب', error: e);
      _showErrorMessage('فشل في تطبيق الترتيب');
    }
  }

  /// إجراء مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber, String expertName) async {
    try {
      LoggerService.info('محاولة الاتصال بـ $expertName: $phoneNumber');
      final phoneUrl = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(phoneUrl)) {
        await launchUrl(phoneUrl);
        LoggerService.info('تم فتح تطبيق الهاتف للاتصال');
      } else {
        _showErrorMessage('لا يمكن إجراء المكالمة');
      }
    } catch (e) {
      LoggerService.error('خطأ في إجراء المكالمة', error: e);
      _showErrorMessage('خطأ في إجراء المكالمة');
    }
  }

  /// فتح واتساب
  Future<void> _openWhatsApp(String phoneNumber, String expertName) async {
    try {
      LoggerService.info('فتح واتساب للتواصل مع $expertName');
      final message = 'مرحباً $expertName، أحتاج لاستشارة زراعية من فضلك.';
      final whatsappUrl = Uri.parse(
        'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}',
      );

      if (await canLaunchUrl(whatsappUrl)) {
        await launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
        LoggerService.info('تم فتح واتساب بنجاح');
      } else {
        _showErrorMessage('لا يمكن فتح واتساب');
      }
    } catch (e) {
      LoggerService.error('خطأ في فتح واتساب', error: e);
      _showErrorMessage('خطأ في فتح واتساب');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// عرض رسالة معلومات
  void _showInfoMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

}

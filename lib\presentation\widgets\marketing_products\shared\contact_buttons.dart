import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/colors.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../core/constants/text_styles.dart';
import '../../../../core/utils/phone_validator.dart';

/// أزرار التواصل مع المزارع
///
/// تحتوي على أزرار الاتصال وواتساب ورسائل SMS
class ContactButtons extends StatelessWidget {
  /// رقم هاتف المزارع
  final String farmerPhone;

  /// اسم المزارع (للرسائل)
  final String farmerName;

  /// اسم المنتج (للرسائل)
  final String productName;

  /// معرف المزارع (للمحادثة الداخلية)
  final String? farmerId;

  /// معرف المنتج (للمحادثة الداخلية)
  final String? productId;

  /// دالة للانتقال إلى المحادثة (اختيارية)
  final VoidCallback? onStartConversation;

  /// إنشاء أزرار التواصل
  const ContactButtons({
    super.key,
    required this.farmerPhone,
    required this.farmerName,
    required this.productName,
    this.farmerId,
    this.productId,
    this.onStartConversation,
  });

  @override
  Widget build(BuildContext context) {
    // التحقق من صحة رقم الهاتف
    if (farmerPhone.isEmpty ||
        YemenPhoneValidator.getValidationError(farmerPhone) != null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(Dimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(Dimensions.radiusM),
        border: Border.all(color: AppColors.divider),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Text(
            'تواصل مع المزارع',
            style: TextStyles.of(context).bodyLarge(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),

          const SizedBox(height: Dimensions.paddingS),

          // عرض رقم الهاتف
          Row(
            children: [
              const Icon(Icons.phone, color: AppColors.primary, size: 20),
              const SizedBox(width: Dimensions.paddingS),
              Text(
                YemenPhoneValidator.formatYemenPhone(farmerPhone),
                style: TextStyles.of(context).bodyMedium(
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              // اسم الشبكة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(Dimensions.radiusS),
                ),
                child: Text(
                  YemenPhoneValidator.getNetworkName(farmerPhone) ?? 'يمن',
                  style: TextStyles.of(context).labelSmall(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: Dimensions.paddingM),

          // أزرار التواصل
          Column(
            children: [
              // الصف الأول: المحادثة الداخلية
              if (farmerId != null && productId != null) ...[
                SizedBox(
                  width: double.infinity,
                  child: _buildContactButton(
                    context: context,
                    icon: Icons.message,
                    label: 'بدء محادثة',
                    color: AppColors.primary,
                    onTap:
                        onStartConversation ??
                        () => _startInternalConversation(context),
                  ),
                ),
                const SizedBox(height: Dimensions.paddingS),
              ],

              // الصف الثاني: الأزرار الخارجية
              Row(
                children: [
                  // زر الاتصال
                  Expanded(
                    child: _buildContactButton(
                      context: context,
                      icon: Icons.call,
                      label: 'اتصال',
                      color: AppColors.success,
                      onTap: () => _makePhoneCall(context),
                    ),
                  ),

                  const SizedBox(width: Dimensions.paddingS),

                  // زر واتساب
                  Expanded(
                    child: _buildContactButton(
                      context: context,
                      icon: Icons.chat,
                      label: 'واتساب',
                      color: const Color(0xFF25D366), // لون واتساب
                      onTap: () => _openWhatsApp(context),
                    ),
                  ),

                  const SizedBox(width: Dimensions.paddingS),

                  // زر رسائل SMS
                  Expanded(
                    child: _buildContactButton(
                      context: context,
                      icon: Icons.sms,
                      label: 'رسالة',
                      color: AppColors.info,
                      onTap: () => _sendSMS(context),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر تواصل
  Widget _buildContactButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(Dimensions.radiusS),
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: Dimensions.paddingS,
          horizontal: Dimensions.paddingXS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(Dimensions.radiusS),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyles.of(
                context,
              ).labelSmall(color: color, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ),
    );
  }

  /// إجراء مكالمة هاتفية
  Future<void> _makePhoneCall(BuildContext context) async {
    try {
      final phoneUrl = Uri.parse('tel:$farmerPhone');
      if (await canLaunchUrl(phoneUrl)) {
        await launchUrl(phoneUrl);
      } else {
        if (context.mounted) {
          _showErrorMessage(context, 'لا يمكن إجراء المكالمة');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, 'خطأ في إجراء المكالمة');
      }
    }
  }

  /// فتح واتساب
  Future<void> _openWhatsApp(BuildContext context) async {
    try {
      final message =
          'مرحباً $farmerName، أنا مهتم بمنتج "$productName" المعروض في تطبيق الزراعة.';
      final whatsappUrl = Uri.parse(
        'https://wa.me/$farmerPhone?text=${Uri.encodeComponent(message)}',
      );

      if (await canLaunchUrl(whatsappUrl)) {
        await launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          _showErrorMessage(context, 'لا يمكن فتح واتساب');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, 'خطأ في فتح واتساب');
      }
    }
  }

  /// إرسال رسالة SMS
  Future<void> _sendSMS(BuildContext context) async {
    try {
      final message = 'مرحباً $farmerName، أنا مهتم بمنتج "$productName".';
      final smsUrl = Uri.parse(
        'sms:$farmerPhone?body=${Uri.encodeComponent(message)}',
      );

      if (await canLaunchUrl(smsUrl)) {
        await launchUrl(smsUrl);
      } else {
        if (context.mounted) {
          _showErrorMessage(context, 'لا يمكن إرسال الرسالة');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, 'خطأ في إرسال الرسالة');
      }
    }
  }

  /// بدء محادثة داخلية
  void _startInternalConversation(BuildContext context) {
    // TODO: تنفيذ الانتقال إلى صفحة المحادثة
    // سيتم تنفيذ هذا لاحقاً عند ربط النظام
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تفعيل نظام المحادثات قريباً'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

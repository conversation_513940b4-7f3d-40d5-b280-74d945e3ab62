import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:agriculture/data/models/education/article_model.dart';
import 'package:agriculture/presentation/bloc/education/articles_cubit/articles_cubit.dart';
import 'package:agriculture/presentation/bloc/education/articles_cubit/articles_state.dart';
import 'package:agriculture/presentation/widgets/shared/custom_loading_animation.dart';
import 'package:agriculture/presentation/widgets/education/article_details_header.dart';
import 'package:agriculture/presentation/widgets/education/article_info_card.dart';
import 'package:agriculture/presentation/widgets/education/article_tags_widget.dart';
import 'package:agriculture/presentation/widgets/education/article_content_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// صفحة تفاصيل المقالة الزراعية
///
/// تعرض هذه الصفحة محتوى المقالة كاملاً مع معلومات إضافية
/// مثل الكاتب وتاريخ النشر ووقت القراءة باستخدام ArticlesCubit
class ArticleDetailsPage extends StatelessWidget {
  /// معرف المقالة
  final String articleId;

  /// إنشاء صفحة تفاصيل المقالة
  ///
  /// المعلمات:
  /// - [articleId]: معرف المقالة المراد عرضها
  const ArticleDetailsPage({
    super.key,
    required this.articleId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ArticlesCubit()..loadArticleDetails(articleId),
      child: Scaffold(
        backgroundColor: AssetsColors.kWhite,
        body: BlocBuilder<ArticlesCubit, ArticlesState>(
          builder: (context, state) {
            if (state is ArticlesLoading) {
              return _buildLoadingState();
            }

            if (state is ArticleDetailsLoaded) {
              return _buildArticleContent(context, state.article);
            }

            if (state is ArticlesError) {
              return _buildErrorState(context, state.message);
            }

            return _buildNotFoundState(context);
          },
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  ///
  /// يعرض مؤشر التحميل أثناء تحميل المقالة
  Widget _buildLoadingState() {
    return Scaffold(
      backgroundColor: AssetsColors.kWhite,
      appBar: AppBar(
        backgroundColor: AssetsColors.primary,
        elevation: 0,
        title: Text(
          'تحميل المقالة...',
          style: TextStyle(
            fontSize: 18,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kWhite,
          ),
        ),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomLoadingAnimation(),
            SizedBox(height: 16),
            Text(
              'جاري تحميل المقالة...',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة الخطأ
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [message]: رسالة الخطأ
  ///
  /// يعرض رسالة خطأ مع زر إعادة المحاولة
  Widget _buildErrorState(BuildContext context, String message) {
    return Scaffold(
      backgroundColor: AssetsColors.kWhite,
      appBar: AppBar(
        backgroundColor: AssetsColors.primary,
        elevation: 0,
        title: Text(
          'خطأ في التحميل',
          style: TextStyle(
            fontSize: 18,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kWhite,
          ),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ أثناء تحميل المقالة',
                style: TextStyles.of(context).headlineMedium(
                  fontSize: 18,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => context.read<ArticlesCubit>().loadArticleDetails(articleId),
                icon: const Icon(Icons.refresh),
                label: const Text(
                  'إعادة المحاولة',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AssetsColors.primary,
                  foregroundColor: AssetsColors.kWhite,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة المقالة غير موجودة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  ///
  /// يعرض رسالة عندما لا توجد المقالة
  Widget _buildNotFoundState(BuildContext context) {
    return Scaffold(
      backgroundColor: AssetsColors.kWhite,
      appBar: AppBar(
        backgroundColor: AssetsColors.primary,
        elevation: 0,
        title: Text(
          'المقالة غير موجودة',
          style: TextStyle(
            fontSize: 18,
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: AssetsColors.kWhite,
          ),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.article_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'المقالة غير موجودة',
                style: TextStyles.of(context).headlineMedium(
                  fontSize: 18,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'لم يتم العثور على المقالة المطلوبة',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 14,
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                label: const Text(
                  'العودة',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AssetsColors.primary,
                  foregroundColor: AssetsColors.kWhite,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء محتوى المقالة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [article]: المقالة المراد عرضها
  ///
  /// يعرض محتوى المقالة كاملاً باستخدام الويدجتات المخصصة
  Widget _buildArticleContent(BuildContext context, ArticleModel article) {
    return CustomScrollView(
      slivers: [
        // رأس المقالة مع الصورة والعنوان
        ArticleDetailsHeader(
          article: article,
          onShare: () => _shareArticle(context, article),
          onToggleFavorite: () => _toggleFavorite(context, article),
        ),

        // محتوى المقالة
        SliverToBoxAdapter(
          child: Column(
            children: [
              const SizedBox(height: 16),

              // بطاقة معلومات المقالة
              ArticleInfoCard(article: article),

              const SizedBox(height: 16),

              // محتوى المقالة (الملخص والنص الكامل)
              ArticleContentWidget(article: article),

              const SizedBox(height: 16),

              // الكلمات المفتاحية
              ArticleTagsWidget(
                tags: article.tags,
                onTagTap: (tag) => _searchByTag(context, tag),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ],
    );
  }

  /// مشاركة المقالة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [article]: المقالة المراد مشاركتها
  ///
  /// يقوم بمشاركة رابط المقالة
  void _shareArticle(BuildContext context, ArticleModel article) {
    // TODO: تنفيذ مشاركة المقالة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'سيتم إضافة ميزة المشاركة قريباً',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
      ),
    );
  }

  /// تبديل حالة المفضلة
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [article]: المقالة المراد إضافتها/إزالتها من المفضلة
  ///
  /// يقوم بإضافة أو إزالة المقالة من المفضلة
  void _toggleFavorite(BuildContext context, ArticleModel article) {
    // TODO: تنفيذ إضافة/إزالة من المفضلة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          article.isFeatured ? 'تم إزالة المقالة من المفضلة' : 'تم إضافة المقالة للمفضلة',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
      ),
    );
  }

  /// البحث بالكلمة المفتاحية
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [tag]: الكلمة المفتاحية للبحث بها
  ///
  /// يقوم بالبحث عن المقالات التي تحتوي على الكلمة المفتاحية
  void _searchByTag(BuildContext context, String tag) {
    // العودة للصفحة السابقة مع البحث بالكلمة المفتاحية
    Navigator.pop(context, {'searchQuery': tag});
  }
}


import 'package:flutter/services.dart';

import '../../../../imports.dart';

/// عرض مربع حوار الخروج من التطبيق
///
/// يستخدم هذا المكون لعرض مربع حوار للتأكيد عند محاولة الخروج من التطبيق.
/// يعرض خيارين: "إلغاء" و"خروج".
Future<bool?> showExitDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 8),
      contentPadding: const EdgeInsets.fromLTRB(24, 8, 24, 16),
      actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      
      title: Text(
        'الخروج من التطبيق',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: AssetsFonts.messiri,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
      content: Text(
        'هل تريد الخروج من التطبيق؟',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontFamily: AssetsFonts.messiri,
          fontSize: 16,
          color: Colors.black54,
        ),
      ),
      actionsAlignment: MainAxisAlignment.spaceEvenly,
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            backgroundColor: Colors.white,
          ),
          child: Text(
            'إلغاء',
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontSize: 16,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context, true);
          },
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            backgroundColor: AppColors.primary,
          ),
          child: Text(
            'خروج',
            style: TextStyle(
              fontFamily: AssetsFonts.messiri,
              fontSize: 16,
              color: Colors.white,
            ),
          ),
        ),
      ],
    ),
  );
}

/// معالجة الخروج من التطبيق
///
/// تستخدم هذه الدالة لمعالجة الخروج من التطبيق عند النقر على زر الرجوع.
/// تعرض مربع حوار للتأكيد وتغلق التطبيق إذا نقر المستخدم على "خروج".
Future<bool> handleAppExit(BuildContext context) async {
  final shouldExit = await showExitDialog(context);
  
  if (shouldExit == true) {
    SystemNavigator.pop();
  }
  
  return false;
}

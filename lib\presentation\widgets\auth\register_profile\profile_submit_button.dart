

import '../../../../imports.dart';

/// زر إكمال التسجيل
class ProfileSubmitButton extends StatelessWidget {
  final String? phoneNumber;

  const ProfileSubmitButton({
    super.key,
    this.phoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<RegisterProfileCubit>();

    return BlocBuilder<RegisterProfileCubit, RegisterProfileState>(
      buildWhen: (previous, current) =>
          current is RegisterProfileLoading ||
          current is RegisterProfileFailure ||
          current is RegisterProfileSuccess,
      builder: (context, state) {
        final bool isLoading = state is RegisterProfileLoading;

        return SizedBox(
          width: double.infinity,
          height: 55,
          child: StatefulBuilder(
            builder: (context, setState) {
              bool isPressed = false;

              return GestureDetector(
                onTapDown: isLoading
                    ? null
                    : (_) {
                        setState(() {
                          isPressed = true;
                        });
                      },
                onTapUp: isLoading
                    ? null
                    : (_) {
                        setState(() {
                          isPressed = false;
                        });
                        cubit.completeRegistration(phoneNumber, context);
                      },
                onTapCancel: isLoading
                    ? null
                    : () {
                        setState(() {
                          isPressed = false;
                        });
                      },
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 150),
                  height: 55,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: isLoading
                        ? Colors.grey.shade400
                        : (isPressed ? Colors.green.shade800 : Colors.green),
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: isPressed
                            ? Colors.green.withAlpha(51)
                            : Colors.green.withAlpha(102),
                        blurRadius: isPressed ? 4 : 8,
                        offset: isPressed ? Offset(0, 2) : Offset(0, 4),
                      ),
                    ],
                  ),
                  transform: Matrix4.identity()..scale(isPressed ? 0.98 : 1.0),
                  child: Center(
                    child: isLoading
                        ? CircularProgressIndicator(color: Colors.white)
                        : Text(
                            'إكمال التسجيل',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontFamily: AssetsFonts.messiri,
                            ),
                          ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

/// استثناء API
///
/// يمثل الأخطاء التي تحدث أثناء التفاعل مع API
class ApiException implements Exception {
  /// رسالة الخطأ
  final String? message;

  /// كود الخطأ
  final int? statusCode;

  /// نوع الخطأ
  final ApiExceptionType type;

  const ApiException({
    this.message,
    this.statusCode,
    this.type = ApiExceptionType.unknown,
  });

  /// إنشاء استثناء خطأ اتصال
  const ApiException.connectionError([String? message])
      : message = message ?? 'خطأ في الاتصال',
        statusCode = null,
        type = ApiExceptionType.connectionError;

  /// إنشاء استثناء انتهاء مهلة الاتصال
  const ApiException.timeout([String? message])
      : message = message ?? 'انتهت مهلة الاتصال',
        statusCode = null,
        type = ApiExceptionType.timeout;

  /// إنشاء استثناء خطأ خادم
  const ApiException.serverError([String? message, this.statusCode])
      : message = message ?? 'خطأ في الخادم',
        type = ApiExceptionType.serverError;

  /// إنشاء استثناء غير مصرح
  const ApiException.unauthorized([String? message])
      : message = message ?? 'غير مصرح',
        statusCode = 401,
        type = ApiExceptionType.unauthorized;

  /// إنشاء استثناء ممنوع
  const ApiException.forbidden([String? message])
      : message = message ?? 'ممنوع',
        statusCode = 403,
        type = ApiExceptionType.forbidden;

  /// إنشاء استثناء غير موجود
  const ApiException.notFound([String? message])
      : message = message ?? 'غير موجود',
        statusCode = 404,
        type = ApiExceptionType.notFound;

  /// إنشاء استثناء خطأ عميل
  const ApiException.clientError([String? message, this.statusCode])
      : message = message ?? 'خطأ عميل',
        type = ApiExceptionType.clientError;

  // ===== خصائص مساعدة =====

  /// هل هو خطأ اتصال؟
  bool get isConnectionError => type == ApiExceptionType.connectionError;

  /// هل هو خطأ انتهاء مهلة؟
  bool get isTimeoutError => type == ApiExceptionType.timeout;

  /// هل هو خطأ خادم؟
  bool get isServerError => type == ApiExceptionType.serverError;

  /// هل هو خطأ غير مصرح؟
  bool get isUnauthorized => type == ApiExceptionType.unauthorized;

  /// هل هو خطأ ممنوع؟
  bool get isForbidden => type == ApiExceptionType.forbidden;

  /// هل هو خطأ غير موجود؟
  bool get isNotFound => type == ApiExceptionType.notFound;

  /// هل هو خطأ عميل؟
  bool get isClientError => type == ApiExceptionType.clientError;

  @override
  String toString() {
    if (statusCode != null) {
      return 'ApiException($statusCode): $message';
    }
    return 'ApiException: $message';
  }
}

/// أنواع استثناءات API
enum ApiExceptionType {
  /// خطأ اتصال
  connectionError,

  /// انتهاء مهلة الاتصال
  timeout,

  /// خطأ خادم (5xx)
  serverError,

  /// غير مصرح (401)
  unauthorized,

  /// ممنوع (403)
  forbidden,

  /// غير موجود (404)
  notFound,

  /// خطأ عميل (4xx)
  clientError,

  /// خطأ غير معروف
  unknown,
}

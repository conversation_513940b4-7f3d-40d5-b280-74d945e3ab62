import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:agriculture/presentation/bloc/admin/admin_users_cubit.dart';
import 'package:agriculture/presentation/bloc/admin/admin_users_state.dart';
import 'package:agriculture/presentation/widgets/admin/index.dart';
import 'package:agriculture/presentation/widgets/shared/custom_loading_animation.dart';
import 'package:agriculture/presentation/widgets/shared/error_message.dart';
import 'package:agriculture/presentation/widgets/shared/enhanced_video_player.dart';
import 'package:agriculture/routing/route_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// صفحة إدارة دروس الدورة التدريبية
///
/// تعرض هذه الصفحة قائمة بدروس دورة معينة
/// مع إمكانية إضافة وتعديل وحذف الدروس
class AdminCourseLessonsPage extends StatefulWidget {
  /// معرف الدورة
  final String courseId;

  /// عنوان الدورة
  final String courseTitle;

  /// إنشاء صفحة إدارة دروس الدورة
  ///
  /// المعلمات:
  /// - [courseId]: معرف الدورة
  /// - [courseTitle]: عنوان الدورة
  const AdminCourseLessonsPage({
    super.key,
    required this.courseId,
    required this.courseTitle,
  });

  @override
  State<AdminCourseLessonsPage> createState() => _AdminCourseLessonsPageState();
}

class _AdminCourseLessonsPageState extends State<AdminCourseLessonsPage> {
  @override
  void initState() {
    super.initState();
    context.read<AdminUsersCubit>().loadCourseLessons(widget.courseId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocBuilder<AdminUsersCubit, AdminUsersState>(
        builder: (context, state) {
          if (state is AdminLessonsLoading) {
            return const Center(child: CustomLoadingAnimation());
          } else if (state is AdminLessonsError) {
            return ErrorMessage(
              message: state.message,
              onRetry: () => context.read<AdminUsersCubit>().loadCourseLessons(widget.courseId),
            );
          } else if (state is AdminLessonsLoaded) {
            return _buildLessonsList(context, state);
          } else {
            return const Center(child: CustomLoadingAnimation());
          }
        },
      ),
      floatingActionButton: BlocBuilder<AdminUsersCubit, AdminUsersState>(
        builder: (context, state) {
          // إخفاء الزر العائم في الحالة الفارغة لتجنب التكرار
          if (state is AdminLessonsLoaded && state.lessons.isEmpty) {
            return const SizedBox.shrink();
          }
          return _buildFloatingActionButton();
        },
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'دروس الدورة',
            style: TextStyles.of(context).bodyMedium(
              fontSize: 18,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
              color: AssetsColors.kWhite,
            ),
          ),
          Text(
            widget.courseTitle,
            style: TextStyles.of(context).bodySmall(
              fontSize: 12,
              fontFamily: AssetsFonts.cairo,
              color: AssetsColors.kWhite.withValues(alpha: 0.8),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
      backgroundColor: AssetsColors.primary,
      foregroundColor: AssetsColors.kWhite,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () => context.read<AdminUsersCubit>().loadCourseLessons(widget.courseId),
          icon: const Icon(Icons.refresh),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  /// بناء قائمة الدروس
  Widget _buildLessonsList(BuildContext context, AdminLessonsLoaded state) {
    if (state.lessons.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<AdminUsersCubit>().loadCourseLessons(widget.courseId);
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(state.lessons.length),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: state.lessons.length,
                itemBuilder: (context, index) {
                  final lesson = state.lessons[index];
                  return AdminLessonCard(
                    lesson: lesson,
                    onEdit: () => _navigateToEditLesson(lesson.id),
                    onPlayVideo: lesson.videoUrl.isNotEmpty
                        ? () => _playVideo(lesson.videoUrl)
                        : null,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس القائمة
  Widget _buildHeader(int lessonsCount) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AssetsColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AssetsColors.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.play_circle_outline,
            color: AssetsColors.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجمالي الدروس',
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 14,
                    fontFamily: AssetsFonts.cairo,
                    color: AssetsColors.kGrey70,
                  ),
                ),
                Text(
                  '$lessonsCount درس',
                  style: TextStyles.of(context).bodyMedium(
                    fontSize: 18,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.primary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AssetsColors.primary,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'مرتبة',
              style: TextStyles.of(context).bodySmall(
                fontSize: 12,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kWhite,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة القائمة الفارغة
  Widget _buildEmptyState() {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<AdminUsersCubit>().loadCourseLessons(widget.courseId);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // معلومات الدورة
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(bottom: 32),
                decoration: BoxDecoration(
                  color: AssetsColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AssetsColors.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.school,
                      color: AssetsColors.primary,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.courseTitle,
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 16,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: AssetsColors.primary,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'دورة تدريبية جاهزة لإضافة الدروس',
                      style: TextStyles.of(context).bodySmall(
                        fontSize: 12,
                        fontFamily: AssetsFonts.cairo,
                        color: AssetsColors.kGrey70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // أيقونة الحالة الفارغة
              Icon(
                Icons.video_library_outlined,
                size: 80,
                color: AssetsColors.kGrey70,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد دروس بعد',
                style: TextStyles.of(context).headlineMedium(
                  fontSize: 20,
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: AssetsColors.kGrey100,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'ابدأ ببناء محتوى الدورة عبر إضافة الدروس التعليمية',
                style: TextStyles.of(context).bodyMedium(
                  fontSize: 16,
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kGrey70,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'يمكنك إضافة فيديوهات تعليمية ومحتوى نصي لكل درس',
                style: TextStyles.of(context).bodySmall(
                  fontSize: 12,
                  fontFamily: AssetsFonts.cairo,
                  color: AssetsColors.kGrey70,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // زر الإضافة
              ElevatedButton.icon(
                onPressed: _navigateToAddLesson,
                icon: const Icon(Icons.add),
                label: const Text(
                  'إضافة أول درس',
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AssetsColors.primary,
                  foregroundColor: AssetsColors.kWhite,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  elevation: 2,
                ),
              ),

              const SizedBox(height: 16),

              // نصائح سريعة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: Colors.blue,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'نصائح سريعة:',
                          style: TextStyles.of(context).bodyMedium(
                            fontSize: 14,
                            fontFamily: AssetsFonts.cairo,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• رتب الدروس بتسلسل منطقي\n• أضف فيديوهات قصيرة ومفيدة\n• اكتب وصف واضح لكل درس',
                      style: TextStyles.of(context).bodySmall(
                        fontSize: 12,
                        fontFamily: AssetsFonts.cairo,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر الإضافة العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToAddLesson,
      backgroundColor: AssetsColors.primary,
      foregroundColor: AssetsColors.kWhite,
      icon: const Icon(Icons.add),
      label: const Text(
        'إضافة درس',
        style: TextStyle(
          fontSize: 14,
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// الانتقال إلى صفحة إضافة درس
  void _navigateToAddLesson() {
    // معلومات مفصلة للتصحيح
    // AdminCourseLessonsPage._navigateToAddLesson() - بدء الانتقال
    // معرف الدورة في الصفحة: "${widget.courseId}"
    // عنوان الدورة في الصفحة: "${widget.courseTitle}"

    // التحقق من أن courseId ليس فارغ
    if (widget.courseId.isEmpty) {
      // خطأ حرج: معرف الدورة فارغ في صفحة الدروس!
      _showMessage('خطأ: معرف الدورة مفقود', Colors.red);
      return;
    }

    final arguments = {
      'courseId': widget.courseId,
      'courseTitle': widget.courseTitle,
    };

    // المعلمات المرسلة: $arguments

    Navigator.pushNamed(
      context,
      RouteConstants.adminLessonForm,
      arguments: arguments,
    );
  }

  /// الانتقال إلى صفحة تعديل درس
  void _navigateToEditLesson(String lessonId) {
    Navigator.pushNamed(
      context,
      RouteConstants.adminLessonForm,
      arguments: {
        'courseId': widget.courseId,
        'courseTitle': widget.courseTitle,
        'lessonId': lessonId,
      },
    );
  }

  /// تشغيل الفيديو باستخدام مشغل الفيديو المحسن
  void _playVideo(String videoUrl) {
    // التحقق من صحة الرابط
    if (videoUrl.isEmpty) {
      _showMessage('رابط الفيديو غير متاح', Colors.orange);
      return;
    }

    // استخدام نفس النظام المستخدم في المنتدى
    context.openFullScreenVideo(
      videoUrl,
      title: 'تشغيل درس الدورة',
    );
  }

  /// عرض رسالة
  void _showMessage(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

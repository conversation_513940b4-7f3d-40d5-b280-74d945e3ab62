

import '../../../core/constants/weather/index.dart';
import '../../../imports.dart';

/// ويدجت حالة التحميل للطقس
///
/// يعرض هيكل تحميل موحد لجميع صفحات الطقس
/// مع skeleton loading للبطاقات والقوائم
class WeatherLoadingWidget extends StatelessWidget {
  /// نوع التحميل المطلوب عرضه
  final WeatherLoadingType loadingType;

  /// إنشاء ويدجت حالة التحميل
  const WeatherLoadingWidget({
    super.key,
    this.loadingType = WeatherLoadingType.full,
  });

  @override
  Widget build(BuildContext context) {
    switch (loadingType) {
      case WeatherLoadingType.full:
        return _buildFullLoading();
      case WeatherLoadingType.currentWeather:
        return _buildCurrentWeatherLoading();
      case WeatherLoadingType.hourlyForecast:
        return _buildHourlyForecastLoading();
      case WeatherLoadingType.weeklyForecast:
        return _buildWeeklyForecastLoading();
      case WeatherLoadingType.minimal:
        return _buildMinimalLoading();
    }
  }

  /// بناء حالة التحميل الكاملة
  Widget _buildFullLoading() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      child: Column(
        children: [
          SizedBox(height: WeatherDimensions.largeMargin),
          const WeatherCardSkeletonLoading(),
          SizedBox(height: WeatherDimensions.largeMargin),
          const HourlyForecastSkeletonLoading(),
          SizedBox(height: WeatherDimensions.largeMargin),
          const WeeklyForecastSkeletonLoading(),
        ],
      ),
    );
  }

  /// بناء حالة تحميل الطقس الحالي
  Widget _buildCurrentWeatherLoading() {
    return const WeatherCardSkeletonLoading();
  }

  /// بناء حالة تحميل التوقعات بالساعة
  Widget _buildHourlyForecastLoading() {
    return const HourlyForecastSkeletonLoading();
  }

  /// بناء حالة تحميل التوقعات الأسبوعية
  Widget _buildWeeklyForecastLoading() {
    return const WeeklyForecastSkeletonLoading();
  }

  /// بناء حالة التحميل المبسطة
  Widget _buildMinimalLoading() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: WeatherColors.primaryBackground,
            strokeWidth: 3.0,
          ),
          SizedBox(height: WeatherDimensions.mediumMargin),
          Text(
            WeatherStrings.loadingMessage,
            style: TextStyle(
              fontSize: WeatherDimensions.bodyFontSize,
              fontFamily: AssetsFonts.cairo,
              color: WeatherColors.primaryText,
            ),
          ),
        ],
      ),
    );
  }
}

/// أنواع التحميل المختلفة
enum WeatherLoadingType {
  /// تحميل كامل لجميع المكونات
  full,
  
  /// تحميل الطقس الحالي فقط
  currentWeather,
  
  /// تحميل التوقعات بالساعة فقط
  hourlyForecast,
  
  /// تحميل التوقعات الأسبوعية فقط
  weeklyForecast,
  
  /// تحميل مبسط مع مؤشر دائري
  minimal,
}

/// ويدجت skeleton loading لبطاقة الطقس
class WeatherCardSkeletonLoading extends StatelessWidget {
  const WeatherCardSkeletonLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: WeatherDimensions.temperatureDisplayHeight + 100,
      decoration: BoxDecoration(
        color: WeatherColors.cardBackground,
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
      ),
      child: Padding(
        padding: EdgeInsets.all(WeatherDimensions.cardPadding),
        child: Column(
          children: [
            // موقع وتاريخ
            _buildSkeletonLine(width: 150, height: 20),
            SizedBox(height: WeatherDimensions.mediumMargin),
            
            // درجة الحرارة والأيقونة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSkeletonCircle(radius: 40),
                _buildSkeletonLine(width: 80, height: 60),
                _buildSkeletonCircle(radius: 40),
              ],
            ),
            SizedBox(height: WeatherDimensions.mediumMargin),
            
            // وصف الطقس
            _buildSkeletonLine(width: 120, height: 16),
            SizedBox(height: WeatherDimensions.largeMargin),
            
            // معلومات إضافية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSkeletonLine(width: 60, height: 14),
                _buildSkeletonLine(width: 60, height: 14),
                _buildSkeletonLine(width: 60, height: 14),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// ويدجت skeleton loading للتوقعات بالساعة
class HourlyForecastSkeletonLoading extends StatelessWidget {
  const HourlyForecastSkeletonLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: WeatherDimensions.hourlyForecastItemHeight,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 8,
        itemBuilder: (context, index) {
          return Container(
            width: WeatherDimensions.hourlyForecastItemWidth,
            margin: EdgeInsets.only(right: WeatherDimensions.smallMargin),
            child: Column(
              children: [
                _buildSkeletonLine(width: 40, height: 12),
                SizedBox(height: WeatherDimensions.smallMargin),
                _buildSkeletonCircle(radius: 20),
                SizedBox(height: WeatherDimensions.smallMargin),
                _buildSkeletonLine(width: 30, height: 16),
                SizedBox(height: WeatherDimensions.smallMargin),
                _buildSkeletonLine(width: 25, height: 10),
              ],
            ),
          );
        },
      ),
    );
  }
}

/// ويدجت skeleton loading للتوقعات الأسبوعية
class WeeklyForecastSkeletonLoading extends StatelessWidget {
  const WeeklyForecastSkeletonLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(7, (index) {
        return Container(
          height: WeatherDimensions.weeklyForecastItemHeight,
          margin: EdgeInsets.only(bottom: WeatherDimensions.smallMargin),
          child: Row(
            children: [
              _buildSkeletonLine(width: 80, height: 16),
              Spacer(),
              _buildSkeletonCircle(radius: 15),
              SizedBox(width: WeatherDimensions.mediumMargin),
              _buildSkeletonLine(width: 40, height: 16),
              SizedBox(width: WeatherDimensions.smallMargin),
              _buildSkeletonLine(width: 40, height: 16),
            ],
          ),
        );
      }),
    );
  }
}

/// بناء خط skeleton
Widget _buildSkeletonLine({required double width, required double height}) {
  return Container(
    width: width,
    height: height,
    decoration: BoxDecoration(
      color: WeatherColors.lightText.withValues(alpha: 0.3),
      borderRadius: BorderRadius.circular(4),
    ),
  );
}

/// بناء دائرة skeleton
Widget _buildSkeletonCircle({required double radius}) {
  return Container(
    width: radius * 2,
    height: radius * 2,
    decoration: BoxDecoration(
      color: WeatherColors.lightText.withValues(alpha: 0.3),
      shape: BoxShape.circle,
    ),
  );
}

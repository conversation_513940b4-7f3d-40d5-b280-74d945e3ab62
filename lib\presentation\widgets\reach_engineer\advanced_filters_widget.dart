import 'package:flutter/material.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// قائمة المحافظات اليمنية
const List<String> _yemenGovernorates = [
  'صنعاء',
  'عدن',
  'تعز',
  'الحديدة',
  'إب',
  'ذمار',
  'حضرموت',
  'المحويت',
  'حجة',
  'صعدة',
  'عمران',
  'البيضاء',
  'لحج',
  'أبين',
  'شبوة',
  'المهرة',
  'الجوف',
  'مأرب',
  'الضالع',
  'ريمة',
  'سقطرى',
];

/// ويدجت الفلاتر المتقدمة للخبراء الزراعيين
///
/// يوفر واجهة شاملة لفلترة الخبراء حسب معايير متعددة
/// مطبق وفق التفضيلات الـ18 مع عدم التكرار
class AdvancedFiltersWidget extends StatefulWidget {
  /// المحافظة المختارة حالياً
  final String? selectedGovernorate;

  /// التخصص المختار حالياً
  final String? selectedSpecialty;

  /// التقييم الأدنى المختار
  final double? minRating;

  /// حالة التوفر المختارة
  final bool? isAvailable;

  /// سنوات الخبرة الأدنى
  final int? minExperience;

  /// دالة استدعاء عند تطبيق الفلاتر
  final Function({
    String? governorate,
    String? specialty,
    double? minRating,
    bool? isAvailable,
    int? minExperience,
  })?
  onFiltersApplied;

  /// دالة استدعاء عند مسح الفلاتر
  final VoidCallback? onFiltersCleared;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  const AdvancedFiltersWidget({
    super.key,
    this.selectedGovernorate,
    this.selectedSpecialty,
    this.minRating,
    this.isAvailable,
    this.minExperience,
    this.onFiltersApplied,
    this.onFiltersCleared,
    this.showAnimations = true,
  });

  @override
  State<AdvancedFiltersWidget> createState() => _AdvancedFiltersWidgetState();
}

class _AdvancedFiltersWidgetState extends State<AdvancedFiltersWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // متغيرات الفلاتر المحلية
  String? _selectedGovernorate;
  String? _selectedSpecialty;
  double _minRating = 0.0;
  bool? _isAvailable;
  int _minExperience = 0;

  @override
  void initState() {
    super.initState();

    // تهيئة القيم من الخصائص
    _selectedGovernorate = widget.selectedGovernorate;
    _selectedSpecialty = widget.selectedSpecialty;
    _minRating = widget.minRating ?? 0.0;
    _isAvailable = widget.isAvailable;
    _minExperience = widget.minExperience ?? 0;

    if (widget.showAnimations) {
      _setupAnimations();
    }
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    if (widget.showAnimations) {
      _animationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _buildContent(),
            ),
          );
        },
      );
    }

    return _buildContent();
  }

  /// بناء محتوى الفلاتر
  Widget _buildContent() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          _buildGovernorateFilter(),
          const SizedBox(height: 16),
          _buildSpecialtyFilter(),
          const SizedBox(height: 16),
          _buildRatingFilter(),
          const SizedBox(height: 16),
          _buildAvailabilityFilter(),
          const SizedBox(height: 16),
          _buildExperienceFilter(),
          const SizedBox(height: 24),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// بناء رأس الفلاتر
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.filter_list, color: AppColors.primary, size: 24),
        const SizedBox(width: 8),
        Text(
          'فلترة الخبراء',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          color: AppColors.textSecondary,
        ),
      ],
    );
  }

  /// بناء فلتر المحافظة
  Widget _buildGovernorateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المحافظة',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedGovernorate,
              hint: Text(
                'اختر المحافظة',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              isExpanded: true,
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('جميع المحافظات'),
                ),
                ..._yemenGovernorates.map((governorate) {
                  return DropdownMenuItem<String>(
                    value: governorate,
                    child: Text(governorate),
                  );
                }),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedGovernorate = value;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء فلتر التخصص
  Widget _buildSpecialtyFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التخصص',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedSpecialty,
              hint: Text(
                'اختر التخصص',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              isExpanded: true,
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('جميع التخصصات'),
                ),
                const DropdownMenuItem<String>(
                  value: 'تخصص عام',
                  child: Text('تخصص عام'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSpecialty = value;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// بناء فلتر التقييم
  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقييم الأدنى: ${_minRating.toStringAsFixed(1)} نجمة',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.primary,
            inactiveTrackColor: AppColors.primary.withValues(alpha: 0.3),
            thumbColor: AppColors.primary,
            overlayColor: AppColors.primary.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: _minRating,
            min: 0.0,
            max: 5.0,
            divisions: 10,
            onChanged: (value) {
              setState(() {
                _minRating = value;
              });
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('0', style: AppTextStyles.bodySmall),
            Text('5', style: AppTextStyles.bodySmall),
          ],
        ),
      ],
    );
  }

  /// بناء فلتر التوفر
  Widget _buildAvailabilityFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حالة التوفر',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(child: _buildAvailabilityChip('الكل', null)),
            const SizedBox(width: 8),
            Expanded(child: _buildAvailabilityChip('متاح', true)),
            const SizedBox(width: 8),
            Expanded(child: _buildAvailabilityChip('مشغول', false)),
          ],
        ),
      ],
    );
  }

  /// بناء رقاقة التوفر
  Widget _buildAvailabilityChip(String label, bool? value) {
    final isSelected = _isAvailable == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _isAvailable = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isSelected ? Colors.white : AppColors.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// بناء فلتر سنوات الخبرة
  Widget _buildExperienceFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'سنوات الخبرة الأدنى: $_minExperience سنة',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppColors.accent,
            inactiveTrackColor: AppColors.accent.withValues(alpha: 0.3),
            thumbColor: AppColors.accent,
            overlayColor: AppColors.accent.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: _minExperience.toDouble(),
            min: 0,
            max: 30,
            divisions: 30,
            onChanged: (value) {
              setState(() {
                _minExperience = value.round();
              });
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('0', style: AppTextStyles.bodySmall),
            Text('30+', style: AppTextStyles.bodySmall),
          ],
        ),
      ],
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _clearFilters,
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.textSecondary),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'مسح الفلاتر',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'تطبيق الفلاتر',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// مسح جميع الفلاتر
  void _clearFilters() {
    setState(() {
      _selectedGovernorate = null;
      _selectedSpecialty = null;
      _minRating = 0.0;
      _isAvailable = null;
      _minExperience = 0;
    });

    widget.onFiltersCleared?.call();
    Navigator.of(context).pop();
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    widget.onFiltersApplied?.call(
      governorate: _selectedGovernorate,
      specialty: _selectedSpecialty,
      minRating: _minRating > 0 ? _minRating : null,
      isAvailable: _isAvailable,
      minExperience: _minExperience > 0 ? _minExperience : null,
    );

    Navigator.of(context).pop();
  }
}

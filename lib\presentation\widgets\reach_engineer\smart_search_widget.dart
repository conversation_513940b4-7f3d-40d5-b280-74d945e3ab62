import 'package:flutter/material.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// ويدجت البحث الذكي المحسن
///
/// يوفر بحث متقدم مع اقتراحات تلقائية وبحث صوتي
/// وفق توصيات المراجعة الشاملة لتحسين تجربة المستخدم
class SmartSearchWidget extends StatefulWidget {
  /// دالة استدعاء عند تغيير النص
  final Function(String)? onSearchChanged;

  /// دالة استدعاء عند النقر على اقتراح
  final Function(String)? onSuggestionSelected;

  /// دالة استدعاء عند النقر على الفلاتر
  final VoidCallback? onFiltersPressed;

  /// قائمة الاقتراحات
  final List<String> suggestions;

  /// عدد الفلاتر المطبقة
  final int appliedFiltersCount;

  /// هل يظهر البحث الصوتي
  final bool enableVoiceSearch;

  /// نص التلميح
  final String hintText;

  const SmartSearchWidget({
    super.key,
    this.onSearchChanged,
    this.onSuggestionSelected,
    this.onFiltersPressed,
    this.suggestions = const [],
    this.appliedFiltersCount = 0,
    this.enableVoiceSearch = true,
    this.hintText = 'ابحث عن خبير زراعي...',
  });

  @override
  State<SmartSearchWidget> createState() => _SmartSearchWidgetState();
}

class _SmartSearchWidgetState extends State<SmartSearchWidget>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  bool _showSuggestions = false;
  List<String> _filteredSuggestions = [];

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    _setupAnimations();
    _setupSpeech();
    _setupListeners();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  void _setupSpeech() {
    // تم تعطيل البحث الصوتي مؤقتاً
  }

  void _setupListeners() {
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _onTextChanged() {
    final query = _controller.text;
    widget.onSearchChanged?.call(query);
    _updateSuggestions(query);
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _animationController.forward();
      _updateSuggestions(_controller.text);
    } else {
      _animationController.reverse();
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _updateSuggestions(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredSuggestions = widget.suggestions.take(5).toList();
        _showSuggestions = widget.suggestions.isNotEmpty;
      });
    } else {
      setState(() {
        _filteredSuggestions =
            widget.suggestions
                .where(
                  (suggestion) =>
                      suggestion.toLowerCase().contains(query.toLowerCase()),
                )
                .take(5)
                .toList();
        _showSuggestions = _filteredSuggestions.isNotEmpty;
      });
    }
  }

  Future<void> _startListening() async {
    // تم تعطيل البحث الصوتي مؤقتاً
    // سيتم إضافته في المرحلة الثانية
  }

  void _stopListening() {
    // تم تعطيل البحث الصوتي مؤقتاً
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [_buildSearchBar(), if (_showSuggestions) _buildSuggestions()],
    );
  }

  /// بناء شريط البحث الرئيسي
  Widget _buildSearchBar() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                _buildSearchIcon(),
                Expanded(child: _buildTextField()),
                if (widget.enableVoiceSearch) _buildVoiceButton(),
                _buildFiltersButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء أيقونة البحث
  Widget _buildSearchIcon() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Icon(Icons.search, color: AppColors.textSecondary, size: 24),
    );
  }

  /// بناء حقل النص
  Widget _buildTextField() {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      style: AppTextStyles.bodyMedium,
      decoration: InputDecoration(
        hintText: widget.hintText,
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(vertical: 16),
      ),
      textInputAction: TextInputAction.search,
      onSubmitted: (value) {
        widget.onSearchChanged?.call(value);
        _focusNode.unfocus();
      },
    );
  }

  /// بناء زر البحث الصوتي (معطل مؤقتاً)
  Widget _buildVoiceButton() {
    return GestureDetector(
      onTap: () {
        // سيتم تفعيل البحث الصوتي في المرحلة الثانية
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('البحث الصوتي سيتوفر قريباً'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        margin: const EdgeInsets.only(right: 8),
        decoration: const BoxDecoration(
          color: AppColors.surface,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.mic_none,
          color: AppColors.textSecondary,
          size: 20,
        ),
      ),
    );
  }

  /// بناء زر الفلاتر
  Widget _buildFiltersButton() {
    return GestureDetector(
      onTap: widget.onFiltersPressed,
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          color:
              widget.appliedFiltersCount > 0
                  ? AppColors.primary
                  : AppColors.surface,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Stack(
          children: [
            Icon(
              Icons.tune,
              color:
                  widget.appliedFiltersCount > 0
                      ? Colors.white
                      : AppColors.textSecondary,
              size: 20,
            ),
            if (widget.appliedFiltersCount > 0)
              Positioned(
                top: -2,
                right: -2,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '${widget.appliedFiltersCount}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الاقتراحات
  Widget _buildSuggestions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children:
            _filteredSuggestions.map((suggestion) {
              return ListTile(
                leading: Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                title: Text(suggestion, style: AppTextStyles.bodyMedium),
                onTap: () {
                  _controller.text = suggestion;
                  widget.onSuggestionSelected?.call(suggestion);
                  setState(() {
                    _showSuggestions = false;
                  });
                  _focusNode.unfocus();
                },
              );
            }).toList(),
      ),
    );
  }
}

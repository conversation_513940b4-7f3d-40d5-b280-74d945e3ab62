
import '../../../imports.dart';

/// صفحة تفاصيل الاستشارة
///
/// تعرض تفاصيل الاستشارة كاملة مع إمكانية التقييم والتفاعل
class ConsultationDetailsPage extends StatefulWidget {
  /// بيانات الاستشارة
  final ConsultationModel consultation;

  /// بيانات الخبير
  final AgriculturalExpertModel expert;

  /// معرف المزارع الحالي
  final String? currentFarmerId;

  /// اسم المزارع الحالي
  final String? currentFarmerName;

  /// إنشاء صفحة تفاصيل الاستشارة
  const ConsultationDetailsPage({
    super.key,
    required this.consultation,
    required this.expert,
    this.currentFarmerId,
    this.currentFarmerName,
  });

  @override
  State<ConsultationDetailsPage> createState() =>
      _ConsultationDetailsPageState();
}

class _ConsultationDetailsPageState extends State<ConsultationDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الاستشارة'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // معلومات الاستشارة
            _buildConsultationInfo(),

            // معلومات الخبير
            _buildExpertInfo(),

            // الرد (إذا كان موجوداً)
            if (widget.consultation.response != null) _buildResponseSection(),

            // قسم التقييم
            if (_canShowRating())
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تقييم الاستشارة',
                      style: AppTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'يمكنك تقييم هذه الاستشارة بعد اكتمالها',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        // TODO: تطبيق نظام التقييم
                      },
                      child: const Text('إضافة تقييم'),
                    ),
                  ],
                ),
              ),

            // أزرار الإجراءات
            _buildActionButtons(),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الاستشارة
  Widget _buildConsultationInfo() {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(Icons.help_outline, color: AppColors.primary, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.consultation.title,
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الفئة والأولوية
            Row(
              children: [
                _buildInfoChip(
                  label: widget.consultation.category,
                  icon: Icons.category,
                  color: AppColors.info,
                ),
                const SizedBox(width: 12),
                _buildInfoChip(
                  label: _getPriorityText(),
                  icon: Icons.priority_high,
                  color: _getPriorityColor(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الوصف
            Text(
              'الوصف:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.consultation.description,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),

            // الصور (إذا كانت موجودة)
            if (widget.consultation.images.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'الصور المرفقة:',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              _buildImageGallery(),
            ],

            const SizedBox(height: 16),

            // معلومات التوقيت والحالة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatusChip(),
                Text(
                  _formatDate(widget.consultation.createdAt),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الخبير
  Widget _buildExpertInfo() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الخبير المختص',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: NetworkImage(widget.expert.profileImage),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.expert.name,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.expert.specialization,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.star, color: AppColors.warning, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            widget.expert.rating.toStringAsFixed(1),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${widget.expert.experienceYears} سنة خبرة',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الرد
  Widget _buildResponseSection() {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.reply, color: AppColors.success, size: 24),
                const SizedBox(width: 12),
                Text(
                  'رد الخبير',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Text(
              widget.consultation.response!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),

            if (widget.consultation.respondedAt != null) ...[
              const SizedBox(height: 12),
              Text(
                'تاريخ الرد: ${_formatDate(widget.consultation.respondedAt!)}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // زر المحادثة
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _startChat,
              icon: const Icon(Icons.chat),
              label: const Text('بدء محادثة مع الخبير'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.info,
                foregroundColor: AppColors.onInfo,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // أزرار إضافية
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareConsultation,
                  icon: const Icon(Icons.share),
                  label: const Text('مشاركة'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _reportConsultation,
                  icon: const Icon(Icons.report),
                  label: const Text('إبلاغ'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة المعلومات
  Widget _buildInfoChip({
    required String label,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة الحالة
  Widget _buildStatusChip() {
    final statusInfo = _getStatusInfo();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusInfo['color'].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(statusInfo['icon'], color: statusInfo['color'], size: 14),
          const SizedBox(width: 4),
          Text(
            statusInfo['text'],
            style: AppTextStyles.bodySmall.copyWith(
              color: statusInfo['color'],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معرض الصور
  Widget _buildImageGallery() {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.consultation.images.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                widget.consultation.images[index],
                width: 100,
                height: 100,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 100,
                    height: 100,
                    color: AppColors.surface,
                    child: Icon(
                      Icons.image_not_supported,
                      color: AppColors.textSecondary,
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  /// التحقق من إمكانية عرض التقييم
  bool _canShowRating() {
    return widget.currentFarmerId != null &&
        widget.currentFarmerName != null &&
        widget.consultation.farmerId == widget.currentFarmerId &&
        (widget.consultation.status == ConsultationStatus.completed ||
            widget.consultation.status == ConsultationStatus.responded);
  }

  /// الحصول على نص الأولوية
  String _getPriorityText() {
    switch (widget.consultation.priority) {
      case ConsultationPriority.normal:
        return 'عادية';
      case ConsultationPriority.urgent:
        return 'عاجلة';
      case ConsultationPriority.emergency:
        return 'طارئة';
    }
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor() {
    switch (widget.consultation.priority) {
      case ConsultationPriority.normal:
        return AppColors.textSecondary;
      case ConsultationPriority.urgent:
        return AppColors.warning;
      case ConsultationPriority.emergency:
        return AppColors.error;
    }
  }

  /// الحصول على معلومات الحالة
  Map<String, dynamic> _getStatusInfo() {
    switch (widget.consultation.status) {
      case ConsultationStatus.pending:
        return {
          'text': 'في الانتظار',
          'color': AppColors.warning,
          'icon': Icons.pending,
        };
      case ConsultationStatus.responded:
        return {
          'text': 'تم الرد',
          'color': AppColors.success,
          'icon': Icons.reply,
        };
      case ConsultationStatus.completed:
        return {
          'text': 'مكتملة',
          'color': AppColors.success,
          'icon': Icons.check_circle,
        };
      case ConsultationStatus.cancelled:
        return {
          'text': 'ملغية',
          'color': AppColors.textSecondary,
          'icon': Icons.cancel,
        };
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} - ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// بدء المحادثة
  void _startChat() {
    // TODO: تنفيذ بدء المحادثة
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('بدء المحادثة - قيد التطوير')));
  }

  /// مشاركة الاستشارة
  void _shareConsultation() {
    // TODO: تنفيذ مشاركة الاستشارة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الاستشارة - قيد التطوير')),
    );
  }

  /// إبلاغ عن الاستشارة
  void _reportConsultation() {
    // TODO: تنفيذ الإبلاغ
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('الإبلاغ - قيد التطوير')));
  }
}

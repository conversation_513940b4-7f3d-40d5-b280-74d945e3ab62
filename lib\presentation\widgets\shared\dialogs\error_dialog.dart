import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

import '../../../../core/constants/assets_colors.dart';
import '../../../../core/constants/assets_fonts.dart';
import '../../../../core/constants/text_styles.dart';

/// حوار الخطأ المتحرك
///
/// يعرض حوار خطأ مع رسوم متحركة لتحسين تجربة المستخدم
/// عند حدوث أخطاء في التطبيق.
class AnimatedErrorDialog extends StatelessWidget {
  /// عنوان الخطأ
  final String title;

  /// رسالة الخطأ
  final String message;

  /// دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة
  final VoidCallback? onRetry;

  /// مسار ملف الرسوم المتحركة (اختياري)
  final String animationAsset;

  /// إنشاء حوار خطأ متحرك
  ///
  /// المعلمات:
  /// - [title]: عنوان الخطأ
  /// - [message]: رسالة الخطأ
  /// - [onRetry]: دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة (اختياري)
  /// - [animationAsset]: مسار ملف الرسوم المتحركة (اختياري)
  const AnimatedErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.animationAsset = 'assets/animations/error.json',
  });

  /// عرض حوار الخطأ
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  /// - [title]: عنوان الخطأ
  /// - [message]: رسالة الخطأ
  /// - [onRetry]: دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة (اختياري)
  /// - [animationAsset]: مسار ملف الرسوم المتحركة (اختياري)
  static Future<void> show({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onRetry,
    String animationAsset = 'assets/animations/error.json',
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AnimatedErrorDialog(
          title: title,
          message: message,
          onRetry: onRetry,
          animationAsset: animationAsset,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AssetsColors.kWhite,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AssetsColors.primary.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رسوم متحركة للخطأ
            SizedBox(
              height: 120,
              child: Lottie.asset(
                animationAsset,
                fit: BoxFit.contain,
                repeat: true,
              ),
            ),
            const SizedBox(height: 20),
            // عنوان الخطأ
            Text(
              title,
              style: TextStyles.of(context).headlineMedium(
                fontSize: 18,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            // رسالة الخطأ
            Text(
              message,
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey100,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر إغلاق
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(foregroundColor: Colors.grey),
                  child: Text(
                    'إغلاق',
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 14,
                      fontFamily: AssetsFonts.cairo,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (onRetry != null) ...[
                  const SizedBox(width: 10),
                  // زر إعادة المحاولة
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      onRetry!();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AssetsColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: Text(
                      'إعادة المحاولة',
                      style: TextStyles.of(context).bodyMedium(
                        fontSize: 14,
                        fontFamily: AssetsFonts.cairo,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}

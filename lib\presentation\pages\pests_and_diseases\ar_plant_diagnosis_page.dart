import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';

/// صفحة التشخيص بالواقع المعزز
///
/// ميزة فريدة عالمياً تستخدم الواقع المعزز لتشخيص الآفات والأمراض
/// مع عرض المعلومات والتوصيات مباشرة على الكاميرا
class ARPlantDiagnosisPage extends StatefulWidget {
  const ARPlantDiagnosisPage({super.key});

  @override
  State<ARPlantDiagnosisPage> createState() => _ARPlantDiagnosisPageState();
}

class _ARPlantDiagnosisPageState extends State<ARPlantDiagnosisPage>
    with TickerProviderStateMixin {

  late AnimationController _scanAnimationController;
  late AnimationController _pulseAnimationController;
  late Animation<double> _scanAnimation;
  late Animation<double> _pulseAnimation;

  bool _isCameraReady = false;
  bool _isScanning = false;
  List<ARDetection> _detections = [];
  ARMode _currentMode = ARMode.realTime;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeCamera();
  }

  @override
  void dispose() {
    _scanAnimationController.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _scanAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scanAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scanAnimationController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    _scanAnimationController.repeat();
    _pulseAnimationController.repeat(reverse: true);
  }

  Future<void> _initializeCamera() async {
    try {
      // محاكاة تهيئة الكاميرا
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isCameraReady = true;
        });
        _startRealTimeDetection();
      }
    } catch (e) {
      // خطأ في تهيئة الكاميرا: $e
    }
  }

  void _startRealTimeDetection() {
    // محاكاة الكشف في الوقت الفعلي
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _detections = _generateSampleDetections();
        });
      }
    });
  }

  List<ARDetection> _generateSampleDetections() {
    return [
      ARDetection(
        id: '1',
        type: DetectionType.disease,
        name: 'اللفحة المتأخرة',
        confidence: 0.89,
        position: const Offset(0.3, 0.4),
        size: const Size(0.2, 0.15),
        severity: DetectionSeverity.high,
        description: 'مرض فطري خطير يصيب الطماطم',
        treatment: 'رش مبيد فطري نحاسي فوراً',
        color: Colors.red,
      ),
      ARDetection(
        id: '2',
        type: DetectionType.pest,
        name: 'المن الأخضر',
        confidence: 0.76,
        position: const Offset(0.6, 0.3),
        size: const Size(0.15, 0.1),
        severity: DetectionSeverity.medium,
        description: 'حشرة ماصة تضعف النبات',
        treatment: 'استخدام الصابون المخفف',
        color: Colors.orange,
      ),
      ARDetection(
        id: '3',
        type: DetectionType.healthyArea,
        name: 'منطقة سليمة',
        confidence: 0.95,
        position: const Offset(0.1, 0.7),
        size: const Size(0.25, 0.2),
        severity: DetectionSeverity.none,
        description: 'نسيج نباتي صحي',
        treatment: 'الحفاظ على الرعاية الحالية',
        color: Colors.green,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // معاينة الكاميرا
          if (_isCameraReady)
            Positioned.fill(
              child: Container(
                color: Colors.black87,
                child: Center(
                  child: Text(
                    'معاينة الكاميرا',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontFamily: AssetsFonts.cairo,
                    ),
                  ),
                ),
              ),
            )
          else
            _buildCameraLoading(),

          // طبقة الواقع المعزز
          if (_isCameraReady) _buildAROverlay(),

          // واجهة التحكم
          _buildControlInterface(),
        ],
      ),
    );
  }

  /// بناء شاشة تحميل الكاميرا
  Widget _buildCameraLoading() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AssetsColors.primary.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.camera_alt,
                      color: AssetsColors.primary,
                      size: 40,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              'جاري تهيئة كاميرا الواقع المعزز...',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء طبقة الواقع المعزز
  Widget _buildAROverlay() {
    return Positioned.fill(
      child: CustomPaint(
        painter: AROverlayPainter(
          detections: _detections,
          scanAnimation: _scanAnimation,
          isScanning: _isScanning,
        ),
        child: Stack(
          children: [
            // مؤشرات الكشف
            ..._detections.map((detection) => _buildDetectionMarker(detection)),

            // خط المسح
            if (_isScanning)
              AnimatedBuilder(
                animation: _scanAnimation,
                builder: (context, child) {
                  return Positioned(
                    top: MediaQuery.of(context).size.height * _scanAnimation.value,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 2,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.transparent,
                            Colors.cyan,
                            Colors.cyan,
                            Colors.transparent,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.cyan,
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر الكشف
  Widget _buildDetectionMarker(ARDetection detection) {
    final screenSize = MediaQuery.of(context).size;
    final left = detection.position.dx * screenSize.width;
    final top = detection.position.dy * screenSize.height;
    final width = detection.size.width * screenSize.width;
    final height = detection.size.height * screenSize.height;

    return Positioned(
      left: left,
      top: top,
      child: GestureDetector(
        onTap: () => _showDetectionDetails(detection),
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            border: Border.all(
              color: detection.color,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Stack(
            children: [
              // خلفية شفافة
              Container(
                decoration: BoxDecoration(
                  color: detection.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
              ),

              // تسمية الكشف
              Positioned(
                top: -25,
                left: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: detection.color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getDetectionIcon(detection.type),
                        color: Colors.white,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        detection.name,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${(detection.confidence * 100).toInt()}%',
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          color: Colors.white,
                          fontSize: 9,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // نقاط الزوايا
              ..._buildCornerPoints(detection.color),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء نقاط الزوايا
  List<Widget> _buildCornerPoints(Color color) {
    return [
      // الزاوية العلوية اليسرى
      Positioned(
        top: -2,
        left: -2,
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
      ),
      // الزاوية العلوية اليمنى
      Positioned(
        top: -2,
        right: -2,
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
      ),
      // الزاوية السفلية اليسرى
      Positioned(
        bottom: -2,
        left: -2,
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
      ),
      // الزاوية السفلية اليمنى
      Positioned(
        bottom: -2,
        right: -2,
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
      ),
    ];
  }

  /// بناء واجهة التحكم
  Widget _buildControlInterface() {
    return Column(
      children: [
        // شريط التطبيق العلوي
        SafeArea(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
                Expanded(
                  child: Text(
                    'التشخيص بالواقع المعزز',
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.info_outline, color: Colors.white),
                  onPressed: _showARInfo,
                ),
              ],
            ),
          ),
        ),

        const Spacer(),

        // أزرار التحكم السفلية
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withOpacity(0.7),
                Colors.black.withOpacity(0.9),
              ],
            ),
          ),
          child: Column(
            children: [
              // معلومات الكشف
              if (_detections.isNotEmpty) _buildDetectionSummary(),

              const SizedBox(height: 20),

              // أزرار التحكم
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton(
                    icon: _currentMode == ARMode.realTime ? Icons.pause : Icons.play_arrow,
                    label: _currentMode == ARMode.realTime ? 'إيقاف' : 'تشغيل',
                    onPressed: _toggleMode,
                    color: Colors.blue,
                  ),
                  _buildControlButton(
                    icon: Icons.camera_alt,
                    label: 'التقاط',
                    onPressed: _captureFrame,
                    color: Colors.white,
                    isLarge: true,
                  ),
                  _buildControlButton(
                    icon: Icons.refresh,
                    label: 'إعادة مسح',
                    onPressed: _rescan,
                    color: Colors.green,
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء ملخص الكشف
  Widget _buildDetectionSummary() {
    final diseaseCount = _detections.where((d) => d.type == DetectionType.disease).length;
    final pestCount = _detections.where((d) => d.type == DetectionType.pest).length;
    final healthyCount = _detections.where((d) => d.type == DetectionType.healthyArea).length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildSummaryItem('أمراض', diseaseCount, Colors.red, Icons.coronavirus),
          _buildSummaryItem('آفات', pestCount, Colors.orange, Icons.bug_report),
          _buildSummaryItem('سليم', healthyCount, Colors.green, Icons.check_circle),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem(String label, int count, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// بناء زر تحكم
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
    bool isLarge = false,
  }) {
    return Column(
      children: [
        Container(
          width: isLarge ? 70 : 50,
          height: isLarge ? 70 : 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
            border: Border.all(color: color, width: 2),
          ),
          child: IconButton(
            icon: Icon(
              icon,
              color: color,
              size: isLarge ? 30 : 20,
            ),
            onPressed: onPressed,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// الحصول على أيقونة نوع الكشف
  IconData _getDetectionIcon(DetectionType type) {
    switch (type) {
      case DetectionType.disease:
        return Icons.coronavirus;
      case DetectionType.pest:
        return Icons.bug_report;
      case DetectionType.healthyArea:
        return Icons.check_circle;
      case DetectionType.nutrientDeficiency:
        return Icons.water_drop;
    }
  }

  /// تبديل الوضع
  void _toggleMode() {
    setState(() {
      if (_currentMode == ARMode.realTime) {
        _currentMode = ARMode.paused;
      } else {
        _currentMode = ARMode.realTime;
        _startRealTimeDetection();
      }
    });
  }

  /// التقاط إطار
  void _captureFrame() async {
    try {
      // محاكاة التقاط الصورة
      await Future.delayed(const Duration(milliseconds: 500));

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حفظ الصورة مع التشخيص',
            style: TextStyle(fontFamily: AssetsFonts.cairo),
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'خطأ في حفظ الصورة',
            style: TextStyle(fontFamily: AssetsFonts.cairo),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إعادة المسح
  void _rescan() {
    setState(() {
      _detections.clear();
      _isScanning = true;
    });

    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isScanning = false;
        _detections = _generateSampleDetections();
      });
    });
  }

  /// عرض تفاصيل الكشف
  void _showDetectionDetails(ARDetection detection) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E293B),
        title: Row(
          children: [
            Icon(
              _getDetectionIcon(detection.type),
              color: detection.color,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                detection.name,
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: detection.color,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'مستوى الثقة: ${(detection.confidence * 100).toInt()}%',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'الوصف: ${detection.description}',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'العلاج: ${detection.treatment}',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: detection.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض معلومات الواقع المعزز
  void _showARInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E293B),
        title: Text(
          'التشخيص بالواقع المعزز',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        content: Text(
          'تقنية متقدمة تجمع بين الكاميرا والذكاء الاصطناعي لكشف الآفات والأمراض فورياً',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'فهمت',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: Colors.cyan,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج كشف الواقع المعزز
class ARDetection {
  final String id;
  final DetectionType type;
  final String name;
  final double confidence;
  final Offset position;
  final Size size;
  final DetectionSeverity severity;
  final String description;
  final String treatment;
  final Color color;

  ARDetection({
    required this.id,
    required this.type,
    required this.name,
    required this.confidence,
    required this.position,
    required this.size,
    required this.severity,
    required this.description,
    required this.treatment,
    required this.color,
  });
}

/// رسام طبقة الواقع المعزز
class AROverlayPainter extends CustomPainter {
  final List<ARDetection> detections;
  final Animation<double> scanAnimation;
  final bool isScanning;

  AROverlayPainter({
    required this.detections,
    required this.scanAnimation,
    required this.isScanning,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withOpacity(0.1)
      ..strokeWidth = 0.5;

    const spacing = 50.0;

    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }

    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// أنواع الكشف
enum DetectionType {
  disease,
  pest,
  healthyArea,
  nutrientDeficiency,
}

/// مستويات خطورة الكشف
enum DetectionSeverity {
  none,
  low,
  medium,
  high,
  critical,
}

/// أوضاع الواقع المعزز
enum ARMode {
  realTime,
  paused,
}

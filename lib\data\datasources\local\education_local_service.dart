import 'dart:convert';

import 'package:agriculture/core/constants/app_constants.dart';
import 'package:agriculture/core/utils/logging/logger_service.dart';
import 'package:agriculture/data/models/education/index.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين المحلي للتعليم والتدريب
///
/// توفر هذه الخدمة وظائف للتخزين المؤقت لبيانات التعليم والتدريب محليًا
class EducationLocalService {
  /// مفتاح تخزين فئات التعليم
  static const String _categoriesKey = '${AppConstants.prefsKeyPrefix}education_categories';

  /// مفتاح تخزين الدورات التدريبية
  static const String _coursesKey = '${AppConstants.prefsKeyPrefix}education_courses';

  /// مفتاح تخزين النصيحة اليومية
  static const String _dailyTipKey = '${AppConstants.prefsKeyPrefix}education_daily_tip';

  /// مفتاح تخزين الدروس
  static const String _lessonsKey = '${AppConstants.prefsKeyPrefix}education_lessons';

  /// مفتاح تخزين الدرس
  static const String _lessonKey = '${AppConstants.prefsKeyPrefix}education_lesson';

  /// مثيل Shared Preferences
  final SharedPreferences _prefs;

  /// إنشاء خدمة التخزين المحلي للتعليم والتدريب
  ///
  /// المعلمات:
  /// - [prefs]: مثيل Shared Preferences
  EducationLocalService(this._prefs);

  /// تخزين فئات التعليم محليًا
  ///
  /// المعلمات:
  /// - [categories]: قائمة فئات التعليم
  Future<void> cacheEducationCategories(List<EducationCategoryModel> categories) async {
    try {
      final List<Map<String, dynamic>> categoriesJson = categories
          .map((category) => {
                'id': category.id,
                'name': category.name,
                'description': category.description,
                'imageUrl': category.imageUrl,
                'order': category.order,
                'itemsCount': category.itemsCount,
              })
          .toList();

      await _prefs.setString(_categoriesKey, jsonEncode(categoriesJson));

      LoggerService.debug(
        'تم تخزين فئات التعليم محليًا (${categories.length} فئة)',
        tag: 'EducationLocalService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين فئات التعليم محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
    }
  }

  /// الحصول على فئات التعليم المخزنة محليًا
  Future<List<EducationCategoryModel>?> getCachedEducationCategories() {
    try {
      final String? categoriesJson = _prefs.getString(_categoriesKey);

      if (categoriesJson == null) {
        return Future.value(null);
      }

      final List<dynamic> decodedJson = jsonDecode(categoriesJson);

      final List<EducationCategoryModel> categories = decodedJson
          .map((item) => EducationCategoryModel(
                id: item['id'],
                name: item['name'],
                description: item['description'],
                imageUrl: item['imageUrl'],
                order: item['order'],
                itemsCount: item['itemsCount'],
              ))
          .toList();

      LoggerService.debug(
        'تم استرجاع فئات التعليم من التخزين المحلي (${categories.length} فئة)',
        tag: 'EducationLocalService',
      );

      return Future.value(categories);
    } catch (e) {
      LoggerService.error(
        'خطأ في استرجاع فئات التعليم من التخزين المحلي',
        error: e,
        tag: 'EducationLocalService',
      );
      return Future.value(null);
    }
  }

  /// تخزين الدورات التدريبية محليًا
  ///
  /// المعلمات:
  /// - [courses]: قائمة الدورات التدريبية
  /// - [categoryId]: معرف الفئة (اختياري)
  Future<void> cacheCourses(List<CourseModel> courses, {String? categoryId}) async {
    try {
      final String key = categoryId != null
          ? '${_coursesKey}_$categoryId'
          : _coursesKey;

      final List<Map<String, dynamic>> coursesJson = courses
          .map((course) => {
                'id': course.id,
                'title': course.title,
                'description': course.description,
                'imageUrl': course.imageUrl,
                'level': course.level,
                'durationMinutes': course.durationMinutes,
                'lessonsCount': course.lessonsCount,
                'lessonIds': course.lessonIds,
                'createdAt': course.createdAt.millisecondsSinceEpoch,
                'updatedAt': course.updatedAt.millisecondsSinceEpoch,
                'categoryId': course.categoryId,
                'instructorName': course.instructorName,
                'viewsCount': course.viewsCount,
              })
          .toList();

      await _prefs.setString(key, jsonEncode(coursesJson));

      LoggerService.debug(
        'تم تخزين الدورات التدريبية محليًا (${courses.length} دورة)${categoryId != null ? ' للفئة $categoryId' : ''}',
        tag: 'EducationLocalService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين الدورات التدريبية محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
    }
  }

  /// الحصول على الدورات التدريبية المخزنة محليًا
  ///
  /// المعلمات:
  /// - [categoryId]: معرف الفئة (اختياري)
  Future<List<CourseModel>?> getCachedCourses({String? categoryId}) {
    try {
      final String key = categoryId != null
          ? '${_coursesKey}_$categoryId'
          : _coursesKey;

      final String? coursesJson = _prefs.getString(key);

      if (coursesJson == null) {
        return Future.value(null);
      }

      final List<dynamic> decodedJson = jsonDecode(coursesJson);

      final List<CourseModel> courses = decodedJson
          .map((item) => CourseModel(
                id: item['id'],
                title: item['title'],
                description: item['description'],
                imageUrl: item['imageUrl'],
                level: item['level'],
                durationMinutes: item['durationMinutes'],
                lessonsCount: item['lessonsCount'],
                lessonIds: List<String>.from(item['lessonIds']),
                createdAt: DateTime.fromMillisecondsSinceEpoch(item['createdAt']),
                updatedAt: DateTime.fromMillisecondsSinceEpoch(item['updatedAt']),
                categoryId: item['categoryId'],
                instructorName: item['instructorName'],
                viewsCount: item['viewsCount'],
              ))
          .toList();

      LoggerService.debug(
        'تم استرجاع الدورات التدريبية من التخزين المحلي (${courses.length} دورة)${categoryId != null ? ' للفئة $categoryId' : ''}',
        tag: 'EducationLocalService',
      );

      return Future.value(courses);
    } catch (e) {
      LoggerService.error(
        'خطأ في استرجاع الدورات التدريبية من التخزين المحلي',
        error: e,
        tag: 'EducationLocalService',
      );
      return Future.value(null);
    }
  }

  /// تخزين النصيحة اليومية محليًا
  ///
  /// المعلمات:
  /// - [tip]: النصيحة اليومية
  Future<void> cacheDailyTip(DailyTipModel tip) async {
    try {
      final Map<String, dynamic> tipJson = {
        'id': tip.id,
        'title': tip.title,
        'content': tip.content,
        'imageUrl': tip.imageUrl,
        'date': tip.date.millisecondsSinceEpoch,
        'categoryId': tip.categoryId,
        'tags': tip.tags,
        'moreInfoUrl': tip.moreInfoUrl,
      };

      await _prefs.setString(_dailyTipKey, jsonEncode(tipJson));

      LoggerService.debug(
        'تم تخزين النصيحة اليومية محليًا',
        tag: 'EducationLocalService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين النصيحة اليومية محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
    }
  }

  /// الحصول على النصيحة اليومية المخزنة محليًا
  Future<DailyTipModel?> getCachedDailyTip() {
    try {
      final String? tipJson = _prefs.getString(_dailyTipKey);

      if (tipJson == null) {
        return Future.value(null);
      }

      final Map<String, dynamic> decodedJson = jsonDecode(tipJson);

      // التحقق من أن النصيحة لليوم الحالي
      final DateTime tipDate = DateTime.fromMillisecondsSinceEpoch(decodedJson['date']);
      final DateTime now = DateTime.now();

      if (tipDate.year != now.year || tipDate.month != now.month || tipDate.day != now.day) {
        // النصيحة ليست لليوم الحالي
        return Future.value(null);
      }

      final DailyTipModel tip = DailyTipModel(
        id: decodedJson['id'],
        title: decodedJson['title'],
        content: decodedJson['content'],
        imageUrl: decodedJson['imageUrl'],
        date: tipDate,
        categoryId: decodedJson['categoryId'],
        tags: List<String>.from(decodedJson['tags']),
        moreInfoUrl: decodedJson['moreInfoUrl'],
      );

      LoggerService.debug(
        'تم استرجاع النصيحة اليومية من التخزين المحلي',
        tag: 'EducationLocalService',
      );

      return Future.value(tip);
    } catch (e) {
      LoggerService.error(
        'خطأ في استرجاع النصيحة اليومية من التخزين المحلي',
        error: e,
        tag: 'EducationLocalService',
      );
      return Future.value(null);
    }
  }

  /// تخزين دروس الدورة التدريبية محليًا
  ///
  /// المعلمات:
  /// - [lessons]: قائمة الدروس
  /// - [courseId]: معرف الدورة التدريبية
  Future<void> cacheCourseLessons(List<LessonModel> lessons, String courseId) async {
    try {
      final lessonsJson = lessons.map((lesson) => lesson.toJson()).toList();
      final jsonString = jsonEncode(lessonsJson);
      await _prefs.setString('${_lessonsKey}_$courseId', jsonString);

      LoggerService.info(
        'تم تخزين دروس الدورة التدريبية محليًا',
        tag: 'EducationLocalService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين دروس الدورة التدريبية محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
    }
  }

  /// الحصول على دروس الدورة التدريبية المخزنة محليًا
  ///
  /// المعلمات:
  /// - [courseId]: معرف الدورة التدريبية
  Future<List<LessonModel>?> getCachedCourseLessons(String courseId) {
    try {
      final jsonString = _prefs.getString('${_lessonsKey}_$courseId');
      if (jsonString == null) {
        return Future.value(null);
      }

      final lessonsJson = jsonDecode(jsonString) as List;
      final lessons = lessonsJson
          .map((json) => LessonModel.fromJson(json as Map<String, dynamic>))
          .toList();

      return Future.value(lessons);
    } catch (e) {
      LoggerService.error(
        'خطأ في جلب دروس الدورة التدريبية المخزنة محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
      return Future.value(null);
    }
  }

  /// تخزين درس محليًا
  ///
  /// المعلمات:
  /// - [lesson]: الدرس
  Future<void> cacheLesson(LessonModel lesson) async {
    try {
      final jsonString = jsonEncode(lesson.toJson());
      await _prefs.setString('${_lessonKey}_${lesson.id}', jsonString);

      LoggerService.info(
        'تم تخزين الدرس محليًا',
        tag: 'EducationLocalService',
      );
    } catch (e) {
      LoggerService.error(
        'خطأ في تخزين الدرس محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
    }
  }

  /// الحصول على درس مخزن محليًا
  ///
  /// المعلمات:
  /// - [lessonId]: معرف الدرس
  Future<LessonModel?> getCachedLesson(String lessonId) {
    try {
      final jsonString = _prefs.getString('${_lessonKey}_$lessonId');
      if (jsonString == null) {
        return Future.value(null);
      }

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final lesson = LessonModel.fromJson(json);

      return Future.value(lesson);
    } catch (e) {
      LoggerService.error(
        'خطأ في جلب الدرس المخزن محليًا',
        error: e,
        tag: 'EducationLocalService',
      );
      return Future.value(null);
    }
  }
}

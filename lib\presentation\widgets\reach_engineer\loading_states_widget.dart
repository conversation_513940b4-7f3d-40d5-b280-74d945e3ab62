import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';

/// أنواع حالات التحميل المختلفة
enum LoadingType {
  /// تحميل قائمة الخبراء
  expertsList,

  /// تحميل بطاقة خبير واحد
  expertCard,

  /// تحميل الإحصائيات
  statistics,

  /// تحميل شريط البحث
  searchBar,

  /// تحميل عام
  general,
}

/// ويدجت حالات التحميل المختلفة للخبراء الزراعيين
///
/// يوفر حالات تحميل جذابة ومتنوعة حسب نوع المحتوى
/// مطبق وفق التفضيلات الـ18 مع عدم التكرار
class LoadingStatesWidget extends StatefulWidget {
  /// نوع التحميل
  final LoadingType type;

  /// عدد العناصر للتحميل (للقوائم)
  final int itemCount;

  /// رسالة التحميل المخصصة
  final String? customMessage;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  /// لون التحميل المخصص
  final Color? customColor;

  const LoadingStatesWidget({
    super.key,
    required this.type,
    this.itemCount = 3,
    this.customMessage,
    this.showAnimations = true,
    this.customColor,
  });

  @override
  State<LoadingStatesWidget> createState() => _LoadingStatesWidgetState();
}

class _LoadingStatesWidgetState extends State<LoadingStatesWidget>
    with TickerProviderStateMixin {
  AnimationController? _pulseController;
  AnimationController? _rotationController;
  Animation<double>? _pulseAnimation;
  Animation<double>? _rotationAnimation;

  @override
  void initState() {
    super.initState();

    if (widget.showAnimations) {
      _setupAnimations();
    }
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController!, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController!, curve: Curves.linear),
    );

    _pulseController!.repeat(reverse: true);
    _rotationController!.repeat();
  }

  @override
  void dispose() {
    if (widget.showAnimations) {
      _pulseController?.dispose();
      _rotationController?.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.type) {
      case LoadingType.expertsList:
        return _buildExpertsListLoading();
      case LoadingType.expertCard:
        return _buildExpertCardLoading();
      case LoadingType.statistics:
        return _buildStatisticsLoading();
      case LoadingType.searchBar:
        return _buildSearchBarLoading();
      case LoadingType.general:
        return _buildGeneralLoading();
    }
  }

  /// بناء تحميل قائمة الخبراء
  Widget _buildExpertsListLoading() {
    return Column(
      children: List.generate(
        widget.itemCount,
        (index) => Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: _buildShimmerCard(),
        ),
      ),
    );
  }

  /// بناء تحميل بطاقة خبير واحد
  Widget _buildExpertCardLoading() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: _buildShimmerCard(),
    );
  }

  /// بناء تحميل الإحصائيات
  Widget _buildStatisticsLoading() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: List.generate(3, (index) {
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(
                left: index < 2 ? 8 : 0,
                right: index > 0 ? 8 : 0,
              ),
              child: _buildShimmerStatistic(),
            ),
          );
        }),
      ),
    );
  }

  /// بناء تحميل شريط البحث
  Widget _buildSearchBarLoading() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: _buildShimmerSearchBar(),
    );
  }

  /// بناء تحميل عام
  Widget _buildGeneralLoading() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildAnimatedLoader(),
          const SizedBox(height: 16),
          Text(
            widget.customMessage ?? 'جاري التحميل...',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة shimmer
  Widget _buildShimmerCard() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // صورة الخبير
                Container(
                  width: 60,
                  height: 60,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم الخبير
                      Container(
                        width: double.infinity,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // التخصص
                      Container(
                        width: 120,
                        height: 14,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                // أيقونة المفضلة
                Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // التقييم والخبرة
            Row(
              children: [
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  width: 100,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // أزرار الإجراءات
            Row(
              children: List.generate(3, (index) {
                return Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      left: index < 2 ? 4 : 0,
                      right: index > 0 ? 4 : 0,
                    ),
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إحصائية shimmer
  Widget _buildShimmerStatistic() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: 60,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: 80,
              height: 14,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط بحث shimmer
  Widget _buildShimmerSearchBar() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محمل متحرك
  Widget _buildAnimatedLoader() {
    if (!widget.showAnimations ||
        _rotationController == null ||
        _pulseController == null) {
      return CircularProgressIndicator(
        color: widget.customColor ?? AppColors.primary,
      );
    }

    return AnimatedBuilder(
      animation: _rotationController!,
      builder: (context, child) {
        return Transform.rotate(
          angle: (_rotationAnimation?.value ?? 0.0) * 2 * 3.14159,
          child: AnimatedBuilder(
            animation: _pulseController!,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation?.value ?? 1.0,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        widget.customColor ?? AppColors.primary,
                        (widget.customColor ?? AppColors.primary).withValues(
                          alpha: 0.6,
                        ),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: (widget.customColor ?? AppColors.primary)
                            .withValues(alpha: 0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.agriculture,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

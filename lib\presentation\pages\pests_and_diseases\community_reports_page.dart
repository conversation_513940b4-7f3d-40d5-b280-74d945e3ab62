
import 'package:flutter/material.dart';

import '../../../core/constants/assets_colors.dart';
import '../../../core/constants/assets_fonts.dart';

/// صفحة تقارير المجتمع للآفات والأمراض
///
/// تتيح للمزارعين الإبلاغ عن الآفات والأمراض ومشاركة الخبرات
/// والحصول على المساعدة من الخبراء والمجتمع
class CommunityReportsPage extends StatefulWidget {
  const CommunityReportsPage({super.key});

  @override
  State<CommunityReportsPage> createState() => _CommunityReportsPageState();
}

class _CommunityReportsPageState extends State<CommunityReportsPage>
    with TickerProviderStateMixin {

  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<CommunityReport> _allReports = [];
  List<CommunityReport> _filteredReports = [];
  List<ExpertResponse> _expertResponses = [];

  String _selectedFilter = 'all';
  bool _isLoading = true;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _setupControllers();
    _setupAnimations();
    _loadCommunityData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _setupControllers() {
    _tabController = TabController(length: 3, vsync: this);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _loadCommunityData() async {
    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _allReports = _generateSampleReports();
      _filteredReports = _allReports;
      _expertResponses = _generateSampleResponses();
      _isLoading = false;
    });

    _animationController.forward();
  }

  List<CommunityReport> _generateSampleReports() {
    return [
      CommunityReport(
        id: '1',
        title: 'إصابة غريبة في أوراق الطماطم',
        description: 'ظهرت بقع صفراء على أوراق الطماطم مع تجعد في الأوراق. لم أر هذه الأعراض من قبل.',
        authorName: 'أحمد محمد الزراعي',
        authorLocation: 'صنعاء، اليمن',
        cropType: 'طماطم',
        severity: ReportSeverity.medium,
        images: [
          'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
          'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
        ],
        reportDate: DateTime.now().subtract(Duration(hours: 2)),
        viewCount: 45,
        responseCount: 8,
        helpfulCount: 12,
        status: ReportStatus.open,
        tags: ['طماطم', 'أوراق صفراء', 'تجعد'],
      ),
      CommunityReport(
        id: '2',
        title: 'انتشار سريع لآفة في محصول الخيار',
        description: 'لاحظت انتشار سريع لحشرات صغيرة على نباتات الخيار. تتغذى على الأوراق وتسبب ذبولها.',
        authorName: 'فاطمة علي الحضرمي',
        authorLocation: 'المكلا، حضرموت',
        cropType: 'خيار',
        severity: ReportSeverity.high,
        images: [
          'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        ],
        reportDate: DateTime.now().subtract(Duration(days: 1)),
        viewCount: 89,
        responseCount: 15,
        helpfulCount: 23,
        status: ReportStatus.expertReviewed,
        tags: ['خيار', 'حشرات', 'ذبول'],
      ),
      CommunityReport(
        id: '3',
        title: 'مرض فطري في البطاطس - حاجة لمساعدة عاجلة',
        description: 'ظهر مرض فطري في مزرعة البطاطس وينتشر بسرعة. الأوراق تتحول للون البني وتموت.',
        authorName: 'محمد سالم الشامي',
        authorLocation: 'إب، اليمن',
        cropType: 'بطاطس',
        severity: ReportSeverity.critical,
        images: [
          'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
          'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
          'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        ],
        reportDate: DateTime.now().subtract(Duration(hours: 6)),
        viewCount: 156,
        responseCount: 28,
        helpfulCount: 45,
        status: ReportStatus.solved,
        tags: ['بطاطس', 'فطري', 'عاجل'],
      ),
      CommunityReport(
        id: '4',
        title: 'نصائح للوقاية من دودة ورق القطن',
        description: 'أشارك معكم تجربتي في الوقاية من دودة ورق القطن باستخدام طرق طبيعية فعالة.',
        authorName: 'عبدالله حسن التهامي',
        authorLocation: 'الحديدة، تهامة',
        cropType: 'قطن',
        severity: ReportSeverity.low,
        images: [
          'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400',
        ],
        reportDate: DateTime.now().subtract(Duration(days: 2)),
        viewCount: 234,
        responseCount: 42,
        helpfulCount: 78,
        status: ReportStatus.open,
        tags: ['قطن', 'وقاية', 'طبيعي'],
      ),
    ];
  }

  List<ExpertResponse> _generateSampleResponses() {
    return [
      ExpertResponse(
        id: '1',
        reportId: '1',
        expertName: 'د. سعد أحمد الزراعي',
        expertTitle: 'أخصائي أمراض النباتات',
        expertRating: 4.8,
        response: 'بناءً على الصور والوصف، يبدو أن هذه إصابة بفيروس تجعد الأوراق. أنصح بإزالة النباتات المصابة فوراً ورش المبيد الحيوي.',
        responseDate: DateTime.now().subtract(Duration(hours: 1)),
        helpfulCount: 15,
        isVerified: true,
      ),
      ExpertResponse(
        id: '2',
        reportId: '2',
        expertName: 'م. أمينة محمد الحكيمي',
        expertTitle: 'مهندسة زراعية - متخصصة آفات',
        expertRating: 4.6,
        response: 'هذه آفة المن الأخضر. يمكن مكافحتها بالصابون المخفف أو الزيوت الطبيعية. تجنب المبيدات الكيميائية في البداية.',
        responseDate: DateTime.now().subtract(Duration(hours: 3)),
        helpfulCount: 22,
        isVerified: true,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          'تقارير المجتمع',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: _showSearchDialog,
            tooltip: 'البحث',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'my_reports',
                child: Row(
                  children: [
                    Icon(Icons.person, color: AssetsColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'تقاريري',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'notifications',
                child: Row(
                  children: [
                    Icon(Icons.notifications, color: AssetsColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'الإشعارات',
                      style: TextStyle(fontFamily: AssetsFonts.cairo),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
          tabs: [
            Tab(text: 'التقارير'),
            Tab(text: 'الخبراء'),
            Tab(text: 'المناقشات'),
          ],
        ),
      ),
      body: _isLoading
          ? _buildLoadingScreen()
          : FadeTransition(
              opacity: _fadeAnimation,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildReportsTab(),
                  _buildExpertsTab(),
                  _buildDiscussionsTab(),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showNewReportDialog,
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
        icon: Icon(Icons.add),
        label: Text(
          'تقرير جديد',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// بناء شاشة التحميل
  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AssetsColors.primary),
          ),
          const SizedBox(height: 20),
          Text(
            'جاري تحميل تقارير المجتمع...',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب التقارير
  Widget _buildReportsTab() {
    return Column(
      children: [
        // شريط الفلاتر
        _buildFilterBar(),

        // قائمة التقارير
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _filteredReports.length,
            itemBuilder: (context, index) {
              final report = _filteredReports[index];
              return _buildReportCard(report);
            },
          ),
        ),
      ],
    );
  }

  /// بناء تبويب الخبراء
  Widget _buildExpertsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _expertResponses.length,
      itemBuilder: (context, index) {
        final response = _expertResponses[index];
        return _buildExpertResponseCard(response);
      },
    );
  }

  /// بناء تبويب المناقشات
  Widget _buildDiscussionsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.forum,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'المناقشات قيد التطوير',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستتمكن قريباً من المشاركة في المناقشات',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الفلاتر
  Widget _buildFilterBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', 'all'),
          _buildFilterChip('مفتوح', 'open'),
          _buildFilterChip('تمت المراجعة', 'reviewed'),
          _buildFilterChip('محلول', 'solved'),
          _buildFilterChip('حرج', 'critical'),
        ],
      ),
    );
  }

  /// بناء رقاقة فلتر
  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = value;
            _applyFilter();
          });
        },
        backgroundColor: Colors.white,
        selectedColor: AssetsColors.primary,
        checkmarkColor: Colors.white,
        elevation: 2,
      ),
    );
  }

  /// بناء بطاقة تقرير
  Widget _buildReportCard(CommunityReport report) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: AssetsColors.primary.withValues(alpha: 0.1),
                  child: Text(
                    report.authorName.substring(0, 1),
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      color: AssetsColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        report.authorName,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        report.authorLocation,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getSeverityColor(report.severity),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getSeverityText(report.severity),
                    style: TextStyle(
                      fontFamily: AssetsFonts.cairo,
                      fontSize: 10,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // عنوان التقرير
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              report.title,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // وصف التقرير
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              report.description,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 13,
                color: AssetsColors.kGrey70,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(height: 12),

          // الصور
          if (report.images.isNotEmpty)
            Container(
              height: 100,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: report.images.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(report.images[index]),
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),

          const SizedBox(height: 12),

          // العلامات
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Wrap(
              spacing: 6,
              runSpacing: 4,
              children: report.tags.map((tag) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AssetsColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AssetsColors.primary.withValues(alpha: 0.3)),
                ),
                child: Text(
                  tag,
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 10,
                    color: AssetsColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )).toList(),
            ),
          ),

          const SizedBox(height: 16),

          // إحصائيات وأزرار
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildStatChip(Icons.visibility, '${report.viewCount}'),
                const SizedBox(width: 8),
                _buildStatChip(Icons.comment, '${report.responseCount}'),
                const SizedBox(width: 8),
                _buildStatChip(Icons.thumb_up, '${report.helpfulCount}'),

                const Spacer(),

                Text(
                  _formatDate(report.reportDate),
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 11,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة رد الخبير
  Widget _buildExpertResponseCard(ExpertResponse response) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الخبير
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.verified_user,
                    color: Colors.green,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            response.expertName,
                            style: TextStyle(
                              fontFamily: AssetsFonts.cairo,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                          if (response.isVerified) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              color: Colors.blue,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      Text(
                        response.expertTitle,
                        style: TextStyle(
                          fontFamily: AssetsFonts.cairo,
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Icon(Icons.star, color: Colors.amber, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      response.expertRating.toString(),
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // رد الخبير
            Text(
              response.response,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 13,
                color: AssetsColors.kGrey70,
                height: 1.4,
              ),
            ),

            const SizedBox(height: 12),

            // إحصائيات الرد
            Row(
              children: [
                _buildStatChip(Icons.thumb_up, '${response.helpfulCount}'),

                const Spacer(),

                Text(
                  _formatDate(response.responseDate),
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 11,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رقاقة إحصائية
  Widget _buildStatChip(IconData icon, String count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey.shade600),
          const SizedBox(width: 4),
          Text(
            count,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 11,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// تطبيق الفلتر
  void _applyFilter() {
    setState(() {
      switch (_selectedFilter) {
        case 'all':
          _filteredReports = _allReports;
          break;
        case 'open':
          _filteredReports = _allReports.where((r) => r.status == ReportStatus.open).toList();
          break;
        case 'reviewed':
          _filteredReports = _allReports.where((r) => r.status == ReportStatus.expertReviewed).toList();
          break;
        case 'solved':
          _filteredReports = _allReports.where((r) => r.status == ReportStatus.solved).toList();
          break;
        case 'critical':
          _filteredReports = _allReports.where((r) => r.severity == ReportSeverity.critical).toList();
          break;
      }
    });
  }

  /// الحصول على لون الخطورة
  Color _getSeverityColor(ReportSeverity severity) {
    switch (severity) {
      case ReportSeverity.low:
        return Colors.green;
      case ReportSeverity.medium:
        return Colors.orange;
      case ReportSeverity.high:
        return Colors.red;
      case ReportSeverity.critical:
        return Colors.red.shade900;
    }
  }

  /// الحصول على نص الخطورة
  String _getSeverityText(ReportSeverity severity) {
    switch (severity) {
      case ReportSeverity.low:
        return 'منخفض';
      case ReportSeverity.medium:
        return 'متوسط';
      case ReportSeverity.high:
        return 'عالي';
      case ReportSeverity.critical:
        return 'حرج';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'البحث في التقارير',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث عن تقرير...',
            hintStyle: TextStyle(fontFamily: AssetsFonts.cairo),
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performSearch();
            },
            child: Text(
              'بحث',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تنفيذ البحث
  void _performSearch() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      setState(() {
        _filteredReports = _allReports;
      });
      return;
    }

    setState(() {
      _filteredReports = _allReports.where((report) =>
          report.title.toLowerCase().contains(query) ||
          report.description.toLowerCase().contains(query) ||
          report.cropType.toLowerCase().contains(query) ||
          report.tags.any((tag) => tag.toLowerCase().contains(query))
      ).toList();
    });
  }

  /// معالجة اختيار القائمة
  void _handleMenuSelection(String value) {
    switch (value) {
      case 'my_reports':
        _showMyReports();
        break;
      case 'notifications':
        _showNotifications();
        break;
    }
  }

  /// عرض تقاريري
  void _showMyReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'صفحة تقاريري قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض الإشعارات
  void _showNotifications() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'صفحة الإشعارات قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }

  /// عرض حوار تقرير جديد
  void _showNewReportDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'ميزة إضافة تقرير جديد قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: AssetsColors.primary,
      ),
    );
  }
}

/// نموذج تقرير المجتمع
class CommunityReport {
  final String id;
  final String title;
  final String description;
  final String authorName;
  final String authorLocation;
  final String cropType;
  final ReportSeverity severity;
  final List<String> images;
  final DateTime reportDate;
  final int viewCount;
  final int responseCount;
  final int helpfulCount;
  final ReportStatus status;
  final List<String> tags;

  CommunityReport({
    required this.id,
    required this.title,
    required this.description,
    required this.authorName,
    required this.authorLocation,
    required this.cropType,
    required this.severity,
    required this.images,
    required this.reportDate,
    required this.viewCount,
    required this.responseCount,
    required this.helpfulCount,
    required this.status,
    required this.tags,
  });
}

/// نموذج رد الخبير
class ExpertResponse {
  final String id;
  final String reportId;
  final String expertName;
  final String expertTitle;
  final double expertRating;
  final String response;
  final DateTime responseDate;
  final int helpfulCount;
  final bool isVerified;

  ExpertResponse({
    required this.id,
    required this.reportId,
    required this.expertName,
    required this.expertTitle,
    required this.expertRating,
    required this.response,
    required this.responseDate,
    required this.helpfulCount,
    required this.isVerified,
  });
}

/// مستويات خطورة التقرير
enum ReportSeverity {
  low,      // منخفض
  medium,   // متوسط
  high,     // عالي
  critical, // حرج
}

/// حالات التقرير
enum ReportStatus {
  open,           // مفتوح
  expertReviewed, // تمت مراجعته من خبير
  solved,         // محلول
  closed,         // مغلق
}
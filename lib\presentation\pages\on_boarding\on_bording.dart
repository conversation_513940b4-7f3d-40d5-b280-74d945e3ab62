import 'package:animate_do/animate_do.dart';

import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../imports.dart';

class OnBoardingPage extends StatefulWidget {
  const OnBoardingPage({super.key});

  @override
  State<OnBoardingPage> createState() => _OnBoardingPageState();
}

class _OnBoardingPageState extends State<OnBoardingPage> {
  late PageController boardController = PageController();
  bool isList = false;
  bool isPressed = false;

  @override
  void initState() {
    super.initState();

    // منع الرجوع إلى صفحة الترحيب إذا كان المستخدم قد رآها من قبل
    WidgetsBinding.instance.addPostFrameCallback((callback) async {
      // التحقق من حالة المستخدم
      try {
        final initialRoute = await InitialRouteHelper.determineInitialRoute();

        // إذا كان المسار الأولي ليس صفحة الترحيب، توجيه المستخدم إلى المسار المناسب
        if (initialRoute != RouteConstants.onBoarding) {
          LoggerService.debug(
            'تم تخطي صفحة الترحيب، توجيه المستخدم إلى $initialRoute',
            tag: 'OnBoardingPage',
          );
          if (mounted) {
            Navigator.pushReplacementNamed(context, initialRoute);
          }
        } else {
          LoggerService.debug(
            'عرض صفحة الترحيب للمستخدم',
            tag: 'OnBoardingPage',
          );
        }
      } catch (e) {
        LoggerService.error(
          'خطأ أثناء تحديد المسار الأولي: $e',
          error: e,
          tag: 'OnBoardingPage',
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          FadeInRight(
            duration: Duration(milliseconds: 1000),
            child: TextButton(
              onPressed: () {
                submit(context);
              },
              child: Text(
                'تخطي',
                style: TextStyles.of(context).headlineSmall(
                  color: AppColors.primary,
                  fontSize: 20,
                  fontWeight: FontWeight.w900,
                  fontFamily: AssetsFonts.messiri,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Color.fromRGBO(230, 255, 230, 0.5),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                // شرائح الترحيب
                Expanded(
                  child: PageView.builder(
                    physics: BouncingScrollPhysics(),
                    onPageChanged: (value) {
                      setState(() {
                        if (value == listOnboarding.length - 1) {
                          isList = true;
                        } else {
                          isList = false;
                        }
                      });
                    },
                    controller: boardController,
                    itemBuilder: (context, index) => OnBoarding(
                      onBoardingModel: listOnboarding[index],
                    ),
                    itemCount: listOnboarding.length,
                  ),
                ),

                // مؤشرات الصفحات وزر التنقل
                FadeInUp(
                  duration: Duration(milliseconds: 1000),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 20, bottom: 10),
                    child: Row(
                      children: [
                        // مؤشرات الصفحات
                        SmoothPageIndicator(
                          effect: ExpandingDotsEffect(
                            activeDotColor: AppColors.primary,
                            dotColor: Colors.grey.shade300,
                            dotHeight: 10,
                            dotWidth: 10,
                            spacing: 5,
                            expansionFactor: 3,
                          ),
                          controller: boardController,
                          count: listOnboarding.length,
                        ),

                        Spacer(),

                        // زر متحرك مع تأثيرات النقر
                        GestureDetector(
                          onTapDown: (_) {
                            setState(() {
                              isPressed = true;
                            });
                          },
                          onTapUp: (_) {
                            setState(() {
                              isPressed = false;
                            });
                            if (isList) {
                              submit(context);
                            } else {
                              boardController.nextPage(
                                duration: Duration(milliseconds: 750),
                                curve: Curves.fastEaseInToSlowEaseOut,
                              );
                            }
                          },
                          onTapCancel: () {
                            setState(() {
                              isPressed = false;
                            });
                          },
                          child: AnimatedContainer(
                            duration: Duration(milliseconds: 150),
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppColors.primary,
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromRGBO(
                                      0, 120, 255, isPressed ? 0.2 : 0.4),
                                  blurRadius: isPressed ? 4 : 8,
                                  spreadRadius: isPressed ? 1 : 2,
                                  offset:
                                      isPressed ? Offset(0, 2) : Offset(0, 4),
                                ),
                              ],
                              // تأثير الضغط - تغيير لون الزر قليلاً
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  isPressed
                                      ? AssetsColors.blue
                                      : Color.fromRGBO(30, 150, 255, 1),
                                  AppColors.primary,
                                ],
                              ),
                            ),
                            // تأثير التصغير عند الضغط
                            transform: Matrix4.identity()
                              ..scale(isPressed ? 0.95 : 1.0),
                            child: Center(
                              child: Icon(
                                Icons.arrow_forward,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// إكمال عملية الترحيب
  ///
  /// تقوم هذه الدالة بتعيين متغير isOnBoarding إلى true لتخطي صفحة الترحيب في المرات القادمة
  /// وتوجيه المستخدم إلى صفحة تسجيل الدخول.
  ///
  /// المعلمات:
  /// - [context]: سياق البناء
  void submit(BuildContext context) async {
    // تعيين متغير isOnBoarding إلى true لتخطي صفحة الترحيب في المرات القادمة
    await InitialRouteHelper.markOnboardingAsCompleted();

    // تسجيل إكمال عملية الترحيب
    LoggerService.debug(
      'تم إكمال عملية الترحيب، توجيه المستخدم إلى صفحة تسجيل الدخول',
      tag: 'OnBoardingPage',
    );

    // توجيه المستخدم إلى صفحة تسجيل الدخول
    if (context.mounted) {
      Navigator.pushReplacementNamed(context, RouteConstants.login);
    }
  }
}

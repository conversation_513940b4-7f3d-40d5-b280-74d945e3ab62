import 'package:equatable/equatable.dart';

/// نموذج تنبيه الطقس الزراعي
///
/// يحتوي على معلومات التنبيه والتوصيات الزراعية
class WeatherAlertModel extends Equatable {
  /// معرف التنبيه
  final String id;

  /// نوع التنبيه
  final WeatherAlertType type;

  /// مستوى الأولوية
  final AlertPriority priority;

  /// عنوان التنبيه
  final String title;

  /// رسالة التنبيه
  final String message;

  /// التوصيات الزراعية
  final List<String> recommendations;

  /// الأنشطة المتأثرة
  final List<AgriculturalActivity> affectedActivities;

  /// وقت إنشاء التنبيه
  final DateTime createdAt;

  /// وقت انتهاء صلاحية التنبيه
  final DateTime? expiresAt;

  /// هل تم قراءة التنبيه
  final bool isRead;

  /// بيانات الطقس المرتبطة
  final Map<String, dynamic>? weatherData;

  const WeatherAlertModel({
    required this.id,
    required this.type,
    required this.priority,
    required this.title,
    required this.message,
    required this.recommendations,
    required this.affectedActivities,
    required this.createdAt,
    this.expiresAt,
    this.isRead = false,
    this.weatherData,
  });

  /// إنشاء نسخة محدثة من التنبيه
  WeatherAlertModel copyWith({
    String? id,
    WeatherAlertType? type,
    AlertPriority? priority,
    String? title,
    String? message,
    List<String>? recommendations,
    List<AgriculturalActivity>? affectedActivities,
    DateTime? createdAt,
    DateTime? expiresAt,
    bool? isRead,
    Map<String, dynamic>? weatherData,
  }) {
    return WeatherAlertModel(
      id: id ?? this.id,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      recommendations: recommendations ?? this.recommendations,
      affectedActivities: affectedActivities ?? this.affectedActivities,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isRead: isRead ?? this.isRead,
      weatherData: weatherData ?? this.weatherData,
    );
  }

  /// تحويل من JSON
  factory WeatherAlertModel.fromJson(Map<String, dynamic> json) {
    return WeatherAlertModel(
      id: json['id'] as String,
      type: WeatherAlertType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => WeatherAlertType.general,
      ),
      priority: AlertPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => AlertPriority.medium,
      ),
      title: json['title'] as String,
      message: json['message'] as String,
      recommendations: List<String>.from(json['recommendations'] as List),
      affectedActivities: (json['affectedActivities'] as List)
          .map((e) => AgriculturalActivity.values.firstWhere(
                (activity) => activity.name == e,
                orElse: () => AgriculturalActivity.general,
              ))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      isRead: json['isRead'] as bool? ?? false,
      weatherData: json['weatherData'] as Map<String, dynamic>?,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'priority': priority.name,
      'title': title,
      'message': message,
      'recommendations': recommendations,
      'affectedActivities': affectedActivities.map((e) => e.name).toList(),
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'isRead': isRead,
      'weatherData': weatherData,
    };
  }

  @override
  List<Object?> get props => [
        id,
        type,
        priority,
        title,
        message,
        recommendations,
        affectedActivities,
        createdAt,
        expiresAt,
        isRead,
        weatherData,
      ];
}

/// أنواع تنبيهات الطقس
enum WeatherAlertType {
  /// تنبيه عام
  general,

  /// تنبيه مطر غزير
  heavyRain,

  /// تنبيه عاصفة
  storm,

  /// تنبيه صقيع
  frost,

  /// تنبيه حرارة عالية
  heatWave,

  /// تنبيه رياح قوية
  strongWind,

  /// تنبيه جفاف
  drought,

  /// تنبيه رطوبة عالية
  highHumidity,

  /// تنبيه ضغط منخفض
  lowPressure,

  /// تنبيه ظروف مثالية
  idealConditions,
}

/// مستويات أولوية التنبيه
enum AlertPriority {
  /// أولوية منخفضة
  low,

  /// أولوية متوسطة
  medium,

  /// أولوية عالية
  high,

  /// أولوية حرجة
  critical,
}

/// الأنشطة الزراعية المتأثرة
enum AgriculturalActivity {
  /// نشاط عام
  general,

  /// الري
  irrigation,

  /// الرش
  spraying,

  /// الحصاد
  harvesting,

  /// الزراعة
  planting,

  /// التقليم
  pruning,

  /// التسميد
  fertilizing,

  /// مكافحة الآفات
  pestControl,

  /// العمل في الحقل
  fieldWork,
}

/// امتدادات مفيدة لأنواع التنبيهات
extension WeatherAlertTypeExtension on WeatherAlertType {
  /// الحصول على أيقونة التنبيه
  String get icon {
    switch (this) {
      case WeatherAlertType.heavyRain:
        return '🌧️';
      case WeatherAlertType.storm:
        return '⛈️';
      case WeatherAlertType.frost:
        return '❄️';
      case WeatherAlertType.heatWave:
        return '🌡️';
      case WeatherAlertType.strongWind:
        return '💨';
      case WeatherAlertType.drought:
        return '🏜️';
      case WeatherAlertType.highHumidity:
        return '💧';
      case WeatherAlertType.lowPressure:
        return '📉';
      case WeatherAlertType.idealConditions:
        return '✅';
      case WeatherAlertType.general:
      
        return '⚠️';
    }
  }

  /// الحصول على لون التنبيه
  int get colorValue {
    switch (this) {
      case WeatherAlertType.storm:
      case WeatherAlertType.heavyRain:
        return 0xFFE53E3E; // أحمر
      case WeatherAlertType.frost:
      case WeatherAlertType.heatWave:
        return 0xFFFF9800; // برتقالي
      case WeatherAlertType.strongWind:
      case WeatherAlertType.drought:
        return 0xFFFFC107; // أصفر
      case WeatherAlertType.idealConditions:
        return 0xFF4CAF50; // أخضر
      case WeatherAlertType.highHumidity:
      case WeatherAlertType.lowPressure:
        return 0xFF2196F3; // أزرق
      case WeatherAlertType.general:
     
        return 0xFF9E9E9E; // رمادي
    }
  }
}

/// امتدادات مفيدة لمستويات الأولوية
extension AlertPriorityExtension on AlertPriority {
  /// الحصول على لون الأولوية
  int get colorValue {
    switch (this) {
      case AlertPriority.critical:
        return 0xFFE53E3E; // أحمر
      case AlertPriority.high:
        return 0xFFFF9800; // برتقالي
      case AlertPriority.medium:
        return 0xFFFFC107; // أصفر
      case AlertPriority.low:
        return 0xFF4CAF50; // أخضر
    }
  }

  /// الحصول على وزن الأولوية للترتيب
  int get weight {
    switch (this) {
      case AlertPriority.critical:
        return 4;
      case AlertPriority.high:
        return 3;
      case AlertPriority.medium:
        return 2;
      case AlertPriority.low:
        return 1;
    }
  }
}

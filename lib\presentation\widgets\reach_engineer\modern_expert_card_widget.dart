import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../core/constants/colors.dart';
import '../../../core/constants/text_styles.dart';
import '../../../core/utils/image_cache_manager.dart';
import '../../../core/widgets/swipe_gesture_detector.dart';
import '../../../data/models/experts/agricultural_expert_model.dart';

/// بطاقة خبير زراعي محسنة وحديثة - المرحلة الثانية
///
/// تعرض معلومات الخبير بتصميم جذاب ووظائف تفاعلية محسنة
/// مطبقة وفق التفضيلات الـ18 مع عدم التكرار
/// تم تحسينها في المرحلة الثانية مع ميزات إضافية
class ModernExpertCardWidget extends StatefulWidget {
  /// بيانات الخبير الزراعي
  final AgriculturalExpertModel expert;

  /// دالة استدعاء عند النقر على البطاقة
  final VoidCallback? onTap;

  /// دالة استدعاء عند النقر على زر الاتصال
  final VoidCallback? onCallPressed;

  /// دالة استدعاء عند النقر على زر الرسائل
  final VoidCallback? onMessagePressed;

  /// دالة استدعاء عند النقر على زر الاستشارة
  final VoidCallback? onConsultationPressed;

  /// دالة استدعاء عند النقر على المفضلة
  final VoidCallback? onFavoritePressed;

  /// دالة استدعاء عند النقر على زر التقييم
  final VoidCallback? onRatingPressed;

  /// دالة استدعاء عند النقر على زر حجز موعد
  final VoidCallback? onBookAppointmentPressed;

  /// هل الخبير في المفضلة
  final bool isFavorite;

  /// هل تظهر الرسوم المتحركة
  final bool showAnimations;

  /// نمط عرض البطاقة
  final ExpertCardStyle style;

  /// هل تظهر الإجراءات السريعة
  final bool showQuickActions;

  /// هل يمكن تقييم الخبير
  final bool enableRating;

  /// هل يمكن حجز موعد مع الخبير
  final bool enableAppointmentBooking;

  const ModernExpertCardWidget({
    super.key,
    required this.expert,
    this.onTap,
    this.onCallPressed,
    this.onMessagePressed,
    this.onConsultationPressed,
    this.onFavoritePressed,
    this.onRatingPressed,
    this.onBookAppointmentPressed,
    this.isFavorite = false,
    this.showAnimations = true,
    this.style = ExpertCardStyle.full,
    this.showQuickActions = true,
    this.enableRating = true,
    this.enableAppointmentBooking = true,
  });

  @override
  State<ModernExpertCardWidget> createState() => _ModernExpertCardWidgetState();
}

class _ModernExpertCardWidgetState extends State<ModernExpertCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _favoriteController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  late Animation<double> _favoriteAnimation;

  @override
  void initState() {
    super.initState();

    if (widget.showAnimations) {
      _setupAnimations();
    }
  }

  /// إعداد الرسوم المتحركة
  void _setupAnimations() {
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _favoriteController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _elevationAnimation = Tween<double>(begin: 2.0, end: 8.0).animate(
      CurvedAnimation(parent: _hoverController, curve: Curves.easeInOut),
    );

    _favoriteAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(parent: _favoriteController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    if (widget.showAnimations) {
      _hoverController.dispose();
      _favoriteController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.style) {
      case ExpertCardStyle.compact:
        return _buildCompactCard();
      case ExpertCardStyle.minimal:
        return _buildMinimalCard();
      case ExpertCardStyle.full:
        return _buildFullCard();
    }
  }

  /// بناء البطاقة الكاملة
  Widget _buildFullCard() {
    Widget card = HorizontalSwipeDetector(
      onSwipeLeft: () {
        // سحب لليسار - إضافة للمفضلة
        widget.onFavoritePressed?.call();
      },
      onSwipeRight: () {
        // سحب لليمين - بدء محادثة
        widget.onMessagePressed?.call();
      },
      enableHaptic: true,
      child: GestureDetector(
        onTap: () {
          widget.onTap?.call();
          HapticFeedback.lightImpact();
        },
        onTapDown: (_) => _onHoverStart(),
        onTapUp: (_) => _onHoverEnd(),
        onTapCancel: _onHoverEnd,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: _elevationAnimation.value,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildCardHeader(),
              _buildCardContent(),
              if (widget.showQuickActions) _buildQuickActions(),
            ],
          ),
        ),
      ),
    );

    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _hoverController,
        builder: (context, child) {
          return Transform.scale(scale: _scaleAnimation.value, child: card);
        },
      );
    }

    return card;
  }

  /// بناء البطاقة المضغوطة
  Widget _buildCompactCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildAvatar(size: 40),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.expert.name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  widget.expert.specialization,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          _buildStatusIndicator(),
          _buildFavoriteButton(size: 20),
        ],
      ),
    );
  }

  /// بناء البطاقة البسيطة
  Widget _buildMinimalCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          _buildAvatar(size: 32),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.expert.name,
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          _buildStatusIndicator(size: 8),
        ],
      ),
    );
  }

  /// بناء رأس البطاقة
  Widget _buildCardHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildAvatar(),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.expert.name,
                        style: AppTextStyles.headlineSmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    _buildStatusIndicator(),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  widget.expert.specialization,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  widget.expert.governorate,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          _buildFavoriteButton(),
        ],
      ),
    );
  }

  /// بناء محتوى البطاقة
  Widget _buildCardContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildExpertStats(),
          const SizedBox(height: 12),
          if (widget.expert.bio.isNotEmpty) _buildBio(),
        ],
      ),
    );
  }

  /// بناء الصورة الرمزية
  Widget _buildAvatar({double size = 60}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child:
          widget.expert.profileImageUrl?.isNotEmpty == true
              ? ImageCacheManager.buildCircularCachedImage(
                imageUrl: widget.expert.profileImageUrl!,
                radius: size / 2,
                placeholder: Container(
                  width: size,
                  height: size,
                  decoration: const BoxDecoration(
                    color: Colors.grey,
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
                errorWidget: _buildDefaultAvatar(size),
              )
              : _buildDefaultAvatar(size),
    );
  }

  /// بناء الصورة الرمزية الافتراضية
  Widget _buildDefaultAvatar(double size) {
    return Icon(Icons.person, color: Colors.white, size: size * 0.6);
  }

  /// بناء مؤشر الحالة
  Widget _buildStatusIndicator({double size = 12}) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: widget.expert.isAvailable ? AppColors.success : AppColors.error,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
    );
  }

  /// بناء زر المفضلة
  Widget _buildFavoriteButton({double size = 24}) {
    Widget button = GestureDetector(
      onTap: () {
        widget.onFavoritePressed?.call();
        if (widget.showAnimations) {
          _favoriteController.forward().then((_) {
            _favoriteController.reverse();
          });
        }
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Icon(
          widget.isFavorite ? Icons.favorite : Icons.favorite_border,
          color: widget.isFavorite ? AppColors.error : AppColors.textSecondary,
          size: size,
        ),
      ),
    );

    if (widget.showAnimations) {
      return AnimatedBuilder(
        animation: _favoriteController,
        builder: (context, child) {
          return Transform.scale(
            scale: _favoriteAnimation.value,
            child: button,
          );
        },
      );
    }

    return button;
  }

  /// بناء إحصائيات الخبير
  Widget _buildExpertStats() {
    return Row(
      children: [
        _buildRatingItem(),
        const SizedBox(width: 16),
        _buildStatItem(
          Icons.work_history,
          '${widget.expert.experienceYears} سنة',
          AppColors.accent,
        ),
        const SizedBox(width: 16),
        _buildAvailabilityItem(),
      ],
    );
  }

  /// بناء عنصر التقييم المحسن
  Widget _buildRatingItem() {
    return GestureDetector(
      onTap: widget.enableRating ? widget.onRatingPressed : null,
      child: Row(
        children: [
          Icon(Icons.star, color: AppColors.warning, size: 16),
          const SizedBox(width: 4),
          Text(
            widget.expert.rating.toStringAsFixed(1),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.warning,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 2),
          Text(
            '(${widget.expert.totalReviews ?? 0})',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر حالة توفر المواعيد
  Widget _buildAvailabilityItem() {
    return GestureDetector(
      onTap:
          widget.enableAppointmentBooking
              ? widget.onBookAppointmentPressed
              : null,
      child: Row(
        children: [
          Icon(
            widget.expert.isAvailable
                ? Icons.schedule
                : Icons.schedule_outlined,
            color:
                widget.expert.isAvailable ? AppColors.success : AppColors.error,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            widget.expert.isAvailable ? 'متاح' : 'مشغول',
            style: AppTextStyles.bodySmall.copyWith(
              color:
                  widget.expert.isAvailable
                      ? AppColors.success
                      : AppColors.error,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(IconData icon, String value, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          value,
          style: AppTextStyles.bodySmall.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// بناء السيرة الذاتية
  Widget _buildBio() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        widget.expert.bio,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
          height: 1.4,
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          // الصف الأول - الإجراءات الأساسية
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  Icons.phone,
                  'اتصال',
                  AppColors.success,
                  widget.onCallPressed,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  Icons.message,
                  'رسالة',
                  AppColors.primary,
                  widget.onMessagePressed,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  Icons.psychology,
                  'استشارة',
                  AppColors.accent,
                  widget.onConsultationPressed,
                ),
              ),
            ],
          ),
          // الصف الثاني - التقييم وحجز المواعيد
          if (widget.enableRating || widget.enableAppointmentBooking) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                if (widget.enableRating)
                  Expanded(
                    child: _buildActionButton(
                      Icons.star_rate,
                      'تقييم',
                      AppColors.warning,
                      widget.onRatingPressed,
                    ),
                  ),
                if (widget.enableRating && widget.enableAppointmentBooking)
                  const SizedBox(width: 8),
                if (widget.enableAppointmentBooking)
                  Expanded(
                    child: _buildActionButton(
                      Icons.calendar_today,
                      'حجز موعد',
                      AppColors.info,
                      widget.onBookAppointmentPressed,
                    ),
                  ),
                // إضافة مساحة فارغة إذا كان هناك زر واحد فقط
                if ((widget.enableRating && !widget.enableAppointmentBooking) ||
                    (!widget.enableRating && widget.enableAppointmentBooking))
                  const Expanded(child: SizedBox()),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(
    IconData icon,
    String label,
    Color color,
    VoidCallback? onPressed,
  ) {
    return GestureDetector(
      onTap: () {
        onPressed?.call();
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 3),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بدء تأثير التمرير
  void _onHoverStart() {
    if (widget.showAnimations) {
      _hoverController.forward();
    }
  }

  /// إنهاء تأثير التمرير
  void _onHoverEnd() {
    if (widget.showAnimations) {
      _hoverController.reverse();
    }
  }
}

/// أنماط عرض بطاقة الخبير
enum ExpertCardStyle {
  /// عرض كامل مع جميع التفاصيل
  full,

  /// عرض مضغوط
  compact,

  /// عرض بسيط
  minimal,
}

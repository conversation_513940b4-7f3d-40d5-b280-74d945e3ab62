
import 'package:jiffy/jiffy.dart';

import '../../../core/constants/weather/index.dart';
import '../../../data/models/weather/weather_model.dart';
import '../../../imports.dart';
import 'dynamic_weather_background.dart';

/// بطاقة طقس محسنة مع تصميم حديث
///
/// تعرض معلومات الطقس الحالي بتصميم جذاب ومتجاوب
class EnhancedWeatherCard extends StatelessWidget {
  /// بيانات الطقس
  final WeatherModel weather;

  /// تفعيل الخلفية الديناميكية
  final bool enableDynamicBackground;

  /// تفعيل الظلال
  final bool enableShadows;

  /// تفعيل التأثيرات المتحركة
  final bool enableAnimations;

  /// إنشاء بطاقة الطقس المحسنة
  const EnhancedWeatherCard({
    super.key,
    required this.weather,
    this.enableDynamicBackground = true,
    this.enableShadows = true,
    this.enableAnimations = true,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration:
          enableAnimations ? const Duration(milliseconds: 600) : Duration.zero,
      curve: Curves.easeInOut,
      margin: EdgeInsets.symmetric(
        horizontal: WeatherDimensions.mediumMargin,
        vertical: WeatherDimensions.smallMargin,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        boxShadow:
            enableShadows
                ? [
                  BoxShadow(
                    color: WeatherColors.shadowColor,
                    blurRadius: WeatherDimensions.shadowBlurRadius,
                    spreadRadius: WeatherDimensions.shadowSpreadRadius,
                    offset: Offset(
                      WeatherDimensions.shadowOffsetX,
                      WeatherDimensions.shadowOffsetY,
                    ),
                  ),
                ]
                : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(WeatherDimensions.cardBorderRadius),
        child:
            enableDynamicBackground
                ? DynamicWeatherBackground(
                  weather: weather,
                  child: _buildCardContent(context),
                )
                : Container(
                  color: WeatherColors.cardBackground,
                  child: _buildCardContent(context),
                ),
      ),
    );
  }

  /// بناء محتوى البطاقة
  Widget _buildCardContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(WeatherDimensions.cardPadding),
      child: Column(
        children: [
          // رأس البطاقة مع الموقع والتاريخ
          _buildCardHeader(context),
          SizedBox(height: WeatherDimensions.largeMargin),

          // درجة الحرارة والأيقونة الرئيسية
          _buildMainTemperatureSection(context),
          SizedBox(height: WeatherDimensions.mediumMargin),

          // وصف الطقس
          _buildWeatherDescription(context),
          SizedBox(height: WeatherDimensions.largeMargin),

          // معلومات إضافية (رطوبة، رياح، ضغط)
          _buildAdditionalInfo(context),
        ],
      ),
    );
  }

  /// بناء رأس البطاقة
  Widget _buildCardHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // أيقونة الموقع واسم المدينة
        Expanded(
          child: Row(
            children: [
              Icon(
                Icons.location_on,
                color: WeatherColors.primaryText,
                size: WeatherDimensions.mediumIconSize,
              ),
              SizedBox(width: WeatherDimensions.smallMargin),
              Expanded(
                child: Text(
                  weather.name ?? WeatherStrings.noDataMessage,
                  style: TextStyle(
                    fontSize: WeatherDimensions.subtitleFontSize,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.w600,
                    color: WeatherColors.primaryText,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),

        // التاريخ والوقت
        Text(
          Jiffy.now().format(pattern: 'dd MMMM yyyy'),
          style: TextStyle(
            fontSize: WeatherDimensions.smallFontSize,
            fontFamily: AssetsFonts.cairo,
            color: WeatherColors.primaryText.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  /// بناء قسم درجة الحرارة الرئيسي
  Widget _buildMainTemperatureSection(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // أيقونة الطقس
        _buildWeatherIcon(),

        // درجة الحرارة الرئيسية
        StackHomeWeather(
          numberWeather:
              weather.main?.temp != null
                  ? weather.main!.temp!.round().toString()
                  : '0',
          color: WeatherColors.primaryText,
          imageAssets: _getWeatherIconPath(),
        ),

        // معلومات إضافية عن درجة الحرارة
        _buildTemperatureDetails(context),
      ],
    );
  }

  /// بناء أيقونة الطقس
  Widget _buildWeatherIcon() {
    return Container(
      padding: EdgeInsets.all(WeatherDimensions.mediumPadding),
      decoration: BoxDecoration(
        color: WeatherColors.primaryText.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getWeatherIconData(),
        size: WeatherDimensions.extraLargeIconSize,
        color: WeatherColors.primaryText,
      ),
    );
  }

  /// بناء تفاصيل درجة الحرارة
  Widget _buildTemperatureDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (weather.main?.feelsLike != null) ...[
          Text(
            '${WeatherStrings.feelsLikeLabel}:',
            style: TextStyle(
              fontSize: WeatherDimensions.smallFontSize,
              fontFamily: AssetsFonts.cairo,
              color: WeatherColors.primaryText.withValues(alpha: 0.7),
            ),
          ),
          Text(
            '${weather.main!.feelsLike!.round()}${WeatherStrings.temperatureUnit}',
            style: TextStyle(
              fontSize: WeatherDimensions.bodyFontSize,
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.w600,
              color: WeatherColors.primaryText,
            ),
          ),
        ],

        if (weather.main?.tempMax != null && weather.main?.tempMin != null) ...[
          SizedBox(height: WeatherDimensions.smallMargin),
          Row(
            children: [
              Icon(
                Icons.keyboard_arrow_up,
                size: WeatherDimensions.smallIconSize,
                color: WeatherColors.primaryText.withValues(alpha: 0.7),
              ),
              Text(
                '${weather.main!.tempMax!.round()}°',
                style: TextStyle(
                  fontSize: WeatherDimensions.smallFontSize,
                  fontFamily: AssetsFonts.cairo,
                  color: WeatherColors.primaryText.withValues(alpha: 0.7),
                ),
              ),
              SizedBox(width: WeatherDimensions.smallMargin),
              Icon(
                Icons.keyboard_arrow_down,
                size: WeatherDimensions.smallIconSize,
                color: WeatherColors.primaryText.withValues(alpha: 0.7),
              ),
              Text(
                '${weather.main!.tempMin!.round()}°',
                style: TextStyle(
                  fontSize: WeatherDimensions.smallFontSize,
                  fontFamily: AssetsFonts.cairo,
                  color: WeatherColors.primaryText.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// بناء وصف الطقس
  Widget _buildWeatherDescription(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: WeatherDimensions.mediumPadding,
        vertical: WeatherDimensions.smallPadding,
      ),
      decoration: BoxDecoration(
        color: WeatherColors.primaryText.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(
          WeatherDimensions.cardBorderRadius / 2,
        ),
      ),
      child: Text(
        weather.weather != null &&
                weather.weather!.isNotEmpty &&
                weather.weather![0].description != null
            ? weather.weather![0].description.toString()
            : WeatherStrings.clearSky,
        style: TextStyle(
          fontSize: WeatherDimensions.bodyFontSize,
          fontFamily: AssetsFonts.cairo,
          fontWeight: FontWeight.w500,
          color: WeatherColors.primaryText,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء المعلومات الإضافية
  Widget _buildAdditionalInfo(BuildContext context) {
    return RowWeather(
      temperature:
          weather.main?.temp != null
              ? weather.main!.temp!.round().toString()
              : '0',
      humidity:
          weather.main?.humidity != null
              ? weather.main!.humidity.toString()
              : '0',
      windSpeed:
          weather.wind?.speed != null ? weather.wind!.speed!.toString() : '0',
      color:
          enableDynamicBackground
              ? Colors.transparent
              : WeatherColors.cardBackground,
    );
  }

  /// الحصول على مسار أيقونة الطقس
  String _getWeatherIconPath() {
    if (weather.weather != null &&
        weather.weather!.isNotEmpty &&
        weather.weather![0].icon != null) {
      return 'assets/icons/${weather.weather![0].icon!.replaceAll('n', 'd')}.png';
    }
    return 'assets/icons/01d.png';
  }

  /// الحصول على أيقونة الطقس
  IconData _getWeatherIconData() {
    if (weather.weather == null || weather.weather!.isEmpty) {
      return Icons.wb_sunny;
    }

    final weatherCode = weather.weather![0].id;
    if (weatherCode != null) {
      final code =
          weatherCode is int
              ? weatherCode
              : int.tryParse(weatherCode.toString()) ?? 800;

      if (code >= 200 && code < 300) return Icons.thunderstorm;
      if (code >= 300 && code < 600) return Icons.grain;
      if (code >= 600 && code < 700) return Icons.ac_unit;
      if (code >= 700 && code < 800) return Icons.foggy;
      if (code == 800) return Icons.wb_sunny;
      if (code > 800) return Icons.cloud;
    }

    return Icons.wb_sunny;
  }
}

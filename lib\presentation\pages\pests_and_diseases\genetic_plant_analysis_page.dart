import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../core/constants/assets_fonts.dart';

/// صفحة التحليل الجيني للنباتات
///
/// ميزة فريدة عالمياً تحلل الحمض النووي للنباتات لتحديد:
/// - المقاومة الجينية للآفات والأمراض
/// - التنبؤ بالمشاكل المستقبلية
/// - توصيات التحسين الجيني
class GeneticPlantAnalysisPage extends StatefulWidget {
  const GeneticPlantAnalysisPage({super.key});

  @override
  State<GeneticPlantAnalysisPage> createState() => _GeneticPlantAnalysisPageState();
}

class _GeneticPlantAnalysisPageState extends State<GeneticPlantAnalysisPage>
    with TickerProviderStateMixin {

  late AnimationController _dnaAnimationController;
  late AnimationController _scanAnimationController;
  late Animation<double> _dnaRotation;
  late Animation<double> _scanProgress;

  bool _isScanning = false;
  bool _analysisComplete = false;
  List<GeneticMarker> _geneticMarkers = [];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadGeneticData();
  }

  @override
  void dispose() {
    _dnaAnimationController.dispose();
    _scanAnimationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _dnaAnimationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _scanAnimationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _dnaRotation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _dnaAnimationController,
      curve: Curves.linear,
    ));

    _scanProgress = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scanAnimationController,
      curve: Curves.easeInOut,
    ));

    _dnaAnimationController.repeat();
  }

  Future<void> _loadGeneticData() async {
    // محاكاة تحميل البيانات الجينية
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _geneticMarkers = _generateGeneticMarkers();
    });
  }

  List<GeneticMarker> _generateGeneticMarkers() {
    return [
      GeneticMarker(
        id: 'R1',
        name: 'مقاومة اللفحة المتأخرة',
        chromosome: 'Chr11',
        position: 45234567,
        resistanceLevel: ResistanceLevel.high,
        confidence: 0.94,
        description: 'جين مقاومة قوي ضد فطر Phytophthora infestans',
      ),
      GeneticMarker(
        id: 'R2',
        name: 'مقاومة دودة ورق القطن',
        chromosome: 'Chr5',
        position: 23456789,
        resistanceLevel: ResistanceLevel.medium,
        confidence: 0.87,
        description: 'مقاومة متوسطة ضد Helicoverpa armigera',
      ),
      GeneticMarker(
        id: 'R3',
        name: 'تحمل الجفاف',
        chromosome: 'Chr2',
        position: 67890123,
        resistanceLevel: ResistanceLevel.high,
        confidence: 0.91,
        description: 'قدرة عالية على تحمل نقص المياه',
      ),
      GeneticMarker(
        id: 'S1',
        name: 'حساسية للبياض الدقيقي',
        chromosome: 'Chr8',
        position: 34567890,
        resistanceLevel: ResistanceLevel.low,
        confidence: 0.89,
        description: 'حساسية عالية لفطر Erysiphe cichoracearum',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E27),
      appBar: AppBar(
        title: Text(
          'التحليل الجيني للنباتات',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showGeneticInfo,
            tooltip: 'معلومات التحليل الجيني',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // رأس التحليل الجيني
            _buildGeneticHeader(),

            const SizedBox(height: 30),

            // مصفوفة الحمض النووي
            _buildDNAMatrix(),

            const SizedBox(height: 30),

            // أزرار التحكم
            _buildControlButtons(),

            const SizedBox(height: 30),

            // نتائج التحليل
            if (_analysisComplete) _buildAnalysisResults(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس التحليل الجيني
  Widget _buildGeneticHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1E3A8A).withValues(alpha: 0.8),
            const Color(0xFF3B82F6).withValues(alpha: 0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.cyan.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.cyan.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.biotech,
                  color: Colors.cyan,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مختبر الجينات الزراعية',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'تحليل متقدم للحمض النووي النباتي',
                      style: TextStyle(
                        fontFamily: AssetsFonts.cairo,
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // إحصائيات سريعة
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'الجينات المحللة',
                  '${_geneticMarkers.length}',
                  Icons.dns,
                  Colors.cyan,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'مستوى المقاومة',
                  _calculateOverallResistance(),
                  Icons.shield,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'دقة التحليل',
                  '${(_calculateAverageConfidence() * 100).toInt()}%',
                  Icons.precision_manufacturing,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 10,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء مصفوفة الحمض النووي
  Widget _buildDNAMatrix() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.cyan.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // خلفية الشبكة
          _buildGridBackground(),

          // حلزون الحمض النووي
          Center(
            child: AnimatedBuilder(
              animation: _dnaRotation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _dnaRotation.value,
                  child: _buildDNAHelix(),
                );
              },
            ),
          ),

          // مؤشر المسح
          if (_isScanning)
            AnimatedBuilder(
              animation: _scanProgress,
              builder: (context, child) {
                return Positioned(
                  top: _scanProgress.value * 260 + 20,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 2,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          Colors.cyan,
                          Colors.cyan,
                          Colors.transparent,
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.cyan,
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  /// بناء خلفية الشبكة
  Widget _buildGridBackground() {
    return CustomPaint(
      size: Size.infinite,
      painter: GridPainter(),
    );
  }

  /// بناء حلزون الحمض النووي
  Widget _buildDNAHelix() {
    return SizedBox(
      width: 200,
      height: 250,
      child: CustomPaint(
        painter: DNAHelixPainter(),
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isScanning ? null : _startGeneticScan,
            icon: Icon(_isScanning ? Icons.hourglass_empty : Icons.play_arrow),
            label: Text(
              _isScanning ? 'جاري المسح...' : 'بدء التحليل الجيني',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.cyan,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(width: 16),

        ElevatedButton.icon(
          onPressed: _showGeneticDatabase,
          icon: const Icon(Icons.storage),
          label: Text(
            'قاعدة البيانات',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontWeight: FontWeight.bold,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء نتائج التحليل
  Widget _buildAnalysisResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نتائج التحليل الجيني',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 16),

        // الجينات المقاومة
        _buildResistanceGenes(),

        const SizedBox(height: 20),

        // الجينات الحساسة
        _buildSusceptibilityGenes(),

        const SizedBox(height: 20),

        // التوصيات الجينية
        _buildGeneticRecommendations(),
      ],
    );
  }

  /// بناء الجينات المقاومة
  Widget _buildResistanceGenes() {
    final resistantGenes = _geneticMarkers.where((gene) =>
        gene.resistanceLevel == ResistanceLevel.high ||
        gene.resistanceLevel == ResistanceLevel.medium).toList();

    return _buildGeneSection(
      'الجينات المقاومة',
      resistantGenes,
      Colors.green,
      Icons.shield_outlined,
    );
  }

  /// بناء الجينات الحساسة
  Widget _buildSusceptibilityGenes() {
    final susceptibleGenes = _geneticMarkers.where((gene) =>
        gene.resistanceLevel == ResistanceLevel.low).toList();

    return _buildGeneSection(
      'نقاط الضعف الجينية',
      susceptibleGenes,
      Colors.red,
      Icons.warning_outlined,
    );
  }

  /// بناء قسم الجينات
  Widget _buildGeneSection(String title, List<GeneticMarker> genes, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          ...genes.map((gene) => _buildGeneCard(gene, color)),
        ],
      ),
    );
  }

  /// بناء بطاقة جين
  Widget _buildGeneCard(GeneticMarker gene, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  gene.name,
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(gene.confidence * 100).toInt()}%',
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 4),

          Text(
            '${gene.chromosome} : ${gene.position}',
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 11,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),

          const SizedBox(height: 4),

          Text(
            gene.description,
            style: TextStyle(
              fontFamily: AssetsFonts.cairo,
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التوصيات الجينية
  Widget _buildGeneticRecommendations() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple.withValues(alpha: 0.2),
            Colors.blue.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.purple.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.lightbulb_outline, color: Colors.amber, size: 24),
              const SizedBox(width: 12),
              Text(
                'التوصيات الجينية',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          ..._generateGeneticRecommendations().map((recommendation) =>
            _buildRecommendationItem(recommendation)),
        ],
      ),
    );
  }

  /// بناء عنصر توصية
  Widget _buildRecommendationItem(String recommendation) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: Colors.amber,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              recommendation,
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontSize: 13,
                color: Colors.white.withValues(alpha: 0.9),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// حساب المقاومة الإجمالية
  String _calculateOverallResistance() {
    if (_geneticMarkers.isEmpty) return 'غير محدد';

    final resistantCount = _geneticMarkers.where((gene) =>
        gene.resistanceLevel == ResistanceLevel.high ||
        gene.resistanceLevel == ResistanceLevel.medium).length;

    final percentage = (resistantCount / _geneticMarkers.length * 100).toInt();

    if (percentage >= 80) return 'عالي';
    if (percentage >= 60) return 'متوسط';
    return 'منخفض';
  }

  /// حساب متوسط الثقة
  double _calculateAverageConfidence() {
    if (_geneticMarkers.isEmpty) return 0.0;

    final totalConfidence = _geneticMarkers.fold<double>(
      0.0, (sum, gene) => sum + gene.confidence);

    return totalConfidence / _geneticMarkers.length;
  }

  /// بدء المسح الجيني
  void _startGeneticScan() async {
    setState(() {
      _isScanning = true;
      _analysisComplete = false;
    });

    _scanAnimationController.forward();

    // محاكاة عملية المسح
    await Future.delayed(const Duration(seconds: 8));

    setState(() {
      _isScanning = false;
      _analysisComplete = true;
      _generateGeneticProfile();
    });

    _scanAnimationController.reset();
  }

  /// إنشاء ملف جيني
  PlantGeneticProfile _generateGeneticProfile() {
    return PlantGeneticProfile(
      plantSpecies: 'Solanum lycopersicum',
      commonName: 'طماطم',
      genomeSize: '900 Mb',
      chromosomeCount: 12,
      geneCount: 35000,
      sequencingQuality: 0.96,
      analysisDate: DateTime.now(),
    );
  }

  /// إنشاء التوصيات الجينية
  List<String> _generateGeneticRecommendations() {
    return [
      'زراعة أصناف مقاومة للفحة المتأخرة لتقليل استخدام المبيدات',
      'تطبيق برنامج تربية انتقائية لتعزيز مقاومة دودة ورق القطن',
      'استخدام تقنيات الري الذكي لاستغلال جين تحمل الجفاف',
      'مراقبة دورية للبياض الدقيقي بسبب الحساسية الجينية',
      'تطبيق المكافحة البيولوجية كبديل للمبيدات الكيميائية',
    ];
  }

  /// عرض معلومات التحليل الجيني
  void _showGeneticInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E293B),
        title: Text(
          'التحليل الجيني للنباتات',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تقنية متقدمة تحلل الحمض النووي للنباتات لتحديد:',
                style: TextStyle(
                  fontFamily: AssetsFonts.cairo,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
              const SizedBox(height: 12),
              ...[
                '• الجينات المقاومة للآفات والأمراض',
                '• نقاط الضعف الجينية',
                '• التنبؤ بالمشاكل المستقبلية',
                '• توصيات التحسين الجيني',
                '• برامج التربية الانتقائية',
              ].map((item) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  item,
                  style: TextStyle(
                    fontFamily: AssetsFonts.cairo,
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 13,
                  ),
                ),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'فهمت',
              style: TextStyle(
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: Colors.cyan,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض قاعدة البيانات الجينية
  void _showGeneticDatabase() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'قاعدة البيانات الجينية قيد التطوير',
          style: TextStyle(fontFamily: AssetsFonts.cairo),
        ),
        backgroundColor: Colors.purple,
      ),
    );
  }
}

/// رسام الشبكة
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withValues(alpha: 0.1)
      ..strokeWidth = 0.5;

    const spacing = 20.0;

    // رسم الخطوط العمودية
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // رسم الخطوط الأفقية
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// رسام حلزون الحمض النووي
class DNAHelixPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint1 = Paint()
      ..color = Colors.cyan
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final paint2 = Paint()
      ..color = Colors.purple
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final centerX = size.width / 2;
    final amplitude = size.width / 4;

    // رسم الحلزون الأول
    final path1 = Path();
    for (double y = 0; y < size.height; y += 2) {
      final x = centerX + amplitude * math.sin(y * 0.05);
      if (y == 0) {
        path1.moveTo(x, y);
      } else {
        path1.lineTo(x, y);
      }
    }
    canvas.drawPath(path1, paint1);

    // رسم الحلزون الثاني
    final path2 = Path();
    for (double y = 0; y < size.height; y += 2) {
      final x = centerX - amplitude * math.sin(y * 0.05);
      if (y == 0) {
        path2.moveTo(x, y);
      } else {
        path2.lineTo(x, y);
      }
    }
    canvas.drawPath(path2, paint2);

    // رسم الروابط
    final linkPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6)
      ..strokeWidth = 1;

    for (double y = 0; y < size.height; y += 20) {
      final x1 = centerX + amplitude * math.sin(y * 0.05);
      final x2 = centerX - amplitude * math.sin(y * 0.05);
      canvas.drawLine(Offset(x1, y), Offset(x2, y), linkPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// نموذج العلامة الجينية
class GeneticMarker {
  final String id;
  final String name;
  final String chromosome;
  final int position;
  final ResistanceLevel resistanceLevel;
  final double confidence;
  final String description;

  GeneticMarker({
    required this.id,
    required this.name,
    required this.chromosome,
    required this.position,
    required this.resistanceLevel,
    required this.confidence,
    required this.description,
  });
}

/// نموذج الملف الجيني للنبات
class PlantGeneticProfile {
  final String plantSpecies;
  final String commonName;
  final String genomeSize;
  final int chromosomeCount;
  final int geneCount;
  final double sequencingQuality;
  final DateTime analysisDate;

  PlantGeneticProfile({
    required this.plantSpecies,
    required this.commonName,
    required this.genomeSize,
    required this.chromosomeCount,
    required this.geneCount,
    required this.sequencingQuality,
    required this.analysisDate,
  });
}

/// مستويات المقاومة
enum ResistanceLevel {
  low,      // منخفض
  medium,   // متوسط
  high,     // عالي
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/constants/assets_fonts.dart';
import 'package:agriculture/core/constants/education_categories_constants.dart';
import 'package:agriculture/core/constants/text_styles.dart';
import 'package:agriculture/data/models/education/course_model.dart';
import 'package:agriculture/presentation/bloc/education/education_cubit/education_cubit.dart';
import 'package:agriculture/presentation/bloc/education/education_cubit/education_state.dart';
import 'package:agriculture/presentation/widgets/education/categories_filter_bar.dart';
import 'package:agriculture/presentation/widgets/education/course_card.dart';
import 'package:agriculture/presentation/widgets/shared/custom_loading_animation.dart';
import 'package:agriculture/routing/route_constants.dart';

/// صفحة الدورات التدريبية مع نظام التصنيفات
///
/// تعرض جميع الدورات مع إمكانية التصفية حسب التصنيف
class CoursesPage extends StatefulWidget {
  const CoursesPage({super.key});

  @override
  State<CoursesPage> createState() => _CoursesPageState();
}

class _CoursesPageState extends State<CoursesPage> with RouteAware {
  /// التصنيف المحدد حالياً
  String _selectedCategory = EducationCategoriesConstants.allCourses;

  @override
  void initState() {
    super.initState();
    // تحميل الدورات عند بدء الصفحة
    _loadCourses();
  }

  @override
  void didPopNext() {
    // يتم استدعاؤها عند العودة من صفحة أخرى
    super.didPopNext();
    print('🔄 العودة إلى صفحة الدورات - إعادة تحميل البيانات');
    _refreshData();
  }

  /// إعادة تحديث البيانات مع إعادة تعيين الحالة
  void _refreshData() {
    // إعادة تعيين التصنيف المحدد
    setState(() {
      _selectedCategory = EducationCategoriesConstants.allCourses;
    });

    // إعادة تحميل الدورات
    _loadCourses();
  }

  /// تحميل الدورات من Firebase
  void _loadCourses() {
    print('🔄 تحميل الدورات من Firebase...');
    final currentState = context.read<EducationCubit>().state;
    print('📊 الحالة الحالية قبل التحميل: ${currentState.runtimeType}');

    // إعادة تحميل الدورات دائماً لضمان البيانات الحديثة
    context.read<EducationCubit>().loadCourses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الدورات التدريبية',
          style: TextStyle(
            fontFamily: AssetsFonts.cairo,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AssetsColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // شريط تصفية التصنيفات
          BlocBuilder<EducationCubit, EducationState>(
            builder: (context, state) {
              return CategoriesFilterBar(
                selectedCategory: _selectedCategory,
                onCategoryChanged: (category) {
                  print('🎯 تم اختيار التصنيف: "$category"');
                  setState(() {
                    _selectedCategory = category;
                  });
                },
                categoriesCount: _getCategoriesCount(state),
              );
            },
          ),

          // قائمة الدورات
          Expanded(
            child: BlocConsumer<EducationCubit, EducationState>(
              listener: (context, state) {
                // معالجة الأخطاء
                if (state is EducationError) {
                  print('❌ خطأ في تحميل الدورات: ${state.message}');
                }

                // تأكيد نجاح التحميل
                if (state is EducationCoursesLoaded) {
                  print('✅ تم تحميل ${state.courses.length} دورة بنجاح');
                }
              },
              builder: (context, state) {
                print('📊 حالة EducationCubit: ${state.runtimeType}');

                if (state is EducationLoading) {
                  return const Center(
                    child: CustomLoadingAnimation(),
                  );
                }

                if (state is EducationError) {
                  return _buildErrorState(
                    state.message,
                    () => _loadCourses(),
                  );
                }

                if (state is EducationCoursesLoaded) {
                  // التحقق من وجود دورات
                  if (state.courses.isEmpty) {
                    print('⚠️ لا توجد دورات في قاعدة البيانات');
                    return _buildEmptyState();
                  }
                  return _buildCoursesContent(state.courses);
                }

                // حالة افتراضية - تحميل الدورات
                print('🔄 حالة غير معروفة - إعادة تحميل الدورات');
                Future.delayed(Duration.zero, () => _loadCourses());
                return const Center(
                  child: CustomLoadingAnimation(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الدورات
  Widget _buildCoursesContent(List<CourseModel> allCourses) {
    print('🔍 === تشخيص شامل للدورات ===');
    print('🎯 التصنيف المطلوب: "$_selectedCategory"');
    print('📊 إجمالي الدورات: ${allCourses.length}');

    // طباعة تفاصيل جميع الدورات
    for (final course in allCourses) {
      final isYemeni = course.id.startsWith('ym-');
      print('📖 دورة: "${course.title}"');
      print('   🆔 المعرف: "${course.id}"');
      print('   📂 التصنيف: "${course.categoryId}"');
      print('   🏷️ النوع: ${isYemeni ? "يمنية" : "Firebase"}');
      print('   ✅ يطابق التصنيف: ${_matchesCategory(course, _selectedCategory)}');
      print('   ---');
    }

    // تصفية الدورات حسب التصنيف المحدد
    List<CourseModel> filteredCourses;

    if (_selectedCategory == EducationCategoriesConstants.allCourses) {
      filteredCourses = allCourses;
      final yemeniCount = allCourses.where((course) => course.id.startsWith('ym-')).length;
      final firebaseCount = allCourses.where((course) => !course.id.startsWith('ym-')).length;
      print('📋 عرض جميع الدورات: ${filteredCourses.length} (يمنية: $yemeniCount، Firebase: $firebaseCount)');
    } else {
      filteredCourses = allCourses
          .where((course) => _matchesCategory(course, _selectedCategory))
          .toList();
      print('🎯 الدورات المفلترة للتصنيف "$_selectedCategory": ${filteredCourses.length} (دورات Firebase فقط)');

      // طباعة تفاصيل الدورات المفلترة
      for (final course in filteredCourses) {
        print('   📖 "${course.title}" - التصنيف: "${course.categoryId}"');
      }
    }

    if (filteredCourses.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التصنيف المحدد (إذا لم يكن "الكل")
          if (_selectedCategory != EducationCategoriesConstants.allCourses)
            _buildCategoryHeader(_selectedCategory),

          if (_selectedCategory != EducationCategoriesConstants.allCourses)
            const SizedBox(height: 16),

          // قائمة الدورات المفلترة
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: filteredCourses.length,
            itemBuilder: (context, index) {
              final course = filteredCourses[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: CourseCard(
                  course: course,
                  onTap: () async {
                    print('🎯 النقر على الدورة: "${course.title}"');

                    // الانتقال إلى تفاصيل الدورة
                    await Navigator.pushNamed(
                      context,
                      RouteConstants.courseDetails,
                      arguments: {'courseId': course.id},
                    );

                    // إعادة تحديث البيانات عند العودة
                    print('🔄 العودة من تفاصيل الدورة - إعادة تحديث البيانات');
                    if (mounted) {
                      _refreshData();
                    }
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// التحقق من تطابق الدورة مع التصنيف
  bool _matchesCategory(CourseModel course, String selectedCategory) {
    // تبويب "جميع الدورات" - يعرض كل شيء
    if (selectedCategory == EducationCategoriesConstants.allCourses) {
      return true;
    }

    // التبويبات الأخرى - دورات Firebase فقط
    // الدورات المحلية اليمنية لا تظهر في التبويبات المحددة
    if (course.id.startsWith('ym-')) {
      return false; // لا تظهر الدورات المحلية في التصنيفات المحددة
    }

    // دورات Firebase - مقارنة مباشرة مع التصنيف
    return course.categoryId == selectedCategory;
  }

  /// بناء عنوان التصنيف المحدد
  Widget _buildCategoryHeader(String categoryId) {
    final categoryName = EducationCategoriesConstants.getCategoryName(categoryId);
    final categoryDescription = EducationCategoriesConstants.getCategoryDescription(categoryId);
    final categoryColor = Color(EducationCategoriesConstants.getCategoryColor(categoryId));
    final iconCode = EducationCategoriesConstants.getCategoryIconCode(categoryId);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            categoryColor.withValues(alpha: 0.8),
            categoryColor,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: categoryColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة التصنيف
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AssetsColors.kWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              IconData(iconCode, fontFamily: 'MaterialIcons'),
              size: 32,
              color: AssetsColors.kWhite,
            ),
          ),

          const SizedBox(width: 16),

          // معلومات التصنيف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  categoryName,
                  style: TextStyles.of(context).headlineMedium(
                    fontSize: 20,
                    fontFamily: AssetsFonts.cairo,
                    fontWeight: FontWeight.bold,
                    color: AssetsColors.kWhite,
                  ),
                ),
                if (categoryDescription.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    categoryDescription,
                    style: TextStyles.of(context).bodyMedium(
                      fontSize: 14,
                      fontFamily: AssetsFonts.cairo,
                      color: AssetsColors.kWhite.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: AssetsColors.kGrey70,
            ),
            const SizedBox(height: 16),
            Text(
              'لم يتم إضافة دورات بعد لهذا القسم',
              style: TextStyles.of(context).headlineMedium(
                fontSize: 18,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة دورات جديدة قريباً',
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String message, VoidCallback onRetry) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AssetsColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: TextStyles.of(context).headlineMedium(
                fontSize: 18,
                fontFamily: AssetsFonts.cairo,
                fontWeight: FontWeight.bold,
                color: AssetsColors.kGrey100,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyles.of(context).bodyMedium(
                fontSize: 14,
                fontFamily: AssetsFonts.cairo,
                color: AssetsColors.kGrey70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AssetsColors.primary,
                foregroundColor: AssetsColors.kWhite,
              ),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على عدد الدورات لكل تصنيف
  Map<String, int> _getCategoriesCount(EducationState state) {
    final Map<String, int> counts = {};

    if (state is EducationCoursesLoaded) {
      final allCourses = state.courses;

      // حساب العدد الإجمالي (جميع الدورات: محلية + Firebase)
      counts[EducationCategoriesConstants.allCourses] = allCourses.length;

      // حساب عدد الدورات لكل تصنيف (دورات Firebase فقط)
      for (final categoryId in EducationCategoriesConstants.displayCategories) {
        final count = allCourses
            .where((course) => !course.id.startsWith('ym-')) // دورات Firebase فقط
            .where((course) => course.categoryId == categoryId)
            .length;
        counts[categoryId] = count;

        // طباعة معلومات التصحيح
        if (count > 0) {
          print('📊 التصنيف "$categoryId": $count دورة Firebase');
        }
      }
    }

    return counts;
  }
}

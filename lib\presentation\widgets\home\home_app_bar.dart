
import 'package:flutter/services.dart';


import '../../../imports.dart';
import '../shared/user_profile_avatar.dart';

/// مكون الأبار الرئيسي لصفحة الهوم
///
/// يحتوي على شعار التطبيق وأيقونة الإشعارات وصورة المستخدم
/// ويمكن تخصيصه حسب الحاجة
class HomeAppBar extends StatefulWidget implements PreferredSizeWidget {
  /// إنشاء مكون الأبار الرئيسي
  const HomeAppBar({super.key});

  @override
  State<HomeAppBar> createState() => _HomeAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _HomeAppBarState extends State<HomeAppBar> {
  @override
  void initState() {
    super.initState();
    // تحميل بيانات الطقس عند بناء الويدجت
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<WeatherCubit>().loadCurrentWeather();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: AssetsColors.dufaultGreencolor,
        statusBarIconBrightness: Brightness.light,
      ),
      leadingWidth: 120,
      backgroundColor: Colors.tealAccent.shade700,
      leading: _buildWeatherWidget(),
      title: _buildAppTitle(context),
      actions: _buildActions(context),
    );
  }

  /// بناء مكون الطقس في الجانب الأيسر
  Widget _buildWeatherWidget() {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Container(
        clipBehavior: Clip.antiAlias,
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: BlocBuilder<WeatherCubit, WeatherState>(
            builder: (context, state) {
              // تحديد البيانات المراد عرضها
              String temperature = '--';
              String description = 'جاري التحميل...';
              IconData weatherIcon = Icons.cloud_outlined;

              if (state is WeatherLoaded) {
                // استخراج درجة الحرارة
                if (state.currentWeather.main?.temp != null) {
                  final temp = (state.currentWeather.main!.temp as num).round();
                  temperature = '$temp°';
                }

                // استخراج وصف الطقس
                if (state.currentWeather.weather != null &&
                    state.currentWeather.weather!.isNotEmpty) {
                  description =
                      state.currentWeather.weather!.first.description ??
                      'غير محدد';
                }

                // تحديد الأيقونة بناءً على حالة الطقس
                weatherIcon = _getWeatherIcon(
                  state.currentWeather.weather?.first.main,
                );
              } else if (state is WeatherPartiallyLoaded &&
                  state.currentWeather != null) {
                // استخراج درجة الحرارة
                if (state.currentWeather!.main?.temp != null) {
                  final temp =
                      (state.currentWeather!.main!.temp as num).round();
                  temperature = '$temp°';
                }

                // استخراج وصف الطقس
                if (state.currentWeather!.weather != null &&
                    state.currentWeather!.weather!.isNotEmpty) {
                  description =
                      state.currentWeather!.weather!.first.description ??
                      'غير محدد';
                }

                // تحديد الأيقونة بناءً على حالة الطقس
                weatherIcon = _getWeatherIcon(
                  state.currentWeather!.weather?.first.main,
                );
              } else if (state is WeatherError) {
                description = 'خطأ في البيانات';
                weatherIcon = Icons.error_outline;
              }

              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(weatherIcon, size: 20, color: Colors.black54),
                  const SizedBox(width: 8.0),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        temperature,
                        style: const TextStyle(
                          fontSize: 13.0,
                          color: Colors.black,
                          height: .7,
                          fontWeight: FontWeight.bold,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        description,
                        style: TextStyle(
                          fontFamily: AssetsFonts.sultan,
                          fontSize: 11.0,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                          overflow: TextOverflow.ellipsis,
                        ),
                        maxLines: 1,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  /// بناء عنوان التطبيق
  Widget _buildAppTitle(BuildContext context) {
    return Text(
      'الارض الطيبة',
      style: TextStyles.of(context).titleLarge().copyWith(
        color: Colors.white,
        fontFamily: AssetsFonts.sultan,
        fontSize: 22.0,
      ),
    );
  }

  /// بناء الإجراءات في الجانب الأيمن (الإشعارات وصورة المستخدم)
  List<Widget> _buildActions(BuildContext context) {
    return [
      // أيقونة الإشعارات
      IconButton(
        onPressed: () {
          // يمكن إضافة التنقل إلى صفحة الإشعارات هنا
        },
        icon: Icon(Icons.notifications_sharp, size: 30, color: Colors.white),
      ),

      // صورة المستخدم
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: UserProfileAvatar(
          onTap: () {
            // الانتقال إلى صفحة الملف الشخصي
            Navigator.pushNamed(context, RouteConstants.profile);
          },
        ),
      ),
    ];
  }

  /// تحديد أيقونة الطقس بناءً على حالة الطقس
  IconData _getWeatherIcon(String? weatherMain) {
    if (weatherMain == null) return Icons.cloud_outlined;

    switch (weatherMain.toLowerCase()) {
      case 'clear':
      case 'صافي':
        return Icons.wb_sunny_outlined;
      case 'clouds':
      case 'غائم':
      case 'غائم جزئي':
        return Icons.cloud_outlined;
      case 'rain':
      case 'مطر':
        return Icons.grain_outlined;
      case 'drizzle':
      case 'رذاذ':
        return Icons.grain_outlined;
      case 'thunderstorm':
      case 'عاصف':
        return Icons.flash_on_outlined;
      case 'snow':
      case 'ثلج':
        return Icons.ac_unit_outlined;
      case 'mist':
      case 'fog':
      case 'ضباب':
        return Icons.foggy;
      default:
        return Icons.cloud_outlined;
    }
  }
}

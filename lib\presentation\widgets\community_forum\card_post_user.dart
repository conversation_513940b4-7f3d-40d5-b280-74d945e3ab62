import 'package:agriculture/core/constants/assets_colors.dart';
import 'package:agriculture/core/utils/extensions/date_time_extensions.dart';
import 'package:agriculture/data/models/community_forum/post_model.dart';
import 'package:agriculture/presentation/bloc/community_forum/posts_cubit.dart';
import 'package:agriculture/presentation/pages/community_forum/comments_page.dart';
import 'package:agriculture/presentation/widgets/community_forum/comment_and_like.dart';
import 'package:agriculture/presentation/widgets/community_forum/comment_preview.dart';
import 'package:agriculture/presentation/widgets/community_forum/post_media.dart';
import 'package:agriculture/presentation/widgets/shared/cachd_net_image.dart';
import 'package:agriculture/presentation/widgets/shared/dialogs/delete_post_dialog.dart';
import 'package:agriculture/presentation/widgets/shared/user_profile_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


class CardPostUser extends StatefulWidget {
  /// نموذج المنشور
  final PostModel post;

  /// معرف المستخدم الحالي
  final String userId;

  /// دالة يتم استدعاؤها عند النقر على زر الإعجاب
  final VoidCallback? onLike;

  /// دالة يتم استدعاؤها عند النقر على زر إلغاء الإعجاب
  final VoidCallback? onUnlike;

  /// دالة يتم استدعاؤها عند النقر على زر التعليق
  final VoidCallback? onComment;

  /// دالة يتم استدعاؤها عند مشاهدة المنشور
  final VoidCallback? onView;

  /// ما إذا كان المستخدم قد أعجب بالمنشور
  final bool isLiked;

  const CardPostUser({
    super.key,
    required this.post,
    required this.userId,
    this.onLike,
    this.onUnlike,
    this.onComment,
    this.onView,
    this.isLiked = false,
  });

  @override
  State<CardPostUser> createState() => _CardPostUserState();
}

class _CardPostUserState extends State<CardPostUser> {
  @override
  void initState() {
    super.initState();
    // زيادة عدد المشاهدات مرة واحدة فقط عند بناء المكون
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onView?.call();
    });
  }

  @override
  Widget build(BuildContext context) {
    // تنسيق تاريخ المنشور باستخدام امتدادات DateTime المخصصة
    final DateTime postDate = DateTime.parse(widget.post.createdAt);
    final String formattedDate = postDate.dateTime;

    return Card(
      margin: const EdgeInsets.only(bottom: 10.0),
      elevation: 7.0,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الجزء العلوي: صورة المستخدم واسمه وتاريخ النشر
            Row(
              children: [
                // صورة المستخدم
                _buildUserAvatar(context, widget.post.userImage),
                const SizedBox(width: 10),

                // اسم المستخدم وتاريخ النشر
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(
                        widget.post.userName,
                        maxLines: 1,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 14.0,
                          color: Colors.black,
                          height: 1.2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        formattedDate,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),

                // زر المزيد من الخيارات
                if (widget.post.userId ==
                    widget.userId) // إظهار الزر فقط لمالك المنشور
                  IconButton(
                    onPressed: () {
                      // عرض قائمة الخيارات (تعديل، حذف)
                      _showOptionsMenu(context);
                    },
                    icon: const Icon(Icons.more_horiz, size: 20.0),
                  ),
              ],
            ),
            const SizedBox(height: 10),

            // نص المنشور
            if (widget.post.text != null && widget.post.text!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 10.0),
                child: Text(
                  widget.post.text!,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontSize: 16.0,
                    color: Colors.black,
                    height: 1.2,
                  ),
                ),
              ),

            // وسائط المنشور (صور أو فيديو)
            if ((widget.post.images != null &&
                    widget.post.images!.isNotEmpty) ||
                widget.post.video != null)
              PostMedia(images: widget.post.images, video: widget.post.video),

            // معلومات المشاهدات والوسوم
            _buildPostStats(),

            // عرض موجز للتعليقات
            if (widget.post.commentsCount > 0)
              CommentPreview(
                postId: widget.post.id,
                commentsCount: widget.post.commentsCount,
                previewCount: 2,
                onViewMore: () {
                  // زيادة عدد المشاهدات عند فتح صفحة التعليقات
                  widget.onView?.call();

                  // الانتقال إلى صفحة التعليقات
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => CommentsPage(post: widget.post),
                    ),
                  );
                },
                onAddComment: () {
                  // زيادة عدد المشاهدات عند فتح صفحة التعليقات
                  widget.onView?.call();

                  // الانتقال إلى صفحة التعليقات
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => CommentsPage(post: widget.post),
                    ),
                  );
                },
              ),

            // مكون الإعجابات والتعليقات والمشاركة
            CommentAndLike(
              likesCount: widget.post.likesCount,
              commentsCount: widget.post.commentsCount,
              isLiked: widget.isLiked,
              onLike: widget.isLiked ? widget.onUnlike : widget.onLike,
              onComment: () {
                // زيادة عدد المشاهدات عند فتح التعليقات
                widget.onView?.call();

                // الانتقال إلى صفحة التعليقات
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => CommentsPage(post: widget.post),
                  ),
                );
              },
              onShare: () => _sharePost(context),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض قائمة خيارات المنشور
  void _showOptionsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('تعديل المنشور'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: فتح صفحة تعديل المنشور
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'حذف المنشور',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // استخدام حوار حذف المنشور المشترك
                    DeletePostDialog.show(
                      context: context,
                      postId: widget.post.id,
                      userId: widget.userId,
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }

  /// مشاركة المنشور
  void _sharePost(BuildContext context) {
    // زيادة عدد المشاهدات عند المشاركة
    widget.onView?.call();

    // إنشاء نص المشاركة

    // مشاركة المنشور
   
    // SharePlus.instance.share(
    //   '$shareText$postContent$hashtagsText\n\nشارك من تطبيق الزراعة',
    //   subject: 'مشاركة منشور من تطبيق الزراعة',
    // );

    // عرض رسالة مؤقتة حتى يتم تحديث المكتبة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المشاركة ستكون متاحة قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// بناء معلومات المشاهدات والوسوم
  Widget _buildPostStats() {
    // ملاحظة: تم نقل زيادة عدد المشاهدات إلى initState لمنع الاستدعاء المتكرر

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عدد المشاهدات
        if (widget.post.viewsCount > 0)
          Padding(
            padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
            child: Text(
              '${widget.post.viewsCount} مشاهدة',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ),

        // الوسوم (هاشتاج)
        if (widget.post.hashtags.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  widget.post.hashtags
                      .map((tag) => _buildHashtagChip(tag))
                      .toList(),
            ),
          ),
      ],
    );
  }

  /// بناء شريحة الوسم (هاشتاج)
  Widget _buildHashtagChip(String tag) {
    return InkWell(
      onTap: () {
        // البحث عن المنشورات بنفس الوسم
        context.read<PostsCubit>().searchPosts('#$tag');
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: AssetsColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: AssetsColors.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Text(
          '#$tag',
          style: TextStyle(
            color: AssetsColors.primary,
            fontSize: 13,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// بناء صورة المستخدم باستخدام UserProfileAvatar
  Widget _buildUserAvatar(BuildContext context, String userImage) {
    return UserProfileAvatar(
      radius: 25.0,
      backgroundColor: AssetsColors.primary.withValues(alpha: 0.1),
      iconColor: AssetsColors.primary,
      // استخدام صورة المستخدم إذا كانت متوفرة
      customWidget:
          userImage.isNotEmpty
              ? ClipOval(
                child: CachedNetImage(
                  imageUrl: userImage,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  lazyLoad: true,
                  memCacheWidth:
                      100, // حجم أصغر للتخزين المؤقت لصور الملف الشخصي
                  memCacheHeight: 100,
                  enableFullScreen: false,
                ),
              )
              : null,
      // الانتقال إلى صفحة الملف الشخصي للمستخدم عند النقر
      onTap: () {
        // TODO: الانتقال إلى صفحة الملف الشخصي للمستخدم
      },
    );
  }
}

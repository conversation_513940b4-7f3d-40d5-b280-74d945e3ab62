import 'package:animate_do/animate_do.dart';

import '../../../../imports.dart';

/// رأس صفحات المصادقة
///
/// يعرض هذا المكون رأس صفحات المصادقة مع صورة الخلفية والعنوان والتأثيرات.
class AuthHeader extends StatelessWidget {
  /// عنوان الصفحة
  final String title;

  /// منشئ رأس صفحات المصادقة
  const AuthHeader({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AssetsLoginImage.tree),
          fit: BoxFit.fill,
        ),
      ),
      child: Stack(
        children: <Widget>[
          // عنصر الضوء الأول
          _buildPositionedImage(
            left: 30,
            width: 70,
            height: 150,
            duration: const Duration(seconds: 1),
            imagePath: AssetsLoginImage.light1,
          ),

          // عنصر الضوء الثاني
          _buildPositionedImage(
            left: 140,
            width: 60,
            height: 120,
            duration: const Duration(milliseconds: 1200),
            imagePath: AssetsLoginImage.light2,
          ),

          // عنصر الساعة
          _buildPositionedImage(
            right: 0,
            top: 0,
            width: 60,
            height: 120,
            duration: const Duration(milliseconds: 1300),
            imagePath: AssetsLoginImage.clock,
          ),

          // عنوان الصفحة
          Positioned(
            child: FadeInUp(
              duration: const Duration(milliseconds: 1600),
              child: Container(
                margin: const EdgeInsets.only(top: 50),
                child: Center(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: Dimensions.fontSizeHeadline,
                      fontWeight: FontWeight.bold,
                      fontFamily: AssetsFonts.messiri,
                    ),
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// بناء عنصر صورة موضعي في رأس الصفحة
  Widget _buildPositionedImage({
    double? left,
    double? right,
    double? top,
    double? bottom,
    required double width,
    required double height,
    required Duration duration,
    required String imagePath,
  }) {
    return Positioned(
      left: left,
      right: right,
      top: top,
      bottom: bottom,
      width: width,
      height: height,
      child: FadeInUp(
        duration: duration,
        child: Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(imagePath),
            ),
          ),
        ),
      ),
    );
  }
}
